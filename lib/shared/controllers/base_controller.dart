import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/routes/other.dart';
import 'package:ivent_app/shared/controllers/base_search_bar_controller.dart';
import 'package:ivent_app/shared/controllers/shared_state.dart';

class BaseController<T extends SharedState> extends GetxController {
  final AuthService authService;
  final T state;

  BaseController(this.authService, this.state);

  SessionUser get sessionUser {
    final user = authService.sessionUser;
    if (user != null) return user;
    throw Exception('SessionUser is null. User must be logged in to access this controller.');
  }

  bool get isLoggedIn => authService.isLoggedIn;

  @mustCallSuper
  void initController() {}

  @mustCallSuper
  void closeController() {}

  @override
  void onInit() {
    super.onInit();
    initController();
  }

  @override
  void onClose() {
    closeController();
    super.onClose();
  }

  void goToSomethingWentWrongPage() => Get.toNamed(OtherRoutes.SOMETHING_WENT_WRONG);

  void handleError(dynamic e, {StackTrace? st, String? customMessage}) {
    final message = customMessage ?? 'Bir hata oluştu. Lütfen tekrar deneyin.';
    Get.defaultDialog(
      title: 'Hata',
      middleText: message,
      backgroundColor: AppColors.primary,
      titleStyle: AppTextStyles.size20Bold.copyWith(color: AppColors.white),
      middleTextStyle: AppTextStyles.size16Regular.copyWith(color: AppColors.white),
      barrierDismissible: true,
      radius: AppDimensions.radiusS,
      confirm: ElevatedButton(
        style: ElevatedButton.styleFrom(backgroundColor: AppColors.white),
        onPressed: () => Get.back(), // close the dialog
        child: Text('Tamam', style: AppTextStyles.size16Bold.copyWith(color: AppColors.primary)),
      ),
    );
    debugPrint('Exception: $e');
  }

  Future<void> runWithLoading(
    Future<void> Function() action, {
    String? loadingTag,
    String? errorMessage,
    VoidCallback? onError,
    VoidCallback? onFinally,
    VoidCallback? onSuccess,
  }) async {
    final tag = loadingTag ?? action.toString();
    if (state.isLoading(tag)) return;
    try {
      state.setLoading(true, tag);
      await action();
      onSuccess?.call();
    } catch (e, st) {
      handleError(e, st: st, customMessage: errorMessage);
      onError?.call();
    } finally {
      state.setLoading(false, tag);
      onFinally?.call();
    }
  }

  bool isLoading([String? tag]) => state.isLoading(tag);
}

abstract class BaseControllerWithSearch<T extends SharedState> extends BaseController<T> {
  BaseControllerWithSearch(AuthService authService, T state) : super(authService, state);

  late final BaseSearchBarController baseSearchBarController;

  TextEditingController get textEditingController => baseSearchBarController.textEditingController;
  String get searchText => baseSearchBarController.text;
  bool get isSearching => baseSearchBarController.isSearching;
  bool get isQueryEmpty => searchText.isEmpty;

  // Must be implemented by the subclass
  bool get isResultsEmpty;
  Future<void> onSearch([String? query]);

  @mustCallSuper
  @override
  Future<void> initController() async {
    super.initController();
    baseSearchBarController = Get.put(BaseSearchBarController((q) => onSearch(q)), tag: runtimeType.toString());
  }

  @mustCallSuper
  @override
  void closeController() {
    Get.delete<BaseSearchBarController>(tag: runtimeType.toString());
    super.closeController();
  }
}
