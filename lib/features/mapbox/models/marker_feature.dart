/// Represents a map marker feature with GeoJSON structure
///
/// Used for displaying iVent markers on the map with selection state
/// and coordinate information. Follows GeoJSON Feature specification.
class MarkerFeature {
  final String type = 'Feature';
  final String id;
  final MarkerFeatureProperties properties;
  final MarkerFeatureGeometry geometry;

  MarkerFeature({
    required this.id,
    required this.properties,
    required this.geometry,
  });

  /// Creates a MarkerFeature from JSON data
  factory MarkerFeature.fromJson(Map<String, dynamic> json) {
    return MarkerFeature(
      id: json['id'] as String,
      properties: MarkerFeatureProperties.fromJson(json['properties']),
      geometry: MarkerFeatureGeometry.fromJson(json['geometry']),
    );
  }

  /// Converts the MarkerFeature to JSON format
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'id': id,
      'properties': properties.toJson(),
      'geometry': geometry.toJson(),
    };
  }

  @override
  String toString() => 'MarkerFeature(type: $type, id: $id, properties: $properties, geometry: $geometry)';
}

/// Properties for a marker feature including selection state
class MarkerFeatureProperties {
  bool isSelected;

  MarkerFeatureProperties({
    required this.isSelected,
  });

  /// Creates properties from JSON data
  factory MarkerFeatureProperties.fromJson(Map<String, dynamic> json) {
    return MarkerFeatureProperties(
      isSelected: json['isSelected'] as bool,
    );
  }

  /// Converts properties to JSON format
  Map<String, dynamic> toJson() {
    return {
      'isSelected': isSelected,
    };
  }

  @override
  String toString() => 'MarkerFeatureProperties(isSelected: $isSelected)';
}

/// Geometry information for a marker feature
///
/// Contains coordinate data in [longitude, latitude] format
/// following GeoJSON Point geometry specification.
class MarkerFeatureGeometry {
  final String type = 'Point';

  /// Coordinates in [longitude, latitude] format
  final List<double> coordinates;

  MarkerFeatureGeometry({
    required this.coordinates,
  });

  /// Creates geometry from JSON data
  factory MarkerFeatureGeometry.fromJson(Map<String, dynamic> json) {
    return MarkerFeatureGeometry(
      coordinates: List<double>.from(json['coordinates']),
    );
  }

  /// Converts geometry to JSON format
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'coordinates': coordinates,
    };
  }

  @override
  String toString() => 'MarkerFeatureGeometry(type: $type, coordinates: $coordinates)';
}
