import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

/// Represents map bounds with calculated ranges and center points
///
/// Used for defining visible map area and performing spatial calculations.
/// Automatically calculates range and center values from boundary coordinates.
class MapBounds {
  // Boundary coordinates
  final double latStart;
  final double latEnd;
  final double lngStart;
  final double lngEnd;

  // Calculated values
  final double latRange;
  final double lngRange;
  final double latCenter;
  final double lngCenter;

  /// Creates map bounds with automatic calculation of ranges and centers
  MapBounds({
    required this.latStart,
    required this.latEnd,
    required this.lngStart,
    required this.lngEnd,
  })  : latRange = latEnd - latStart,
        lngRange = lngEnd - lngStart,
        latCenter = (latStart + latEnd) / 2,
        lngCenter = (lngStart + lngEnd) / 2;

  /// Creates map bounds from Mapbox CoordinateBounds
  factory MapBounds.fromCoordinateBounds(CoordinateBounds bounds) {
    return MapBounds(
      latStart: bounds.southwest.coordinates.lat.toDouble(),
      latEnd: bounds.northeast.coordinates.lat.toDouble(),
      lngStart: bounds.southwest.coordinates.lng.toDouble(),
      lngEnd: bounds.northeast.coordinates.lng.toDouble(),
    );
  }

  @override
  String toString() {
    return 'MapBounds(latStart: $latStart, latEnd: $latEnd, lngStart: $lngStart, lngEnd: $lngEnd)';
  }
}
