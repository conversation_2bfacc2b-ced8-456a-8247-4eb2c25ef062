import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/widgets/lists/ia_selection_list.dart';

class IventDetailInviteMorePeople extends StatelessWidget {
  final TextEditingController _searchBarController = TextEditingController();
  final String iventId;

  IventDetailInviteMorePeople(this.iventId, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final IventDetailsController _controller = Get.find(tag: iventId);

    return Obx(() {
      final invitableGroups = _controller.invitationsController.invitableGroups;
      final invitableUsers = _controller.invitationsController.invitableUsers;
      if (invitableGroups == null || invitableUsers == null) {
        return const Center(child: CircularProgressIndicator());
      }
      return IaScaffold.search(
        title: 'Daha Fazla Kişi Çağır',
        textEditingController: _searchBarController,
        body: SingleChildScrollView(
          child: Column(
            children: [
              if (invitableGroups.groupCount != 0) groupSelectionList(invitableGroups, _controller),
              if (invitableUsers.friendCount != 0) userSelectionList(invitableUsers, _controller),
              const SizedBox(height: 100),
            ],
          ),
        ),
        floatingActionButton: IaFloatingActionButton(
          onPressed: () => _controller.invitationsController.inviteFriends(context),
          isEnabled: _controller.invitationsController.selectedFriendCount != 0,
          text: _controller.invitationsController.selectedFriendCount != 0
              ? '${_controller.invitationsController.selectedFriendCount} Kişiyi Çağır'
              : 'Birden Fazla Seçebilirsin',
        ),
      );
    });
  }
}
