import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/utils/list_utils.dart';
import 'package:ivent_app/core/utils/share_utils.dart';
import 'package:ivent_app/core/widgets/composite/buttons/shared_buttons.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_linked_avatars.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_text_button.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_divider.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ia_ivent_thumbnail.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ivent_detail_buttons.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/widgets/tags/ia_ivent_tags_scroll.dart';
import 'package:ivent_app/features/ivent_detail/widgets/tiles/ia_contact_tile.dart';

/// iVent detail page displaying comprehensive information about an iVent
///
/// Shows iVent details including thumbnail, description, tags, collaborators,
/// participants, and action buttons. The page adapts its content based on
/// the user's relationship to the iVent (creator, participant, or visitor).
///
/// The page uses reactive state management through GetX and follows the
/// project's established patterns for UI composition and styling.

class IventDetail extends StatefulWidget {
  /// The unique identifier for the iVent
  final String iventId;

  const IventDetail(this.iventId, {Key? key}) : super(key: key);

  @override
  State<IventDetail> createState() => _IventDetailState();
}

class _IventDetailState extends State<IventDetail> {
  // Controllers
  late final IventDetailsController _controller;

  // State
  bool isDescriptionExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = Get.find(tag: widget.iventId);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final pageContent = _controller.iventInfoController.iventPage;

      return IaScaffold.noSearch(
        showDivider: false,
        title: 'iVent Detay',
        trailing: _buildMembersButton(),
        body: pageContent == null
            ? const IaLoadingIndicator()
            : Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: _buildContent(pageContent),
                    ),
                  ),
                  _buildBottomBar(pageContent),
                ],
              ),
      );
    });
  }

  // Widget builders

  /// Shares the current ivent using the native share dialog
  void _shareIvent(GetIventPageByIventIdReturn pageContent) {
    ShareUtils.shareIventWithLoading(
      iventId: pageContent.iventId,
      iventName: pageContent.iventName,
      description: pageContent.description,
      thumbnailUrl: pageContent.thumbnailUrl,
      context: context,
    );
  }

  /// Builds the members button in the app bar
  Widget _buildMembersButton() {
    return IaTextButton(
      text: 'Üyeler',
      textStyle: AppTextStyles.size16Regular,
    );
  }

  /// Builds the main content of the page
  Widget _buildContent(GetIventPageByIventIdReturn pageContent) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: AppDimensions.padding12),
        _buildIventThumbnail(pageContent),
        _buildTagsSection(pageContent),
        _buildCreatorAndParticipantsSection(pageContent),
        _buildDescription(pageContent),
      ],
    );
  }

  /// Builds the iVent thumbnail section
  Widget _buildIventThumbnail(GetIventPageByIventIdReturn pageContent) {
    return IaIventThumbnail.big(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      iventId: pageContent.iventId,
      iventName: pageContent.iventName,
      locationId: pageContent.locationId,
      locationName: pageContent.locationName,
      date: pageContent.dates.map((date) => DateTime.parse(date)).toList(),
      thumbnailUrl: pageContent.thumbnailUrl,
      isFavorited: _controller.iventInfoController.isFavorited,
      onFavorite: _controller.iventInfoController.toggleIventFavorite,
      onShare: () => _shareIvent(pageContent),
      favoriteCount: pageContent.favoriteCount,
    );
  }

  /// Builds the tags section if tags exist
  Widget _buildTagsSection(GetIventPageByIventIdReturn pageContent) {
    if (pageContent.tagNames.isEmpty) {
      return const SizedBox.shrink();
    }

    return IaIventTagsScroll(
      margin: const EdgeInsets.only(top: AppDimensions.padding12),
      tags: pageContent.tagNames,
    );
  }

  /// Builds the creator and participants section
  Widget _buildCreatorAndParticipantsSection(GetIventPageByIventIdReturn pageContent) {
    return IaListTile.withImageUrl(
      margin: const EdgeInsets.all(AppDimensions.padding20),
      onTap: () => _controller.goToCollabsPage(),
      avatarUrl: pageContent.creatorImageUrl,
      title: pageContent.creatorUsername,
      subtitle: _buildCollabsDetailsText(
        collabCount: pageContent.collabCount,
        collabNames: pageContent.collabNames,
      ),
      trailing: IaLinkedAvatars(
        maxWidth: Get.width * 0.3,
        onTap: () => _controller.goToParticipantsPage(
          pageContent.viewType,
          pageContent.memberCount,
        ),
        viewType: pageContent.viewType,
        memberAvatarUrls: pageContent.memberAvatarUrls,
        memberCount: pageContent.memberCount,
        memberNames: pageContent.memberFirstnames ?? [],
      ),
    );
  }

  String? _buildCollabsDetailsText({
    required int collabCount,
    required List<String> collabNames,
  }) {
    if (collabCount == 0)
      return null;
    else if (collabCount == 1)
      return '${collabNames[0]}';
    else if (collabCount == 2)
      return '${collabNames[0]}, ${collabNames[1]}';
    else
      return '${collabNames[0]}, ${collabNames[1]} ve ${collabCount - 2} Diğer Paydaş';
  }

  Widget _buildDescription(GetIventPageByIventIdReturn pageContent) {
    return pageContent.viewType != IventViewTypeEnum.created
        ? _buildNormalDescription(pageContent)
        : _buildCreatorDescription(pageContent);
  }

  Widget _buildNormalDescription(GetIventPageByIventIdReturn pageContent) {
    if (pageContent.description == null) return const SizedBox.shrink();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Açıklama', style: AppTextStyles.size16Bold),
          const SizedBox(height: AppDimensions.padding4),
          Text(
            pageContent.description!
                    .substring(0, isDescriptionExpanded ? null : min(500, pageContent.description!.length)) +
                (pageContent.description!.length > 500 ? (isDescriptionExpanded ? '' : '...') : ''),
            style: AppTextStyles.size16RegularTextSecondary,
            maxLines: null,
            overflow: TextOverflow.visible,
          ),
          if (pageContent.description!.length > 500)
            IaTextButton(
              margin: const EdgeInsets.only(top: AppDimensions.padding4),
              onPressed: () => setState(() => isDescriptionExpanded = !isDescriptionExpanded),
              text: isDescriptionExpanded ? 'Gizle' : 'Devamını Oku',
              textStyle: AppTextStyles.size16RegularPrimary,
            ),
          const SizedBox(height: AppDimensions.padding20),
        ],
      ),
    );
  }

  Widget _buildCreatorDescription(GetIventPageByIventIdReturn pageContent) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: GestureDetector(
        onTap: () {},
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Açıklama', style: AppTextStyles.size16Bold),
            const SizedBox(height: AppDimensions.padding8),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                const IaSvgIcon(iconPath: AppAssets.editPencilLine01, iconColor: AppColors.darkGrey),
                const SizedBox(width: AppDimensions.padding8),
                Expanded(
                  child: Text(
                    pageContent.description ?? '',
                    style: AppTextStyles.size16RegularTextSecondary,
                    maxLines: 1,
                    softWrap: false,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.padding20),
            _buildRegistrationTypes(pageContent),
          ],
        ),
      ),
    );
  }

  Widget _buildRegistrationTypes(GetIventPageByIventIdReturn pageContent) {
    if (pageContent.viewType != IventViewTypeEnum.created) return const SizedBox.shrink();
    final registrationTypes = [
      pageContent.googleFormsUrl,
      pageContent.instagramUsername,
      pageContent.whatsappUrl,
      pageContent.whatsappNumber,
      pageContent.callNumber,
      pageContent.websiteUrl,
    ];
    if (registrationTypes.where((var element) => element != null && element.isNotEmpty).isEmpty)
      return const SizedBox.shrink();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Kayıt Türleri', style: AppTextStyles.size16Bold),
        const SizedBox(height: AppDimensions.padding8),
        ...insertBetween(
          [
            if (pageContent.googleFormsUrl != null && pageContent.googleFormsUrl!.isNotEmpty)
              IaContactTile.googleForms(text: pageContent.googleFormsUrl!),
            if (pageContent.instagramUsername != null && pageContent.instagramUsername!.isNotEmpty)
              IaContactTile.instagram(text: pageContent.instagramUsername!),
            if (pageContent.whatsappUrl != null && pageContent.whatsappUrl!.isNotEmpty)
              IaContactTile.whatsappGroup(text: pageContent.whatsappUrl!),
            if (pageContent.whatsappNumber != null && pageContent.whatsappNumber!.isNotEmpty)
              IaContactTile.whatsappMessage(text: pageContent.whatsappNumber!),
            if (pageContent.callNumber != null && pageContent.callNumber!.isNotEmpty)
              IaContactTile.phoneCall(text: pageContent.callNumber!),
            if (pageContent.websiteUrl != null && pageContent.websiteUrl!.isNotEmpty)
              IaContactTile.website(text: pageContent.websiteUrl!),
          ],
          const SizedBox(height: AppDimensions.padding12),
        ),
        const SizedBox(height: AppDimensions.padding20),
      ],
    );
  }

  /// Builds the bottom action bar based on user's relationship to the iVent
  Widget _buildBottomBar(GetIventPageByIventIdReturn pageContent) {
    return Column(
      children: [
        const IaDivider(),
        Container(
          height: AppDimensions.bottomIventDetayBottomBarHeight,
          padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
          child: _buildBottomBarContent(pageContent),
        ),
      ],
    );
  }

  /// Builds the appropriate bottom bar content based on view type
  Widget _buildBottomBarContent(GetIventPageByIventIdReturn pageContent) {
    switch (pageContent.viewType) {
      case IventViewTypeEnum.joined:
        return _buildJoinedViewBottomBar(pageContent);
      case IventViewTypeEnum.created:
        return _buildCreatedViewBottomBar();
      default:
        return _buildOtherViewBottomBar();
    }
  }

  /// Builds bottom bar for joined users showing communication options
  Widget _buildJoinedViewBottomBar(GetIventPageByIventIdReturn pageContent) {
    return Row(
      children: insertBetween(
        [
          if (pageContent.googleFormsUrl != null) IventDetailButtons.iventDetailCommunicationGoogleForms(onTap: () {}),
          if (pageContent.instagramUsername != null) IventDetailButtons.iventDetailCommunicationInstagram(onTap: () {}),
          if (pageContent.whatsappUrl != null) IventDetailButtons.iventDetailCommunicationWhatsappGroup(onTap: () {}),
          if (pageContent.whatsappNumber != null) IventDetailButtons.iventDetailCommunicationWhatsappChat(onTap: () {}),
          if (pageContent.callNumber != null) IventDetailButtons.iventDetailCommunicationCall(onTap: () {}),
          if (pageContent.websiteUrl != null) IventDetailButtons.iventDetailCommunicationLink(onTap: () {}),
        ],
        const SizedBox(width: AppDimensions.padding16),
      ),
    );
  }

  /// Builds bottom bar for iVent creators showing share option
  Widget _buildCreatedViewBottomBar() {
    return Obx(() {
      final pageContent = _controller.iventInfoController.iventPage;
      if (pageContent == null) return const SizedBox.shrink();

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding20),
        child: SharedButtons.longBar(
          isEnabled: true,
          onTap: () => _shareIvent(pageContent),
          text: 'Etkinliği Paylaş',
          trailingIconPath: AppAssets.shareAndroid,
        ),
      );
    });
  }

  /// Builds bottom bar for other users showing join option
  Widget _buildOtherViewBottomBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text('Katılıyor musun?', style: AppTextStyles.size16Bold),
        IventDetailButtons.iventDetailJoin(
          onTap: () => _controller.goToInitiallyInvitableUsersPage(),
        ),
      ],
    );
  }
}
