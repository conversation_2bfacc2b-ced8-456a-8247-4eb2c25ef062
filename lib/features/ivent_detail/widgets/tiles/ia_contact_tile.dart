import 'package:flutter/widgets.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';

/// A tile widget for displaying contact information with an icon and text.
///
/// This widget follows the project's design patterns for displaying contact
/// methods and communication options. It provides a consistent interface for
/// showing different types of contact information like phone numbers, social
/// media handles, and other communication methods.
///
/// The widget uses the "Ia" prefix to follow the project's naming conventions
/// and integrates with the app's design system for consistent styling.
class IaContactTile extends StatelessWidget {
  /// The path to the icon asset to display
  final String iconPath;

  /// The text content to display next to the icon
  final String text;

  const IaContactTile({
    super.key,
    required this.iconPath,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        IaSvgIcon(iconPath: iconPath, iconSize: 24, iconColor: AppColors.darkGrey),
        const SizedBox(width: AppDimensions.padding8),
        Expanded(child: Text(text, style: AppTextStyles.size16RegularTextSecondary, maxLines: 1, softWrap: false)),
      ],
    );
  }

  /// Factory constructor for Google Forms contact tile
  static IaContactTile googleForms({
    required String text,
  }) {
    return IaContactTile(
      iconPath: AppAssets.googleForms,
      text: text,
    );
  }

  /// Factory constructor for Instagram contact tile
  static IaContactTile instagram({
    required String text,
  }) {
    return IaContactTile(
      iconPath: AppAssets.instagram,
      text: text,
    );
  }

  /// Factory constructor for WhatsApp group contact tile
  static IaContactTile whatsappGroup({
    required String text,
  }) {
    return IaContactTile(
      iconPath: AppAssets.whatsapp,
      text: text,
    );
  }

  /// Factory constructor for WhatsApp message contact tile
  static IaContactTile whatsappMessage({
    required String text,
  }) {
    return IaContactTile(
      iconPath: AppAssets.whatsapp,
      text: text,
    );
  }

  /// Factory constructor for phone call contact tile
  static IaContactTile phoneCall({
    required String text,
  }) {
    return IaContactTile(
      iconPath: AppAssets.phone,
      text: text,
    );
  }

  /// Factory constructor for website contact tile
  static IaContactTile website({
    required String text,
  }) {
    return IaContactTile(
      iconPath: AppAssets.link,
      text: text,
    );
  }
}
