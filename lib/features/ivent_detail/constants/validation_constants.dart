/// Validation constants for ivent detail feature.
///
/// This class contains validation rules and limits used throughout the ivent detail
/// feature, organized by functional areas for better maintainability and consistency
/// with the project's constants management patterns.
class IventDetailValidationConstants {
  IventDetailValidationConstants._(); // Private constructor to prevent instantiation

  // ============================================================================
  // DESCRIPTION VALIDATION
  // ============================================================================

  /// Maximum length for ivent description
  static const int maxDescriptionLength = 500;

  /// Minimum length for ivent description
  static const int minDescriptionLength = 10;

  // ============================================================================
  // INVITATION LIMITS
  // ============================================================================

  /// Maximum number of users that can be invited at once
  static const int maxInvitationBatchSize = 50;

  /// Minimum number of participants required for an ivent
  static const int minParticipantCount = 1;

  /// Maximum number of participants allowed for an ivent
  static const int maxParticipantCount = 1000;

  // ============================================================================
  // UI DISPLAY LIMITS
  // ============================================================================

  /// Default number of items to show in collapsed lists
  static const int defaultListShowLimit = 3;

  /// Maximum number of tags to display in tag scroll
  static const int maxTagsToDisplay = 10;

  /// Default timeout for loading operations (in seconds)
  static const int defaultLoadingTimeoutSeconds = 30;
}
