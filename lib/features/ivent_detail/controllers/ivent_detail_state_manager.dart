import 'package:get/get.dart';
import 'package:ivent_app/shared/controllers/shared_state.dart';

/// State manager for iVent detail feature
///
/// Manages shared reactive state across all iVent detail controllers.
/// This includes navigation state, UI state, and shared data that needs
/// to be accessible across multiple controllers within the feature.
///
/// Follows the architecture guide's shared state pattern with proper
/// reactive variables, getters/setters, and helper methods.
class IventDetailSharedState extends SharedState {
  // Constants
  final String iventId;

  // Reactive state using GetX observables
  final _hasError = false.obs;
  final _errorMessage = ''.obs;
  final _currentView = Rxn<String>();
  final _isRefreshing = false.obs;

  // Constructor
  IventDetailSharedState(this.iventId);

  // Getters and setters for reactive state
  bool get hasError => _hasError.value;
  set hasError(bool value) => _hasError.value = value;

  String get errorMessage => _errorMessage.value;
  set errorMessage(String value) => _errorMessage.value = value;

  String? get currentView => _currentView.value;
  set currentView(String? value) => _currentView.value = value;

  bool get isRefreshing => _isRefreshing.value;
  set isRefreshing(bool value) => _isRefreshing.value = value;

  // Helper methods for complex state operations

  /// Clears any error state
  void clearError() {
    hasError = false;
    errorMessage = '';
  }

  /// Sets error state with message
  void setError(String message) {
    hasError = true;
    errorMessage = message;
  }

  /// Clears all state to defaults
  void clearAll() {
    hasError = false;
    errorMessage = '';
    currentView = null;
    isRefreshing = false;
  }

  /// Sets the current view being displayed
  void setCurrentView(String view) {
    currentView = view;
  }

  /// Starts refresh operation
  void startRefresh() {
    isRefreshing = true;
  }

  /// Ends refresh operation
  void endRefresh() {
    isRefreshing = false;
  }

  /// Checks if a specific view is currently active
  bool isViewActive(String view) {
    return currentView == view;
  }
}
