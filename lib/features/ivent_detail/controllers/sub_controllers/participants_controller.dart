import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/routes/ivent_detail.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Controller for managing iVent participants
///
/// Handles loading participant data and navigation to participant-related
/// pages. Manages different participant views based on the user's relationship
/// to the iVent (creator, participant, or visitor).
///
/// Follows the architecture guide's sub-controller pattern with proper
/// reactive state management and business logic methods.
class ParticipantsController extends BaseController<IventDetailSharedState> {
  // Reactive state specific to this sub-controller
  final _participants = Rxn<SearchParticipantsByIventIdReturn>();

  // Constructor
  ParticipantsController(
    AuthService authService,
    IventDetailSharedState state,
  ) : super(authService, state);

  // Getters for reactive state
  SearchParticipantsByIventIdReturn? get participants => _participants.value;

  // Setters for reactive state
  set participants(SearchParticipantsByIventIdReturn? value) => _participants.value = value;

  // Public methods

  /// Loads participants and navigates to appropriate participants page
  ///
  /// [viewType] determines which page to navigate to
  /// [participantCount] is used to check if navigation should occur
  /// [q] optional search query for filtering participants
  Future<void> getParticipantsPage(
    IventViewTypeEnum viewType,
    int participantCount, {
    String? q,
  }) async {
    // Don't navigate if no participants and default view
    if (viewType == IventViewTypeEnum.default_ && participantCount == 0) {
      return;
    }

    await runWithLoading(
      () async {
        participants = await authService.squadMembershipsApi.searchParticipantsByIventId(
          state.iventId,
          q: q,
        );

        _navigateToParticipantsPage(viewType);
      },
      loadingTag: 'getParticipantsPage',
    );
  }

  // Private methods

  /// Navigates to the appropriate participants page based on view type
  void _navigateToParticipantsPage(IventViewTypeEnum viewType) {
    switch (viewType) {
      case IventViewTypeEnum.joined:
        Get.toNamed(IventDetayRoutes.IVENT_DETAY_SQUAD, arguments: state.iventId);
        break;
      case IventViewTypeEnum.created:
      default:
        Get.toNamed(IventDetayRoutes.IVENT_DETAY_KISILER, arguments: state.iventId);
        break;
    }
  }
}
