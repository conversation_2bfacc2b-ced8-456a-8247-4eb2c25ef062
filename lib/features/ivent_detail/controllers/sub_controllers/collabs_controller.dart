import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/routes/ivent_detail.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Controller for managing iVent collaborators
///
/// Handles searching and displaying collaborators (paydaşlar) for an iVent.
/// Provides search functionality and navigation to the collaborators page.
/// Integrates with BaseSearchBarController for search input management.
///
/// Follows the architecture guide's sub-controller pattern with search
/// functionality and proper reactive state management.
class CollabsController extends BaseControllerWithSearch<IventDetailSharedState> {
  // Reactive state specific to this sub-controller
  final _collabsResult = Rxn<SearchCollabsReturn>();

  // Constructor
  CollabsController(
    AuthService authService,
    IventDetailSharedState state,
  ) : super(authService, state);

  // Getters
  SearchCollabsReturn? get collabsResult => _collabsResult.value;

  @override
  bool get isResultsEmpty => collabsResult?.collabs.isEmpty ?? true;

  @override
  Future<void> onSearch([String? query]) async {
    await runWithLoading(
      () async {
        _collabsResult.value = await authService.iventCollabsApi.searchCollabs(
          state.iventId,
          q: query,
        );
      },
      loadingTag: 'searchCollabs',
    );
  }

  // Public methods

  /// Navigates to collaborators page and loads data if needed
  Future<void> getCollabsPage({String? q}) async {
    Get.toNamed(IventDetayRoutes.IVENT_DETAY_PAYDASLAR, arguments: state.iventId);

    // Load initial data if not already loaded
    if (collabsResult == null) {
      await onSearch();
    }
  }
}
