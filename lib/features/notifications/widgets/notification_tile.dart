import 'package:flutter/material.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/utils/format_named.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_image_container.dart';
import 'package:ivent_app/features/notifications/constants/notification_templates.dart';

class NotificationTile extends StatelessWidget {
  final NotificationItem notification;

  const NotificationTile({super.key, required this.notification});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.padding20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildAvatar(),
          const SizedBox(width: AppDimensions.padding12),
          _buildContent(),
          _buildImage(),
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return CircleAvatar(
      radius: 16,
      backgroundImage: notification.accountAvatarUrl != null ? NetworkImage(notification.accountAvatarUrl!) : null,
      backgroundColor: AppColors.mediumGrey,
    );
  }

  Widget _buildContent() {
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildText(),
          _buildDate(),
          // _buildAction(),
        ],
      ),
    );
  }

  Widget _buildText() {
    return Text(
      formatNamed(notificationTemplates[notification.notificationType]!, {
        'accountUsername': notification.accountUsername,
      }),
      style: AppTextStyles.size14Medium,
      maxLines: null,
    );
  }

  Widget _buildDate() => Text(notification.createdAt, style: AppTextStyles.size10RegularTextSecondary);

  // Widget _buildAction() {
  //   return const Text(
  //     '3 Memories',
  //     style: TextStyle(fontSize: 12, color: AppColors.textSecondary),
  //     maxLines: 1,
  //     softWrap: false,
  //   );
  // }

  Widget _buildImage() {
    if (notification.contentThumbnailUrl == null) return const SizedBox.shrink();
    return IaImageContainer.withImageUrl(
      margin: const EdgeInsets.only(left: AppDimensions.padding20),
      imageUrl: notification.contentThumbnailUrl!,
      roundness: AppDimensions.radiusM,
    );
  }
}
