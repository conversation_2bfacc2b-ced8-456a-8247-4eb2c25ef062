import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/notifications/controllers/notification_controller.dart';
import 'package:ivent_app/features/notifications/widgets/notification_tile.dart';

class NotificationsPage extends StatelessWidget {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final notificationController = Get.find<NotificationController>();

    return Obx(() {
      final notifications = notificationController.notifications;
      if (notifications == null) return const IaLoadingIndicator();
      return ListView.separated(
        itemCount: notifications.notificationCount,
        itemBuilder: (context, index) {
          return NotificationTile(notification: notifications.notifications[index]);
        },
        separatorBuilder: (context, index) => const IaDivider(),
      );
    });
  }
}
