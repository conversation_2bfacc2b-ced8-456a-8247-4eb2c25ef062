import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/notifications/controllers/notification_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class NotificationController extends BaseController<NotificationSharedState> {
  // Reactive state
  final _notifications = Rxn<GetNotificationsReturn>();

  /// Creates a new notifications controller
  NotificationController(AuthService authService, NotificationSharedState state) : super(authService, state);

  // Getters

  /// List of notifications
  GetNotificationsReturn? get notifications => _notifications.value;

  Future<void> getNotifications() async {
    _notifications.value = await authService.notificationsApi.getNotifications(
      limit: 10,
      page: 1,
    );
  }
}
