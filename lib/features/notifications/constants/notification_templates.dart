import 'package:ivent_app/api/api.dart';

Map<NotificationTypeEnum, String> notificationTemplates = {
  NotificationTypeEnum.arkadaslikIstegiGonderdi: '{accountUsername} sana arkadaşlık isteği gönderdi.',
  NotificationTypeEnum.artikArkadassiniz: '{accountUsername} ile artık arkadassınız.',
  NotificationTypeEnum.arkadaslikIstegiOnayladi: '{accountUsername}, arkadaşlık isteğini onayladı.',
  NotificationTypeEnum.uyelikIstegiGonderdi: '{accountUsername} sayfana üyelik isteği gönderdi',
  NotificationTypeEnum.uyelikIstegiKabulEtti: '{accountUsername} üyelik isteğini kabul etti',
  NotificationTypeEnum.sayfaniTakipEtti: '{accountUsername} sayfanı takip etmeye başladı',
  
  NotificationTypeEnum.iventineVibeEkledi: '{accountUsername} Iventine vibe ekledi',
  NotificationTypeEnum.sayfanaIventEkledi: '{accountUsername} Sayfana ivent ekledi',
  NotificationTypeEnum.uyelikDaveti: '{accountUsername} Üyelik daveti',
  NotificationTypeEnum.sayfanaUyeOldu: '{accountUsername} Sayfana üye oldu',
  NotificationTypeEnum.vibeinaMedyaEkledi: '{accountUsername} Vibeina medya ekledi',
  NotificationTypeEnum.iventeDavetEtti: '{accountUsername} Ivente davet etti',
  NotificationTypeEnum.iventDavetiniKabulEtti: '{accountUsername} Ivent davetini kabul etti',
  NotificationTypeEnum.vibeiniBegendi: '{accountUsername} Vibeini beğendi',
  NotificationTypeEnum.uygulamayaKatildi: '{accountUsername} Uygulamaya katıldı',
  NotificationTypeEnum.iventBilgileriniGuncelledi: '{accountUsername} Ivent bilgilerini güncelledi',
  NotificationTypeEnum.iventIptal: '{accountUsername} Ivent iptal etti',
  NotificationTypeEnum.whatsappGrubunaKatilmaIstegiGonderdi: '{accountUsername} Whatsapp grubuna katilma isteği gönderdi',
  NotificationTypeEnum.whatsappGrubunaKatilmaIstegiOnaylandi: '{accountUsername} Whatsapp grubuna katilma isteği onaylandı',
  NotificationTypeEnum.iventePaydasOlarakEklemeIstegi: '{accountUsername} Ivente paydas olarak ekleme isteği gönderdi',
  NotificationTypeEnum.iventePaydaslikIstegiKabulEdildi: '{accountUsername} Ivente paydaslık isteği kabul edildi',
  NotificationTypeEnum.iventiniXKisiFavoriledi: '{accountUsername} Iventini favoriletti',
  NotificationTypeEnum.iventineKatildi: '{accountUsername} Ivente katıldı',
  NotificationTypeEnum.iventYayinladi: '{accountUsername} Ivent yayınıldı',
  NotificationTypeEnum.iventeXAdetMemoryEkledi: '{accountUsername} Ivente x adet memory ekledi',
  NotificationTypeEnum.arkadasGrubunaEkledi: '{accountUsername} Arkadaş grubuna ekledi',
  NotificationTypeEnum.vibeEklemekIcinSonucSaat: '{accountUsername} Vibe eklemek için sonuç saat',
  NotificationTypeEnum.vibeEklemekIcinYirmiDortSaat: '{accountUsername} Vibe eklemek için 24 saat',
  NotificationTypeEnum.iventYaklasiyor: '{accountUsername} Ivent yaklaşıyor',
  NotificationTypeEnum.iventCreatorBasvurunuzOnaylandi: '{accountUsername} Ivent Creator başvurunuz onaylandı',
  NotificationTypeEnum.iventCreatorBasvurunuzReddedildi: '{accountUsername} Ivent Creator başvurunuz reddedildi',
  NotificationTypeEnum.sayfaBasvurunuzOnaylandi: '{accountUsername} Sayfa başvurunuz onaylandı',
  NotificationTypeEnum.sayfaBasvurunuzReddedildi: '{accountUsername} Sayfa başvurunuz reddedildi',
  NotificationTypeEnum.vibeYorumYapti: '{accountUsername} Vibe yorum yaptı',
  NotificationTypeEnum.sayfayaUyeOldunuz: '{accountUsername} Sayfaya üye oldunuz',
  NotificationTypeEnum.vibeinizaEklediginizMedyayiGizlediniz: '{accountUsername} Vibeinize eklediğiniz medyayı gizlediniz',
  NotificationTypeEnum.vibeinizaEklediginizMedyayiEklediniz: '{accountUsername} Vibeinize eklediğiniz medyayı eklediniz',
  NotificationTypeEnum.whatsappGrubunaKatilmaIstegiOnayladiniz: '{accountUsername} Whatsapp grubuna katilma isteği onayladınız',
  NotificationTypeEnum.iventePaydasOldunuz: '{accountUsername} Ivente paydas oldunuz',
  NotificationTypeEnum.ileIventeKatildiniz: '{accountUsername} Ile ivente katıldınız',
  NotificationTypeEnum.iventeKatiliyor: '{accountUsername} Ivente katılıyor',
};
