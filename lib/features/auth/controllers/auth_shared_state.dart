import 'package:get/get.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';
import 'package:ivent_app/shared/controllers/shared_state.dart';

class AuthSharedState extends SharedState {
  final _phoneNumber = ''.obs;
  String get phoneNumber => _phoneNumber.value;
  set phoneNumber(String value) => _phoneNumber.value = value;

  String get formattedPhoneNumber {
    if (phoneNumber.length != AuthValidationConstants.phoneNumberMaxLength) {
      return '';
    }
    return _formatPhoneNumber(phoneNumber);
  }

  bool get isPhoneNumberValid => phoneNumber.length == AuthValidationConstants.phoneNumberMaxLength;

  String _formatPhoneNumber(String value) {
    if (value.length != AuthValidationConstants.phoneNumberMaxLength) {
      return value;
    }

    final String areaCode = value.substring(0, 3);
    final String remainingNumber = value.substring(3);
    return '${AuthValidationConstants.turkeyCountryCode}($areaCode)$remainingNumber';
  }
}
