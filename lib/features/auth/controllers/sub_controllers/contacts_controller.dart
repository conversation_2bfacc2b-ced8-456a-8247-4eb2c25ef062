import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/auth/controllers/auth_shared_state.dart';
import 'package:ivent_app/features/auth/models/contact.dart';
import 'package:ivent_app/routes/auth.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class ContactsController extends BaseControllerWithSearch<AuthSharedState> {
  ContactsController(AuthService authService, AuthSharedState state) : super(authService, state);

  @override
  bool get isResultsEmpty => searchResults.isEmpty;

  @override
  Future<void> onSearch([String? query]) async {
    if (query == null || query.isEmpty) {
      searchResults.assignAll(contacts);
    } else {
      searchResults.assignAll(contacts
          .where((contact) => (contact.username?.contains(query) ?? false) || contact.phoneNumber.contains(query))
          .toList());
    }
  }

  final _pendingContactIds = <String>[].obs;
  final _getContactsReturn = Rxn<GetContactsByUserIdReturn>();
  final _contacts = <ContactItem>[].obs;
  final _searchResults = <ContactItem>[].obs;

  List<String> get pendingContactIds => _pendingContactIds;
  GetContactsByUserIdReturn? get getContactsReturn => _getContactsReturn.value;
  List<ContactItem> get contacts => _contacts;
  List<ContactItem> get searchResults => _searchResults;

  set pendingContactIds(List<String> value) => _pendingContactIds.assignAll(value);
  set getContactsReturn(GetContactsByUserIdReturn? value) => _getContactsReturn.value = value;
  set contacts(List<ContactItem> value) => _contacts.assignAll(value);
  set searchResults(List<ContactItem> value) => _searchResults.assignAll(value);

  int get pendingInvitationsCount => pendingContactIds.length;

  Future<void> goToContactsPage(bool accessGranted) async {
    await runWithLoading(() async {
      Get.toNamed(AuthRoutes.CONTACTS_PAGE, arguments: accessGranted);
      if (accessGranted) await _loadContacts();
    });
  }

  Future<void> _loadContacts() async {
    await runWithLoading(() async {
      // Read device contacts and extract phone numbers
      final phoneNumbers = await _getDeviceContactPhoneNumbers();

      getContactsReturn = await authService.usersApi.getContactsByUserId(
        sessionUser.sessionId,
        GetContactsByUserIdDto(phoneNumbers: phoneNumbers),
      );

      contacts.assignAll(getContactsReturn!.contacts.map((e) => ContactItem.fromUserListItem(e)).toList());
      contacts.assignAll(phoneNumbers.map((e) => ContactItem.fromPhoneNumber(e)).toList());
    });
  }

  Future<List<String>> _getDeviceContactPhoneNumbers() async {
    // Check if contacts permission is granted
    if (!await FlutterContacts.requestPermission()) return [];

    // Get all contacts with phone numbers
    final contacts = await FlutterContacts.getContacts(
      withProperties: true,
      withPhoto: false,
    );

    return contacts.map((contact) => contact.phones.map((phone) => phone.number).toList()).expand((x) => x).toList();
  }

  Future<void> toggleFriendRequest(String userId) async {
    await runWithLoading(() async {
      if (pendingContactIds.contains(userId)) {
        await _cancelFriendRequest(userId);
      } else {
        await _sendFriendRequest(userId);
      }
    });
  }

  UserRelationshipStatusEnum? getRelationshipStatus(String userId) {
    if (!pendingContactIds.contains(userId)) return null;
    return UserRelationshipStatusEnum.pending;
  }

  Future<void> _sendFriendRequest(String userId) async {
    await authService.userRelationshipsApi.inviteFriendByUserId(userId);
    pendingContactIds.add(userId);
  }

  Future<void> _cancelFriendRequest(String userId) async {
    await authService.userRelationshipsApi.removeFriendByUserId(userId);
    pendingContactIds.remove(userId);
  }
}
