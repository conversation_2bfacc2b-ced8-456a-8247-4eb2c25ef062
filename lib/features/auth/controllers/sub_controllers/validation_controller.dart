import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/auth/controllers/auth_shared_state.dart';
import 'package:ivent_app/routes/auth.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class ValidationController extends BaseController<AuthSharedState> {
  ValidationController(AuthService authService, AuthSharedState state) : super(authService, state);

  final _validationCode = ''.obs;
  final _validateReturn = Rxn<ValidateReturn>();
  String get validationCode => _validationCode.value;
  ValidateReturn? get validateReturn => _validateReturn.value;
  set validationCode(String value) => _validationCode.value = value;
  set validateReturn(ValidateReturn? value) => _validateReturn.value = value;

  bool get isValidationCodeComplete => validationCode.length == 6;

  Future<void> validateUser() async {
    await runWithLoading(
      () async {
        validateReturn = await authService.authApi.validate(
          ValidateDto(
            validationCode: validationCode,
            phoneNumber: state.formattedPhoneNumber,
          ),
        );

        if (validateReturn == null) {
          goToSomethingWentWrongPage();
          return;
        }

        if (validateReturn!.type == AuthEnum.login) {
          await _loginUser();
          Get.toNamed(AuthRoutes.APP_NAVIGATION);
        } else {
          Get.toNamed(AuthRoutes.NAME_PAGE);
        }
      },
      loadingTag: 'validateUser',
    );
  }

  Future<void> _loginUser() async {
    final result = validateReturn!;
    final sessionUser = SessionUser(
      token: result.token!,
      sessionId: result.userId!,
      sessionRole: result.role!,
      sessionUsername: result.username!,
      sessionFullname: result.fullname!,
      sessionAvatarUrl: result.avatarUrl,
    );
    await authService.login(sessionUser);
  }
}
