import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';
import 'package:ivent_app/features/auth/controllers/auth_shared_state.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class RegistrationController extends BaseController<AuthSharedState> {
  RegistrationController(AuthService authService, AuthSharedState state) : super(authService, state);

  final _fullname = ''.obs;
  final _checkedHobbyIds = <String>[].obs;
  final _registerReturn = Rxn<RegisterReturn>();
  String get fullname => _fullname.value;
  List<String> get checkedHobbyIds => _checkedHobbyIds;
  RegisterReturn? get registerReturn => _registerReturn.value;
  set fullname(String value) => _fullname.value = value.trim();
  set checkedHobbyIds(List<String> value) => _checkedHobbyIds.assignAll(value);
  set registerReturn(RegisterReturn? value) => _registerReturn.value = value;

  bool get isFullnameValid =>
      fullname.trim().length >= AuthValidationConstants.fullNameMinLength &&
      fullname.trim().length <= AuthValidationConstants.fullNameMaxLength;

  bool get areHobbiesValid => checkedHobbyIds.length >= AuthValidationConstants.minRequiredHobbies;

  void addHobby(String hobbyId) {
    if (!checkedHobbyIds.contains(hobbyId)) {
      checkedHobbyIds.add(hobbyId);
    }
  }

  void removeHobby(String hobbyId) {
    checkedHobbyIds.remove(hobbyId);
  }

  void toggleHobby(String hobbyId) {
    if (checkedHobbyIds.contains(hobbyId)) {
      removeHobby(hobbyId);
    } else {
      addHobby(hobbyId);
    }
  }

  Future<void> registerUser() async {
    await runWithLoading(
      () async {
        final registerDto = RegisterDto(
          fullname: fullname,
          phoneNumber: state.formattedPhoneNumber,
          hobbyIds: checkedHobbyIds,
        );

        registerReturn = await authService.usersApi.register(registerDto);

        if (registerReturn != null) {
          await _createUserSession();
        } else {
          throw Exception('Kayıt işlemi başarısız oldu');
        }
      },
    );
  }

  Future<void> _createUserSession() async {
    final result = registerReturn!;

    final sessionUser = SessionUser(
      token: result.token,
      sessionId: result.userId,
      sessionRole: result.role,
      sessionUsername: result.username,
      sessionFullname: result.fullname,
      sessionAvatarUrl: result.avatarUrl,
    );

    await authService.login(sessionUser);
  }
}
