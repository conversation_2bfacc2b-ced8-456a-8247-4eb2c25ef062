import 'package:ivent_app/api/api.dart';

class ContactItem {
  final String? userId;
  final String? username;
  final String? avatarUrl;
  final String? university;
  final String phoneNumber;

  ContactItem({
    this.userId,
    this.username,
    this.avatarUrl,
    this.university,
    required this.phoneNumber,
  });

  factory ContactItem.fromUserListItem(UserListItemWithPhoneNumber contact) {
    return ContactItem(
      userId: contact.userId,
      username: contact.username,
      avatarUrl: contact.avatarUrl,
      university: contact.university,
      phoneNumber: contact.phoneNumber,
    );
  }

  factory ContactItem.fromPhoneNumber(String phoneNumber) {
    return ContactItem(phoneNumber: phoneNumber);
  }

  String toString() {
    return 'ContactItem(userId: $userId, username: $username, avatarUrl: $avatarUrl, university: $university, phoneNumber: $phoneNumber)';
  }
}
