import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/notifications/controllers/notification_controller.dart';
import 'package:ivent_app/features/notifications/controllers/notification_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/profile_side_menu_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibe_upload_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_page_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';
import 'package:ivent_app/shared/controllers/shared_state.dart';

class AppNavigationController extends BaseController {
  final HomeSharedState homeSharedState;
  final VibesSharedState vibesSharedState;
  final NotificationSharedState notificationSharedState;
  final ProfileSharedState profileSharedState;

  AppNavigationController(
    AuthService authService,
    SharedState defaultSharedState,
    this.homeSharedState,
    this.vibesSharedState,
    this.notificationSharedState,
    this.profileSharedState,
  ) : super(authService, defaultSharedState);

  @override
  void initController() async {
    super.initController();
    final userId = sessionUser.sessionId;

    Get.lazyPut(() => HomeController(authService, homeSharedState), fenix: true);
    Get.lazyPut(() => VibesPageController(authService, vibesSharedState), fenix: true);
    Get.lazyPut(() => VibeUploadController(authService, vibesSharedState), fenix: true);
    Get.lazyPut(() => NotificationController(authService, notificationSharedState), fenix: true);
    Get.lazyPut(() => ProfileController(authService, profileSharedState), tag: userId, fenix: true);
    Get.lazyPut(() => ProfileSideMenuController(authService, profileSharedState), fenix: true);
  }

  @override
  void closeController() {
    Get.delete<HomeController>();
    Get.delete<VibesPageController>();
    Get.delete<VibeUploadController>();
    Get.delete<NotificationController>();
    Get.delete<ProfileController>(tag: sessionUser.sessionId);
    super.closeController();
  }
}
