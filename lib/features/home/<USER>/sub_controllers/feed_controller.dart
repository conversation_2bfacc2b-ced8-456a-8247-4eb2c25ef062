import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/home/<USER>/sub_controllers/home_panels_controller.dart';
import 'package:ivent_app/features/home/<USER>/feed_filters.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Controller for managing the feed functionality in the home feature
///
/// Handles iVent list retrieval, filtering, and pagination.
class FeedController extends BaseController<HomeSharedState> {
  // Dependencies
  final HomePanelsController homePanelsController;

  // Reactive state
  final _iventItems = <IventCardItem>[].obs;
  final _existingFeedFilters = Rxn<FeedFilters>();
  final _feedPage = 1.obs;
  final _feedContinuable = true.obs;

  /// Creates a new feed controller
  FeedController(
    AuthService authService,
    HomeSharedState state,
    this.homePanelsController,
  ) : super(authService, state);

  // Getters

  /// List of iVent items to display in the feed
  List<IventCardItem> get iventItems => _iventItems;

  /// Currently applied feed parameters
  FeedFilters? get existingFeedFilters => _existingFeedFilters.value;

  /// Current page number for pagination
  int get feedPage => _feedPage.value;

  /// Whether more feed items can be loaded
  bool get feedContinuable => _feedContinuable.value;

  // Setters
  set iventItems(List<IventCardItem> value) => _iventItems.value = value;
  set existingFeedFilters(FeedFilters? value) => _existingFeedFilters.value = value;
  set feedPage(int value) => _feedPage.value = value;
  set feedContinuable(bool value) => _feedContinuable.value = value;

  @override
  Future<void> initController() async {
    super.initController();
    await retrieveIventList();
  }

  /// Retrieves the list of iVents based on current filters
  Future<void> retrieveIventList() async {
    await runWithLoading(
      () async {
        // Build feed parameters
        final params = _buildFeedParams();
        existingFeedFilters = params;

        // Make API call to get iVent list
        final response = await authService.homeApi.feed(
          params.dateType,
          params.categories,
          locationCoeff: params.locationCoeff,
          q: params.q,
          page: 1,
          latitude: params.latitude,
          longitude: params.longitude,
          endDate: params.endDate,
          startDate: params.startDate,
        );

        // Update iVent items and pagination state
        if (response != null && response.ivents.isNotEmpty) {
          iventItems = response.ivents;
        }
      },
      loadingTag: 'feed',
    );
  }

  /// Loads more iVent items for pagination
  Future<void> loadMoreIventItems() async {
    if (!feedContinuable) return;

    await runWithLoading(
      () async {
        // Increment page number
        feedPage++;

        // Make API call to get more iVent items
        final response = await authService.homeApi.feed(
          existingFeedFilters!.dateType,
          existingFeedFilters!.categories,
          locationCoeff: existingFeedFilters!.locationCoeff,
          q: existingFeedFilters!.q,
          page: feedPage,
          latitude: existingFeedFilters!.latitude,
          longitude: existingFeedFilters!.longitude,
          endDate: existingFeedFilters!.endDate,
          startDate: existingFeedFilters!.startDate,
        );

        // Append new iVent items and update pagination state
        if (response != null && response.ivents.isNotEmpty) {
          iventItems = [...iventItems, ...response.ivents];
        } else {
          feedContinuable = false;
        }
      },
      loadingTag: 'feed',
      onError: () => feedPage--,
    );
  }

  /// Toggles a time filter at the given index
  void toggleTimeFilter(int index) {
    // Update the filter index
    state.dateFilterIndex = index;

    // Reset any selected dates
    if (index != 0) state.selectedDates = [];

    // Reload iVent list with new filter
    feedPage = 1;
    retrieveIventList();
  }

  /// Sets custom date range
  void setSelectedDates(DateTime startDate, DateTime endDate) {
    // Update selected dates
    state.selectedDates = [startDate, endDate];

    // Set filter to RANGE
    state.dateFilterIndex = FeedDateEnum.values.indexOf(FeedDateEnum.range);

    // Reload iVent list with new date range
    feedPage = 1;
    retrieveIventList();
  }

  /// Returns selected dates as a formatted string
  String selectedDatesAsString() {
    if (state.selectedDates.isEmpty) return 'Başlangıç Tarihi Seçiniz';
    if (state.selectedDates.length == 1) return 'Bitiş Tarihi Seçiniz';
    final format = DateFormat('d MMM');
    final startDate = format.format(state.selectedDates[0]);
    final endDate = format.format(state.selectedDates[1]);
    return '$startDate - $endDate';
  }

  /// Applies all filters and refreshes the feed
  void applyFilters() {
    // Reset to first page and reload
    feedPage = 1;
    retrieveIventList();
  }

  /// Builds feed parameters based on current filters
  FeedFilters _buildFeedParams() {
    final params = FeedFilters(
      dateType: FeedDateEnum.values[state.dateFilterIndex],
      categories: '',
      locationCoeff: state.locationCoeff,
      q: '',
    );

    // Add hobby IDs if selected
    if (state.selectedHobbyIds.isNotEmpty) {
      params.categories = state.selectedHobbyIds.join(',');
    }

    // Add location parameters if a place is selected
    if (state.selectedPlace != null) {
      params.latitude = state.selectedPlace!.latitude;
      params.longitude = state.selectedPlace!.longitude;
    }

    // Add date range if custom range is selected
    if (state.selectedDates.length == 2) {
      params.startDate = _formatDateForApi(state.selectedDates[0]);
      params.endDate = _formatDateForApi(state.selectedDates[1]);
    }

    return params;
  }

  /// Formats a date for API use (YYYY-MM-DD)
  String _formatDateForApi(DateTime date) => DateFormat('yyyy-MM-dd').format(date);
}
