import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/mapbox/controllers/mapbox_controller.dart';
import 'package:ivent_app/features/mapbox/models/ia_location_item.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class LocationController extends BaseControllerWithSearch<HomeSharedState> {
  late final MapboxController mapboxController;

  final _placeSuggestionResults = <SearchBoxSuggestFeature>[].obs;

  LocationController(AuthService authService, HomeSharedState state) : super(authService, state) {
    mapboxController = MapboxController(authService: authService);
  }

  List<SearchBoxSuggestFeature> get placeSuggestionResults => _placeSuggestionResults;

  @override
  bool get isResultsEmpty => placeSuggestionResults.isEmpty;

  set placeSuggestionResults(List<SearchBoxSuggestFeature> value) => _placeSuggestionResults.assignAll(value);

  @override
  Future<void> initController() async {
    super.initController();
    await mapboxController.getUserLocationCoordinates();
  }

  @override
  Future<void> onSearch([String? query]) async {
    if (query == null || query.isEmpty) {
      placeSuggestionResults = [];
      return;
    }

    await runWithLoading(
      () async {
        final proximity = mapboxController.userLocationCoordinates != null
            ? '${mapboxController.userLocationCoordinates!.lng},${mapboxController.userLocationCoordinates!.lat}'
            : null;

        final response = await authService.mapboxApi.searchBoxSuggest(
          query,
          sessionUser.sessionId,
          proximity: proximity,
          limit: 10,
        );

        if (response != null) {
          placeSuggestionResults = response.suggestions;
        } else {
          placeSuggestionResults = [];
        }
      },
      loadingTag: 'searchPlaces',
    );
  }

  Future<void> useCurrentLocation() async {
    await runWithLoading(() async {
      if (mapboxController.userLocation != null) {
        state.selectedPlace = mapboxController.userLocation;

        Get.back();
      } else {
        await mapboxController.getUserLocationCoordinates();

        if (mapboxController.userLocation == null) {
          print('Error: Unable to get current location');
        }
      }
    });
  }

  Future<void> selectPlace(SearchBoxSuggestFeature suggestion) async {
    await runWithLoading(() async {
      final result = await authService.mapboxApi.searchBoxRetrieve(
        suggestion.mapboxId,
        sessionUser.sessionId,
      );

      if (result != null && result.features.isNotEmpty) {
        final feature = result.features.first;

        final place = IaLocationItem(
          mapboxId: feature.properties.mapboxId,
          locationId: feature.properties.mapboxId,
          name: feature.properties.name,
          address: feature.properties.fullAddress ?? feature.properties.placeFormatted,
          latitude: feature.geometry.coordinates[1],
          longitude: feature.geometry.coordinates[0],
        );

        state.selectedPlace = place;

        Get.back();
      }
    });
  }
}
