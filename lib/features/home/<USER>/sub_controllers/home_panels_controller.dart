import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/routes/feed.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class HomePanelsController extends BaseController<HomeSharedState> {
  final PanelController panelController = PanelController();

  final _isPanelDraggable = true.obs;
  final _isPanelVisible = true.obs;
  final _screenIndex = 0.obs;

  HomePanelsController(AuthService authService, HomeSharedState state) : super(authService, state);

  bool get isPanelDraggable => _isPanelDraggable.value;
  bool get isPanelVisible => _isPanelVisible.value;
  int get screenIndex => _screenIndex.value;

  set isPanelDraggable(bool value) => _isPanelDraggable.value = value;
  set isPanelVisible(bool value) => _isPanelVisible.value = value;
  set screenIndex(int value) => _screenIndex.value = value;

  void goToFeedPage() {
    screenIndex = 0;
    isPanelDraggable = true;
    panelController.open();
  }

  void goToSearchPage() {
    screenIndex = 1;
    isPanelDraggable = false;
    panelController.open();
  }

  void goToFilterPage() {
    screenIndex = 2;
    isPanelDraggable = false;
    panelController.open();
  }

  void goToLocationPage() => Get.toNamed(FeedRoutes.LOCATION_PAGE);

  void setPanelOpen() => isPanelVisible = true;

  void setPanelClosed() => isPanelVisible = false;
}
