import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/home/<USER>/sub_controllers/search_accounts_controller.dart';
import 'package:ivent_app/features/home/<USER>/sub_controllers/search_ivents_controller.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class HomeSearchController extends BaseController<HomeSharedState> {
  late final SearchAccountsController searchAccountsController;
  late final SearchIventsController searchIventsController;

  HomeSearchController(AuthService authService, HomeSharedState state) : super(authService, state);

  final _searchTabIndex = 0.obs;

  int get searchTabIndex => _searchTabIndex.value;

  set searchTabIndex(int value) => _searchTabIndex.value = value;

  @override
  void initController() async {
    super.initController();
    searchAccountsController = Get.put(SearchAccountsController(authService, state));
    searchIventsController = Get.put(SearchIventsController(authService, state));
  }

  @override
  void closeController() {
    Get.delete<SearchAccountsController>();
    Get.delete<SearchIventsController>();
    super.closeController();
  }

  void changeSearchTab(int index) => searchTabIndex = index;
}
