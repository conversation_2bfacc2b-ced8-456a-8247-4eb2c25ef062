import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class SearchAccountsController extends BaseControllerWithSearch<HomeSharedState> {
  final _searchedAccountResults = <BasicAccountListItem>[].obs;

  SearchAccountsController(AuthService authService, HomeSharedState state) : super(authService, state);

  List<BasicAccountListItem> get searchedAccountResults => _searchedAccountResults;

  @override
  bool get isResultsEmpty => _searchedAccountResults.isEmpty;

  @override
  Future<void> onSearch([String? query]) async {
    if (query == null || query.isEmpty) {
      _searchedAccountResults.value = [];
      return;
    }

    await runWithLoading(
      () async {
        final response = await authService.homeApi.searchAccount(query);
        if (response != null) {
          _searchedAccountResults.value = response.accounts;
        } else {
          _searchedAccountResults.value = [];
        }
      },
      loadingTag: 'searchAccounts',
    );
  }
}
