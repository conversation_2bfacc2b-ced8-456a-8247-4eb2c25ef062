import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class SearchIventsController extends BaseControllerWithSearch<HomeSharedState> {
  final _searchedIventsResults = <IventCardItem>[].obs;

  SearchIventsController(AuthService authService, HomeSharedState state) : super(authService, state);

  List<IventCardItem> get searchedIventsResults => _searchedIventsResults;

  @override
  bool get isResultsEmpty => _searchedIventsResults.isEmpty;

  @override
  Future<void> onSearch([String? query]) async {
    if (query == null || query.isEmpty) {
      _searchedIventsResults.value = [];
      return;
    }

    await runWithLoading(
      () async {
        final response = await authService.homeApi.searchIvent(query);
        if (response != null) {
          _searchedIventsResults.value = response.ivents;
        } else {
          _searchedIventsResults.value = [];
        }
      },
      loadingTag: 'searchIvents',
    );
  }
}
