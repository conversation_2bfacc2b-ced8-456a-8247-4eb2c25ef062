import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Controller for managing filter functionality in the home feature
///
/// Handles hobby selection, filter clearing, and other filter-related operations.
class FilterController extends BaseController<HomeSharedState> {
  /// Creates a new filter controller
  FilterController(AuthService authService, HomeSharedState state) : super(authService, state);

  /// Toggles selection state of a hobby
  ///
  /// Adds the hobby to selected list if not present, removes it if already selected
  void toggleHobbySelection(String hobbyId) {
    state.toggleHobbySelection(hobbyId);
  }

  /// Clears all selected filters
  void clearFilters() {
    state.selectedHobbyIds = [];
  }

  /// Updates the location coefficient value
  void updateLocationCoeff(int value) {
    state.locationCoeff = value;
  }
}
