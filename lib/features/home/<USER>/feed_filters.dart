import 'package:ivent_app/api/api.dart';

class FeedFilters {
  FeedDateEnum dateType;
  String categories;
  int? locationCoeff;
  String? q;
  double? latitude;
  double? longitude;
  Object? endDate;
  Object? startDate;

  FeedFilters({
    required this.dateType,
    required this.categories,
    this.locationCoeff,
    this.q,
    this.latitude,
    this.longitude,
    this.endDate,
    this.startDate,
  });

  @override
  String toString() {
    return 'FeedFilters{dateType: $dateType, categories: $categories, locationCoeff: $locationCoeff, q: $q, latitude: $latitude, longitude: $longitude, endDate: $endDate, startDate: $startDate}';
  }
}
