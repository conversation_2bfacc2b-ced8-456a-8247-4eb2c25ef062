/// Validation constants for ivent create feature.
///
/// This class contains validation rules and limits used throughout the ivent create
/// feature, organized by functional areas for better maintainability and consistency
/// with the project's constants management patterns.
class IventCreateValidationConstants {
  IventCreateValidationConstants._(); // Private constructor to prevent instantiation

  // ============================================================================
  // FORM VALIDATION
  // ============================================================================

  /// Minimum length for ivent name
  static const int minIventNameLength = 3;

  /// Maximum length for ivent name
  static const int maxIventNameLength = 100;

  /// Minimum length for ivent description
  static const int minDescriptionLength = 10;

  /// Maximum length for ivent description
  static const int maxDescriptionLength = 500;

  // ============================================================================
  // DATE AND TIME VALIDATION
  // ============================================================================

  /// Minimum number of dates required for an ivent
  static const int minDatesRequired = 1;

  /// Maximum number of dates allowed for an ivent
  static const int maxDatesAllowed = 10;

  /// Minimum hours in advance an ivent can be scheduled
  static const int minHoursInAdvance = 1;

  /// Maximum days in advance an ivent can be scheduled
  static const int maxDaysInAdvance = 365;

  // ============================================================================
  // CATEGORY AND TAGS VALIDATION
  // ============================================================================

  /// Minimum number of tags required
  static const int minTagsRequired = 1;

  /// Maximum number of tags allowed
  static const int maxTagsAllowed = 5;

  // ============================================================================
  // CONTACT INFORMATION VALIDATION
  // ============================================================================

  /// Maximum length for contact URLs
  static const int maxUrlLength = 500;

  /// Maximum length for phone numbers
  static const int maxPhoneLength = 20;

  /// Maximum length for Instagram username
  static const int maxInstagramUsernameLength = 30;

  // ============================================================================
  // UI LIMITS
  // ============================================================================

  /// Default timeout for form submission (in seconds)
  static const int defaultSubmissionTimeoutSeconds = 30;

  /// Maximum file size for image upload (in MB)
  static const int maxImageSizeMB = 10;
}
