import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/buttons/hobby_buttons.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/core/widgets/layout/screens/ia_search_screen.dart';
import 'package:ivent_app/features/auth/widgets/hobby_category_box.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/sub_controllers/ivent_create_form_controller.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

class IventCreateTags extends StatefulWidget {
  const IventCreateTags({super.key});

  @override
  State<IventCreateTags> createState() => _IventCreateTagsState();
}

class _IventCreateTagsState extends State<IventCreateTags> {
  late final TextEditingController _searchBarController;
  late final IventCreateController _controller;
  bool _canContinueToNextPage = false;
  late List<String> _checkedHobbyIds = [];

  IventCreateFormController get _formController => _controller.formController;
  List<Hobby> get _selectedHobbies => Hobby.getHobbiesFromHobbyIds(_checkedHobbyIds);

  void _toggleHobby(String hobbyId) {
    if (_checkedHobbyIds.contains(hobbyId)) {
      _checkedHobbyIds.remove(hobbyId);
    } else {
      _checkedHobbyIds.add(hobbyId);
    }
    setState(() => _canContinueToNextPage = _checkedHobbyIds.isNotEmpty);
  }

  @override
  void initState() {
    super.initState();
    _controller = Get.find();
    _checkedHobbyIds = _formController.selectedTags.map((e) => e.hobbyId).toList();
    _searchBarController = TextEditingController();
  }

  @override
  void dispose() {
    _searchBarController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      showBackButton: false,
      title: 'En Az 2 İlgi Alanı',
      bodyPadding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppDimensions.padding20),
          _buildHintText(),
          Expanded(child: _buildSearchScreen()),
        ],
      ),
      floatingActionButton: IaFloatingActionButton(
        isEnabled: _canContinueToNextPage,
        text: 'Onayla',
        onPressed: () async {
          _formController.selectedTags = _selectedHobbies;
          Get.back();
        },
      ),
    );
  }

  IaSearchScreen _buildSearchScreen() {
    return IaSearchScreen(
      textEditingController: _searchBarController,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_checkedHobbyIds.isNotEmpty) _buildHobbiesSelected(),
          Expanded(child: _buildHobbiesAvailable()),
        ],
      ),
    );
  }

  Text _buildHintText() {
    return Text(
      'Seçeceğiniz ilgi alanları paylaştığınız etkinliklerin seçildiği kategorilerde yayınlanmasını sağlar.',
      style: AppTextStyles.size14RegularTextSecondary,
      maxLines: null,
    );
  }

  Container _buildHobbiesSelected() {
    return Container(
      margin: const EdgeInsets.only(bottom: 30),
      height: AppDimensions.buttonHeightSelectedHobbyTag,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        itemCount: _checkedHobbyIds.length,
        itemBuilder: (context, index) => HobbyButtons.selectedHobbyTag(
          onTap: () => _toggleHobby(_checkedHobbyIds[index]),
          text: Hobby.getHobbyNameFromHobbyId(_checkedHobbyIds[index]),
        ),
        separatorBuilder: (context, index) => const SizedBox(width: 10),
      ),
    );
  }

  ListView _buildHobbiesAvailable() {
    final keys = availableHobbies.keys.toList();
    final values = availableHobbies.values.toList();
    return ListView.separated(
      padding: const EdgeInsets.only(bottom: 100),
      itemCount: keys.length,
      itemBuilder: (context, index) {
        return HobbyCategoryBox(
          mainCategory: keys.elementAt(index),
          hobbyList: values.elementAt(index),
          selectedHobbyIds: _checkedHobbyIds,
          onHobbyToggle: _toggleHobby,
        );
      },
      separatorBuilder: (context, index) => const SizedBox(height: 40),
    );
  }
}

Map<String, List<Hobby>> availableHobbies = {
  'Müzik': Hobby.hobbyListByParentHobbyName['Müzik']!,
  'Sanat & Kültür': Hobby.hobbyListByParentHobbyName['Sanat & Kültür']!,
  'Spor': Hobby.hobbyListByParentHobbyName['Spor']!,
  'Kariyer & Akademik': Hobby.hobbyListByParentHobbyName['Kariyer & Akademik']!,
  'Yeme İçme': Hobby.hobbyListByParentHobbyName['Yeme İçme']!,
  'Toplum': Hobby.hobbyListByParentHobbyName['Toplum']!,
};
