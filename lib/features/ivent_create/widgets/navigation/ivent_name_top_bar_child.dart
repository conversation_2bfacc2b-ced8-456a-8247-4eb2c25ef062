import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';

/// A specialized top bar widget that displays the ivent name and image.
/// 
/// This widget is designed to be used in the top bar of ivent creation pages
/// to provide visual context about the ivent being created. It shows the
/// selected image as a circular avatar alongside the ivent name.
/// 
/// The widget automatically handles text overflow by truncating the ivent name
/// if it's too long to fit in the available space.
class IventNameTopBarChild extends StatelessWidget {
  /// The ivent creation controller containing the form data
  final IventCreateController controller;

  const IventNameTopBarChild({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _buildIventImage(),
        const SizedBox(width: AppDimensions.padding12),
        Expanded(child: _buildIventName()),
      ],
    );
  }

  /// Builds the circular avatar displaying the selected ivent image
  Widget _buildIventImage() {
    return CircleAvatar(
      radius: 24,
      backgroundColor: AppColors.mediumGrey,
      backgroundImage: controller.state.selectedImageUrl != null
          ? NetworkImage(controller.state.selectedImageUrl!)
          : null,
      child: controller.state.selectedImageUrl == null
          ? const Icon(Icons.image, color: AppColors.white)
          : null,
    );
  }

  /// Builds the ivent name text with proper overflow handling
  Widget _buildIventName() {
    return Text(
      controller.formController.iventName.isNotEmpty
          ? controller.formController.iventName
          : 'Etkinlik İsmi',
      style: AppTextStyles.size32Bold.copyWith(
        color: controller.formController.iventName.isNotEmpty
            ? AppColors.black
            : AppColors.textSecondary,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }
}
