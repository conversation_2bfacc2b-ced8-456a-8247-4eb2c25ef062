import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';

/// A progress indicator widget specifically designed for the ivent creation flow.
/// 
/// This widget displays the current progress through the ivent creation steps
/// using a series of dots or bars. It provides visual feedback to users about
/// their current position in the creation process and how many steps remain.
/// 
/// The indicator supports different visual styles and can be customized
/// to match the overall design of the ivent creation flow.
class IventCreateProgressIndicator extends StatelessWidget {
  /// Total number of steps in the creation flow
  final int totalSteps;
  
  /// Current step index (0-based)
  final int currentStep;
  
  /// Color for completed steps
  final Color completedColor;
  
  /// Color for the current step
  final Color currentColor;
  
  /// Color for remaining steps
  final Color remainingColor;
  
  /// Size of each step indicator
  final double stepSize;
  
  /// Spacing between step indicators
  final double spacing;
  
  /// Whether to show step numbers
  final bool showNumbers;

  const IventCreateProgressIndicator({
    super.key,
    required this.totalSteps,
    required this.currentStep,
    this.completedColor = AppColors.primary,
    this.currentColor = AppColors.secondary,
    this.remainingColor = AppColors.lightGrey,
    this.stepSize = 12.0,
    this.spacing = AppDimensions.padding8,
    this.showNumbers = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: _buildStepIndicators(),
    );
  }

  /// Builds the list of step indicator widgets
  List<Widget> _buildStepIndicators() {
    final indicators = <Widget>[];
    
    for (int i = 0; i < totalSteps; i++) {
      if (i > 0) {
        indicators.add(SizedBox(width: spacing));
      }
      indicators.add(_buildStepIndicator(i));
    }
    
    return indicators;
  }

  /// Builds an individual step indicator
  Widget _buildStepIndicator(int stepIndex) {
    final isCompleted = stepIndex < currentStep;
    final isCurrent = stepIndex == currentStep;
    
    Color color;
    if (isCompleted) {
      color = completedColor;
    } else if (isCurrent) {
      color = currentColor;
    } else {
      color = remainingColor;
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: stepSize,
      height: stepSize,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        border: isCurrent
            ? Border.all(color: currentColor, width: 2)
            : null,
      ),
      child: showNumbers
          ? Center(
              child: Text(
                '${stepIndex + 1}',
                style: TextStyle(
                  color: isCompleted || isCurrent ? AppColors.white : AppColors.textSecondary,
                  fontSize: stepSize * 0.6,
                  fontWeight: FontWeight.bold,
                ),
              ),
            )
          : null,
    );
  }

  /// Factory constructor for a simple dot-style progress indicator
  factory IventCreateProgressIndicator.dots({
    required int totalSteps,
    required int currentStep,
  }) {
    return IventCreateProgressIndicator(
      totalSteps: totalSteps,
      currentStep: currentStep,
      stepSize: 8.0,
      spacing: AppDimensions.padding4,
    );
  }

  /// Factory constructor for a numbered progress indicator
  factory IventCreateProgressIndicator.numbered({
    required int totalSteps,
    required int currentStep,
  }) {
    return IventCreateProgressIndicator(
      totalSteps: totalSteps,
      currentStep: currentStep,
      stepSize: 24.0,
      spacing: AppDimensions.padding12,
      showNumbers: true,
    );
  }

  /// Factory constructor for a large progress indicator
  factory IventCreateProgressIndicator.large({
    required int totalSteps,
    required int currentStep,
  }) {
    return IventCreateProgressIndicator(
      totalSteps: totalSteps,
      currentStep: currentStep,
      stepSize: 16.0,
      spacing: AppDimensions.padding12,
    );
  }
}
