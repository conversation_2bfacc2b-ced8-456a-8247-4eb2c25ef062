import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_divider.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';

/// A panel widget for selecting ivent privacy settings during creation.
///
/// This widget allows users to choose between different privacy levels
/// for their ivent (public, private, etc.) with visual feedback and
/// clear descriptions of each option.
///
/// Follows the architecture guide's widget naming conventions with "Ia" prefix
/// and proper widget structure patterns.
class IaIventCreatePrivacyPanel extends StatefulWidget {
  const IaIventCreatePrivacyPanel({super.key});

  @override
  State<IaIventCreatePrivacyPanel> createState() => _IaIventCreatePrivacyPanelState();
}

class _IaIventCreatePrivacyPanelState extends State<IaIventCreatePrivacyPanel> {
  final IventCreateController _controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Obx(() {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // IaListTile.withImageUrl(
            //   margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
            //   avatarUrl: _controller.submissionController.userBanner?.avatarUrl,
            //   title: _controller.submissionController.userBanner?.fullname,
            //   subtitle: 'Hesap seçimi ve paydaşları düzenle...',
            // ),
            const IaDivider(margin: EdgeInsets.symmetric(vertical: 8)),
            IaListTile.withIconData(
              onTap: () => _controller.formController.selectedIventPrivacy = IventPrivacyEnum.public,
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
              iconData: Icons.supervised_user_circle_outlined,
              title: 'Herkes',
              subtitle: 'Etkinliği herkesle paylaş',
              // trailing:
              //     IaCircularButton.checkBox(isSelected: _controller.selectedIventPrivacy == IventPrivacyEnum.PUBLIC),
            ),
            IaListTile.withIconData(
              onTap: () => _controller.formController.selectedIventPrivacy = IventPrivacyEnum.friends,
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
              iconData: Icons.group_outlined,
              title: 'Arkadaşlar',
              subtitle: 'Bla bla TODO',
              // trailing:
              //     IaCircularButton.checkBox(isSelected: _controller.selectedIventPrivacy == IventPrivacyEnum.FRIENDS),
            ),
            IaListTile.withIconData(
              onTap: () => _controller.formController.selectedIventPrivacy = IventPrivacyEnum.edu,
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
              iconData: Icons.group_outlined,
              title: 'Sadece Üniversite Öğrencileri',
              subtitle: 'Tüm üniversite öğrencileriyle paylaş',
              // trailing: IaCircularButton.checkBox(isSelected: _controller.selectedIventPrivacy == IventPrivacyEnum.EDU),
            ),
            IaListTile.withIconData(
              onTap: () => _controller.formController.selectedIventPrivacy = IventPrivacyEnum.selectedEdu,
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
              iconData: Icons.group_outlined,
              title: 'Seçili Üniversiteler',
              subtitle: 'Seçilen üniversitelerin öğrencileri ile paylaş',
              trailing: const Icon(Icons.keyboard_arrow_right),
            ),
            IaRoundedContainer(
              onTap: () => _controller.submissionController.closePanel(),
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
              color: AppColors.primary,
              text: 'Bitti',
            )
          ],
        );
      }),
    );
  }
}
