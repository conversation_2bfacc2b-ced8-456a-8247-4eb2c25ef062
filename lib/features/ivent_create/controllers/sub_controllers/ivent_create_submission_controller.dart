import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

/// Controller for managing iVent creation submission and finalization
///
/// Handles the final submission process including validation, data preparation,
/// API calls, and navigation after successful iVent creation.
///
/// Follows the architecture guide's sub-controller pattern with proper
/// reactive state management and business logic methods.
class IventCreateSubmissionController extends BaseController<IventCreateSharedState> {
  // Constructor
  IventCreateSubmissionController(AuthService authService, IventCreateSharedState state) : super(authService, state);

  final PanelController _panelController = PanelController();

  var _selectedPanelIndex = 0.obs;
  var _isPanelVisible = false.obs;

  int get selectedPanelIndex => _selectedPanelIndex.value;
  bool get isPanelVisible => _isPanelVisible.value;
  PanelController get panelController => _panelController;

  set selectedPanelIndex(int value) => _selectedPanelIndex.value = value;
  set isPanelVisible(bool value) => _isPanelVisible.value = value;

  @override
  Future<void> initController() async {
    super.initController();
    print('IventCreateSubmissionController has been initialized');
  }

  void openPanel(int panelIndex) {
    isPanelVisible = true;
    selectedPanelIndex = panelIndex;
    panelController.open();
  }

  void closePanel() {
    isPanelVisible = false;
    panelController.close();
  }
}
