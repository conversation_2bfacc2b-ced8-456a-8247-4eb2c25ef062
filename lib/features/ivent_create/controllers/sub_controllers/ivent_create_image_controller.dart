import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/routes/ivent_create.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Controller for managing iVent creation image selection and handling
///
/// Handles image-related operations including loading suggested images,
/// image selection, and image processing for the iVent creation process.
///
/// Follows the architecture guide's sub-controller pattern with proper
/// reactive state management and business logic methods.
class IventCreateImageController extends BaseController<IventCreateSharedState> {
  // Constructor
  IventCreateImageController(AuthService authService, IventCreateSharedState state) : super(authService, state);

  // Reactive state specific to this sub-controller
  final _suggestedImages = Rxn<GetSuggestedImagesReturn>();

  // Getters for reactive state
  GetSuggestedImagesReturn? get suggestedImages => _suggestedImages.value;

  // Setters for reactive state
  set suggestedImages(GetSuggestedImagesReturn? value) => _suggestedImages.value = value;

  Future<void> getSuggestedImagesPage() async {
    await runWithLoading(
      () async {
        suggestedImages = await authService.iventsApi.getSuggestedImages('');
        Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_IMAGE_SELECTION);
      },
      loadingTag: 'getSuggestedImages',
    );
  }
}
