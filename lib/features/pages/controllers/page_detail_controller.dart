import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/pages/controllers/page_creation_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Controller for page detail functionality
///
/// Manages page information, details, ivents, vibes, and follow operations.
/// Uses backend APIs for all data operations and follows proper state management
/// patterns with dependency injection.
///
/// Follows the architecture guide's controller patterns with proper BaseController
/// inheritance, initialization order, and comprehensive lifecycle management.
class PageDetailController extends BaseController<PageCreationSharedState> {
  /// The page ID for this detail view
  final String pageId;

  /// Constructor with dependency injection
  PageDetailController(AuthService authService, this.pageId) : super(authService, PageCreationSharedState());

  // Lifecycle methods

  @override
  Future<void> initController() async {
    super.initController();

    try {
      debugPrint('PageDetailController initialized for page: $pageId');

      // Load page data
      await _loadPageData();
    } catch (e) {
      handleError(e);
    }
  }

  @override
  void closeController() {
    try {
      debugPrint('PageDetailController cleaned up for page: $pageId');
    } catch (e) {
      debugPrint('Error cleaning up PageDetailController: $e');
    }

    super.closeController();
  }

  // Reactive state
  final _pageInfo = Rxn<GetPageByPageIdReturn>();
  final _pageDetails = Rxn<GetPageDetailsByPageIdReturn>();
  final _pageIvents = Rxn<GetIventsCreatedByPageIdReturn>();
  final _pageVibes = Rxn<GetVibeFoldersByPageIdReturn>();
  final _isFollowing = false.obs;
  final _isSubscribed = false.obs;
  final _currentTab = 0.obs; // 0: About, 1: iVents, 2: Vibes

  // Getters
  GetPageByPageIdReturn? get pageInfo => _pageInfo.value;
  GetPageDetailsByPageIdReturn? get pageDetails => _pageDetails.value;
  GetIventsCreatedByPageIdReturn? get pageIvents => _pageIvents.value;
  GetVibeFoldersByPageIdReturn? get pageVibes => _pageVibes.value;
  bool get isFollowing => _isFollowing.value;
  bool get isSubscribed => _isSubscribed.value;
  int get currentTab => _currentTab.value;

  // Setters
  set pageInfo(GetPageByPageIdReturn? value) => _pageInfo.value = value;
  set pageDetails(GetPageDetailsByPageIdReturn? value) => _pageDetails.value = value;
  set pageIvents(GetIventsCreatedByPageIdReturn? value) => _pageIvents.value = value;
  set pageVibes(GetVibeFoldersByPageIdReturn? value) => _pageVibes.value = value;
  set isFollowing(bool value) => _isFollowing.value = value;
  set isSubscribed(bool value) => _isSubscribed.value = value;
  set currentTab(int value) => _currentTab.value = value;

  // Navigation methods with proper error handling

  /// Navigates to vibe detail page
  void navigateToVibeDetail(String vibeFolderId) {
    try {
      Get.toNamed('/vibe-detail', arguments: vibeFolderId);
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates to ivent detail page
  void navigateToIventDetail(String iventId) {
    try {
      Get.toNamed('/ivent-detail', arguments: iventId);
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates back to previous page
  void goBack() => Get.back();

  /// Refreshes page data
  Future<void> refreshPageData() async {
    try {
      await _loadPageData();
    } catch (e) {
      handleError(e);
    }
  }

  /// Loads all page data from backend APIs
  Future<void> _loadPageData() async {
    try {
      debugPrint('🔄 Loading page data for: $pageId');

      // Load page basic info
      await _loadPageInfo();

      // Load page details
      await _loadPageDetails();

      // Load page ivents
      await _loadPageIvents();

      // Load page vibes
      await _loadPageVibes();

      debugPrint('✅ Page data loaded successfully');
    } catch (e) {
      debugPrint('❌ Error loading page data: $e');
      Get.snackbar(
        'Hata',
        'Sayfa bilgileri yüklenirken bir hata oluştu.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {}
  }

  /// Loads page basic information
  Future<void> _loadPageInfo() async {
    try {
      debugPrint('📄 Loading page info from API...');

      final result = await authService.pagesApi.getPageByPageId(pageId);

      if (result != null) {
        pageInfo = result;
        debugPrint('✅ Page info loaded: ${result.pageName}');
      } else {
        debugPrint('⚠️ No page info found');
      }
    } catch (e) {
      debugPrint('❌ Error loading page info: $e');
      throw e;
    }
  }

  /// Loads page details
  Future<void> _loadPageDetails() async {
    try {
      debugPrint('📋 Loading page details from API...');

      final result = await authService.pagesApi.getDetailsByPageId(pageId);

      if (result != null) {
        pageDetails = result;
        debugPrint('✅ Page details loaded');
      } else {
        debugPrint('⚠️ No page details found');
      }
    } catch (e) {
      debugPrint('❌ Error loading page details: $e');
      throw e;
    }
  }

  /// Loads page ivents
  Future<void> _loadPageIvents() async {
    try {
      debugPrint('🎉 Loading page ivents from API...');

      final result = await authService.pagesApi.getIventsCreatedByPageId(
        pageId,
        limit: 20,
        page: 1,
      );

      if (result != null) {
        pageIvents = result;
        debugPrint('✅ Page ivents loaded: ${result.ivents.length} ivents');
      } else {
        debugPrint('⚠️ No page ivents found');
      }
    } catch (e) {
      debugPrint('❌ Error loading page ivents: $e');
      throw e;
    }
  }

  /// Loads page vibes
  Future<void> _loadPageVibes() async {
    try {
      debugPrint('🎵 Loading page vibes from API...');

      final result = await authService.pagesApi.getVibesByPageId(
        pageId,
        limit: 20,
        page: 1,
      );

      if (result != null) {
        pageVibes = result;
        debugPrint('✅ Page vibes loaded: ${result.vibeFolders.length} vibes');
      } else {
        debugPrint('⚠️ No page vibes found');
      }
    } catch (e) {
      debugPrint('❌ Error loading page vibes: $e');
      throw e;
    }
  }

  /// Toggles follow status
  Future<void> toggleFollow() async {
    try {
      debugPrint('🔄 Toggling follow status...');

      if (isFollowing) {
        // Unfollow - API doesn't have unfollow endpoint, so we'll handle it gracefully
        debugPrint('⚠️ Unfollow API not available, handling gracefully');
        Get.snackbar(
          'Bilgi',
          'Takip bırakma özelliği henüz mevcut değil.',
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        // Follow
        await authService.pagesApi.followByPageId(pageId);
        isFollowing = true;

        // Update follower count if page info exists
        if (pageInfo != null) {
          final updatedInfo = GetPageByPageIdReturn(
            pageId: pageInfo!.pageId,
            pageName: pageInfo!.pageName,
            thumbnailUrl: pageInfo!.thumbnailUrl,
            createdIventCount: pageInfo!.createdIventCount,
            followerCount: pageInfo!.followerCount + 1,
            tagIds: pageInfo!.tagIds,
            haveMembership: pageInfo!.haveMembership,
            isFirstPerson: pageInfo!.isFirstPerson,
          );
          pageInfo = updatedInfo;
        }

        debugPrint('✅ Page followed successfully');
        Get.snackbar(
          'Başarılı',
          'Sayfa takip edildi!',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      debugPrint('❌ Error toggling follow: $e');
      Get.snackbar(
        'Hata',
        'Takip durumu güncellenirken bir hata oluştu.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {}
  }

  /// Toggles subscription status
  Future<void> toggleSubscription() async {
    try {
      debugPrint('🔄 Toggling subscription status...');

      if (isSubscribed) {
        // Unsubscribe - API doesn't have unsubscribe endpoint, so we'll handle it gracefully
        debugPrint('⚠️ Unsubscribe API not available, handling gracefully');
        Get.snackbar(
          'Bilgi',
          'Bildirim kapatma özelliği henüz mevcut değil.',
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        // Subscribe
        await authService.pagesApi.subscribeByPageId(pageId);
        isSubscribed = true;

        debugPrint('✅ Page subscribed successfully');
        Get.snackbar(
          'Başarılı',
          'Sayfa bildirimlerine abone oldunuz!',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      debugPrint('❌ Error toggling subscription: $e');
      Get.snackbar(
        'Hata',
        'Bildirim durumu güncellenirken bir hata oluştu.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {}
  }

  /// Changes current tab
  void changeTab(int tabIndex) {
    currentTab = tabIndex;
    debugPrint('📑 Changed to tab: $tabIndex');
  }

  /// Blocks the page
  Future<void> blockPage() async {
    try {
      debugPrint('🚫 Blocking page...');

      await authService.pagesApi.blockPageByPageId(pageId);

      debugPrint('✅ Page blocked successfully');
      Get.snackbar(
        'Başarılı',
        'Sayfa engellendi.',
        snackPosition: SnackPosition.BOTTOM,
      );

      // Go back after blocking
      Get.back();
    } catch (e) {
      debugPrint('❌ Error blocking page: $e');
      Get.snackbar(
        'Hata',
        'Sayfa engellenirken bir hata oluştu.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {}
  }
}
