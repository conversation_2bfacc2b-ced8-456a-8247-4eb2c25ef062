/// Validation constants for pages feature.
///
/// This class contains validation rules and limits used throughout the pages
/// feature, organized by functional areas for better maintainability and consistency
/// with the project's constants management patterns.
class PagesValidationConstants {
  PagesValidationConstants._(); // Private constructor to prevent instantiation

  // ============================================================================
  // PAGE INFORMATION VALIDATION
  // ============================================================================

  /// Minimum length for page name
  static const int minPageNameLength = 3;

  /// Maximum length for page name
  static const int maxPageNameLength = 100;

  /// Minimum length for page description
  static const int minDescriptionLength = 0;

  /// Maximum length for page description
  static const int maxDescriptionLength = 500;

  /// Maximum length for website URL
  static const int maxWebsiteUrlLength = 500;

  // ============================================================================
  // HOBBY SELECTION VALIDATION
  // ============================================================================

  /// Minimum number of hobbies required
  static const int minHobbiesRequired = 1;

  /// Maximum number of hobbies allowed
  static const int maxHobbiesAllowed = 10;

  /// Maximum hobbies to display initially in category
  static const int maxInitialHobbiesDisplay = 6;

  // ============================================================================
  // ADMIN SELECTION VALIDATION
  // ============================================================================

  /// Minimum number of admins required
  static const int minAdminsRequired = 1;

  /// Maximum number of admins allowed
  static const int maxAdminsAllowed = 10;

  /// Minimum characters for admin search
  static const int minAdminSearchLength = 2;

  /// Maximum characters for admin search
  static const int maxAdminSearchLength = 50;

  // ============================================================================
  // IMAGE UPLOAD VALIDATION
  // ============================================================================

  /// Maximum file size for page thumbnail (in MB)
  static const int maxThumbnailSizeMB = 5;

  /// Allowed image formats
  static const List<String> allowedImageFormats = ['jpg', 'jpeg', 'png', 'webp'];

  /// Maximum image width in pixels
  static const int maxImageWidth = 2048;

  /// Maximum image height in pixels
  static const int maxImageHeight = 2048;

  // ============================================================================
  // FORM STEP VALIDATION
  // ============================================================================

  /// Total number of steps in page creation
  static const int totalCreationSteps = 4;

  /// Minimum steps required to be completed before submission
  static const int minStepsForSubmission = 3;

  // ============================================================================
  // UI LIMITS
  // ============================================================================

  /// Default timeout for page operations (in seconds)
  static const int defaultOperationTimeoutSeconds = 30;

  /// Maximum number of recent searches to store
  static const int maxRecentSearchesCount = 10;

  /// Debounce delay for search input (in milliseconds)
  static const int searchDebounceDelayMs = 500;

  /// Maximum number of pages to display in lists
  static const int maxPagesDisplayCount = 100;

  // ============================================================================
  // CONTENT LIMITS
  // ============================================================================

  /// Maximum number of ivents to display per page
  static const int maxIventsPerPage = 20;

  /// Maximum number of vibes to display per page
  static const int maxVibesPerPage = 20;

  /// Maximum number of followers to display per page
  static const int maxFollowersPerPage = 50;

  // ============================================================================
  // VALIDATION PATTERNS
  // ============================================================================

  /// Website URL validation pattern
  static const String websiteUrlPattern = r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$';

  /// Page name validation pattern (alphanumeric, spaces, and common symbols)
  static const String pageNamePattern = r'^[a-zA-Z0-9\s\-_\.]+$';

  // ============================================================================
  // ERROR CODES
  // ============================================================================

  /// Page name validation error codes
  static const String errorPageNameRequired = 'PAGE_NAME_REQUIRED';
  static const String errorPageNameTooShort = 'PAGE_NAME_TOO_SHORT';
  static const String errorPageNameTooLong = 'PAGE_NAME_TOO_LONG';
  static const String errorPageNameInvalidFormat = 'PAGE_NAME_INVALID_FORMAT';

  /// Description validation error codes
  static const String errorDescriptionTooLong = 'DESCRIPTION_TOO_LONG';

  /// Website URL validation error codes
  static const String errorWebsiteInvalidFormat = 'WEBSITE_INVALID_FORMAT';
  static const String errorWebsiteTooLong = 'WEBSITE_TOO_LONG';

  /// Location validation error codes
  static const String errorLocationRequired = 'LOCATION_REQUIRED';

  /// Hobby validation error codes
  static const String errorHobbiesRequired = 'HOBBIES_REQUIRED';
  static const String errorTooManyHobbies = 'TOO_MANY_HOBBIES';

  /// Admin validation error codes
  static const String errorAdminsRequired = 'ADMINS_REQUIRED';
  static const String errorTooManyAdmins = 'TOO_MANY_ADMINS';

  /// Image validation error codes
  static const String errorImageTooLarge = 'IMAGE_TOO_LARGE';
  static const String errorImageInvalidFormat = 'IMAGE_INVALID_FORMAT';
  static const String errorImageDimensionsTooLarge = 'IMAGE_DIMENSIONS_TOO_LARGE';
}
