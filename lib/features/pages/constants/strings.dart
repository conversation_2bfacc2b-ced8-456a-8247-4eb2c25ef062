/// String constants for pages feature.
///
/// This class contains all the localized strings used throughout the pages
/// feature, organized by functional areas for better maintainability.
///
/// Follows the architecture guide's constants management patterns with proper
/// private constructor and static const declarations.
class PagesStrings {
  PagesStrings._(); // Private constructor to prevent instantiation

  // ============================================================================
  // PAGE CREATION STEPS
  // ============================================================================

  /// Page creation title
  static const String sayfaOlustur = 'Sayfa Oluştur';

  /// Step 1 title
  static const String adimBir = 'Adım 1';

  /// Step 2 title
  static const String adimIki = 'Adım 2';

  /// Step 3 title
  static const String adimUc = 'Adım 3';

  /// Step 4 title
  static const String adimDort = 'Adım 4';

  /// Page name label
  static const String sayfaAdi = 'Sayfa Adı';

  /// Page description label
  static const String sayfaAciklamasi = 'Sayfa Açıklaması';

  /// Website URL label
  static const String websitesi = 'Website';

  /// Location label
  static const String konum = 'Konum';

  // ============================================================================
  // PAGE SETTINGS
  // ============================================================================

  /// Education page setting
  static const String egitimSayfasi = 'Eğitim Sayfası';

  /// Membership system setting
  static const String uyelikSistemi = 'Üyelik Sistemi';

  /// Has membership text
  static const String uyelikSistemiMevcut = 'Üyelik sistemi mevcut';

  /// Page owner text
  static const String sayfaninSahibisiniz = 'Bu sayfanın sahibisiniz';

  // ============================================================================
  // PAGE DETAIL
  // ============================================================================

  /// Page detail title
  static const String sayfaDetayi = 'Sayfa Detayı';

  /// About tab
  static const String hakkinda = 'Hakkında';

  /// Events tab
  static const String iventler = 'iVentler';

  /// Vibes tab
  static const String vibes = 'Vibes';

  /// Page information section
  static const String sayfaBilgileri = 'Sayfa Bilgileri';

  /// Follow button
  static const String takipEt = 'Takip Et';

  /// Following button
  static const String takipEdiliyor = 'Takip Ediliyor';

  /// Subscribe button
  static const String aboneOl = 'Abone Ol';

  /// Subscribed button
  static const String abonesin = 'Abonesın';

  // ============================================================================
  // HOBBY SELECTION
  // ============================================================================

  /// Select hobbies title
  static const String hobileriSec = 'Hobileri Seç';

  /// Show more hobbies
  static const String dahaFazlaGoster = 'Daha Fazla Göster';

  /// Show less hobbies
  static const String dahaAzGoster = 'Daha Az Göster';

  // ============================================================================
  // ADMIN SELECTION
  // ============================================================================

  /// Select admins title
  static const String adminleriSec = 'Adminleri Seç';

  /// Search admins placeholder
  static const String adminAra = 'Admin Ara';

  /// No admins found
  static const String adminBulunamadi = 'Admin bulunamadı';

  // ============================================================================
  // BUTTONS AND ACTIONS
  // ============================================================================

  /// Continue button
  static const String devamEt = 'Devam Et';

  /// Previous button
  static const String onceki = 'Önceki';

  /// Create page button
  static const String sayfaOlusturButton = 'Sayfa Oluştur';

  /// Save button
  static const String kaydet = 'Kaydet';

  /// Cancel button
  static const String iptal = 'İptal';

  /// Select image button
  static const String resimSec = 'Resim Seç';

  /// Change image button
  static const String resimDegistir = 'Resim Değiştir';

  // ============================================================================
  // VALIDATION MESSAGES
  // ============================================================================

  /// Page name required error
  static const String sayfaAdiGerekli = 'Sayfa adı gereklidir';

  /// Page name too short error
  static const String sayfaAdiCokKisa = 'Sayfa adı çok kısa';

  /// Page name too long error
  static const String sayfaAdiCokUzun = 'Sayfa adı çok uzun';

  /// Description too long error
  static const String aciklamaCokUzun = 'Açıklama çok uzun';

  /// Invalid website URL error
  static const String gecersizWebsite = 'Geçersiz website adresi';

  /// Location required error
  static const String konumGerekli = 'Konum seçimi gereklidir';

  /// At least one hobby required error
  static const String enAzBirHobi = 'En az bir hobi seçmelisiniz';

  // ============================================================================
  // SUCCESS MESSAGES
  // ============================================================================

  /// Page created successfully
  static const String sayfaOlusturuldu = 'Sayfa başarıyla oluşturuldu';

  /// Page updated successfully
  static const String sayfaGuncellendi = 'Sayfa başarıyla güncellendi';

  // ============================================================================
  // ERROR MESSAGES
  // ============================================================================

  /// Page creation failed
  static const String sayfaOlusturulamadi = 'Sayfa oluşturulamadı';

  /// Page update failed
  static const String sayfaGuncellenemedi = 'Sayfa güncellenemedi';

  /// Network error
  static const String agHatasi = 'Ağ bağlantısı hatası';

  /// Unknown error
  static const String bilinmeyenHata = 'Bilinmeyen bir hata oluştu';
}
