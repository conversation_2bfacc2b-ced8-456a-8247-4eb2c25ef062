import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/pages/controllers/page_creation_controller.dart';

class PageCreationStep4 extends StatefulWidget {
  const PageCreationStep4({Key? key}) : super(key: key);

  @override
  State<PageCreationStep4> createState() => _PageCreationStep4State();
}

class _PageCreationStep4State extends State<PageCreationStep4> {
  late final PageCreationController _controller;
  final TextEditingController _adminSearchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controller = Get.find<PageCreationController>();
  }

  @override
  void dispose() {
    _adminSearchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: 'Sayfa Oluştur',
      body: Padding(
        padding: const EdgeInsets.all(AppDimensions.padding20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Progress indicator
            _buildProgressIndicator(),
            const SizedBox(height: AppDimensions.padding32),

            // Content
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Admins section
                    _buildAdminsSection(),
                  ],
                ),
              ),
            ),

            // Navigation buttons
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Row(
      children: [
        _buildProgressDot(0, true),
        _buildProgressLine(true),
        _buildProgressDot(1, true),
        _buildProgressLine(true),
        _buildProgressDot(2, true),
        _buildProgressLine(true),
        _buildProgressDot(3, true),
      ],
    );
  }

  Widget _buildProgressDot(int step, bool isActive) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: isActive ? AppColors.primary : AppColors.lightGrey,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          '${step + 1}',
          style: AppTextStyles.size14Bold.copyWith(
            color: isActive ? AppColors.white : AppColors.textSecondary,
          ),
        ),
      ),
    );
  }

  Widget _buildProgressLine(bool isActive) {
    return Expanded(
      child: Container(
        height: 2,
        color: isActive ? AppColors.primary : AppColors.lightGrey,
      ),
    );
  }

  Widget _buildAdminsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Yöneticiler',
              style: AppTextStyles.size16Bold,
            ),
            const SizedBox(width: AppDimensions.padding4),
            Text(
              '(Opsiyonel)',
              style: AppTextStyles.size14Regular.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.padding8),
        Text(
          'Sayfanızı yönetecek kişileri ekleyin',
          style: AppTextStyles.size14Regular.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.padding16),

        // Selected admins
        Obx(() {
          if (_controller.selectedAdmins.isNotEmpty) {
            return Container(
              padding: const EdgeInsets.all(AppDimensions.padding16),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(color: AppColors.primary.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Seçilen Yöneticiler (${_controller.selectedAdmins.length})',
                    style: AppTextStyles.size14Bold.copyWith(color: AppColors.primary),
                  ),
                  const SizedBox(height: AppDimensions.padding12),
                  ...(_controller.selectedAdmins.map((admin) {
                    return Container(
                      margin: const EdgeInsets.only(bottom: AppDimensions.padding8),
                      padding: const EdgeInsets.all(AppDimensions.padding12),
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                        border: Border.all(color: AppColors.lightGrey),
                      ),
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: 16,
                            backgroundColor: AppColors.primary,
                            child: admin.avatarUrl != null
                                ? ClipOval(
                                    child: Image.network(
                                      admin.avatarUrl!,
                                      width: 32,
                                      height: 32,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error, stackTrace) {
                                        return const Icon(
                                          Icons.person,
                                          color: AppColors.white,
                                          size: 20,
                                        );
                                      },
                                    ),
                                  )
                                : const Icon(
                                    Icons.person,
                                    color: AppColors.white,
                                    size: 20,
                                  ),
                          ),
                          const SizedBox(width: AppDimensions.padding12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  admin.username,
                                  style: AppTextStyles.size14Bold,
                                ),
                                if (admin.university != null)
                                  Text(
                                    admin.university!,
                                    style: AppTextStyles.size12Regular.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close, color: AppColors.textSecondary),
                            onPressed: () => _controller.toggleAdminSelection(admin),
                          ),
                        ],
                      ),
                    );
                  }).toList()),
                ],
              ),
            );
          }
          return const SizedBox.shrink();
        }),

        if (_controller.selectedAdmins.isNotEmpty) const SizedBox(height: AppDimensions.padding16),

        // Admin search
        TextField(
          controller: _adminSearchController,
          onChanged: (value) => _controller.searchModeratorsForPageCreation(value),
          decoration: InputDecoration(
            hintText: 'Yönetici ara...',
            hintStyle: AppTextStyles.size14Regular.copyWith(
              color: AppColors.textSecondary,
            ),
            prefixIcon: const Icon(Icons.search, color: AppColors.textSecondary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.lightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.lightGrey),
            ),
            contentPadding: const EdgeInsets.all(AppDimensions.padding16),
          ),
          style: AppTextStyles.size14Regular,
        ),
        const SizedBox(height: AppDimensions.padding16),

        // Admin search results
        Obx(() {
          if (_controller.isSearchingUsers) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(AppDimensions.padding16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          if (_controller.users.isEmpty && _adminSearchController.text.isNotEmpty) {
            return Container(
              padding: const EdgeInsets.all(AppDimensions.padding16),
              child: Text(
                'Kullanıcı bulunamadı',
                style: AppTextStyles.size14Regular.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            );
          }

          if (_controller.users.isNotEmpty) {
            return Container(
              constraints: const BoxConstraints(maxHeight: 200),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _controller.users.length,
                itemBuilder: (context, index) {
                  final user = _controller.users[index];
                  final isSelected = _controller.isAdminSelected(user);

                  return Container(
                    margin: const EdgeInsets.only(bottom: AppDimensions.padding8),
                    child: ListTile(
                      leading: CircleAvatar(
                        radius: 20,
                        backgroundColor: AppColors.primary,
                        child: user.avatarUrl != null
                            ? ClipOval(
                                child: Image.network(
                                  user.avatarUrl!,
                                  width: 40,
                                  height: 40,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Icon(
                                      Icons.person,
                                      color: AppColors.white,
                                    );
                                  },
                                ),
                              )
                            : const Icon(
                                Icons.person,
                                color: AppColors.white,
                              ),
                      ),
                      title: Text(
                        user.username,
                        style: AppTextStyles.size14Medium,
                      ),
                      subtitle: user.university != null
                          ? Text(
                              user.university!,
                              style: AppTextStyles.size12Regular.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            )
                          : null,
                      trailing: Checkbox(
                        value: isSelected,
                        onChanged: (value) => _controller.toggleAdminSelection(user),
                        activeColor: AppColors.primary,
                      ),
                      onTap: () => _controller.toggleAdminSelection(user),
                    ),
                  );
                },
              ),
            );
          }

          return const SizedBox.shrink();
        }),
      ],
    );
  }

  Widget _buildNavigationButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Get.back(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
              side: const BorderSide(color: AppColors.lightGrey),
            ),
            child: Text(
              'Geri',
              style: AppTextStyles.size16Medium.copyWith(color: AppColors.textSecondary),
            ),
          ),
        ),
        const SizedBox(width: AppDimensions.padding16),
        Expanded(
          flex: 2,
          child: Obx(() => ElevatedButton(
                onPressed: _controller.isLoading() ? null : () => _controller.createPage(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  disabledBackgroundColor: AppColors.lightGrey,
                  padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
                child: _controller.isLoading()
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          color: AppColors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : Text(
                        'Sayfayı Oluştur',
                        style: AppTextStyles.size16Bold.copyWith(color: AppColors.white),
                      ),
              )),
        ),
      ],
    );
  }
}
