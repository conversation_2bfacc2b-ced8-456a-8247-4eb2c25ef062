import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/pages/controllers/page_creation_controller.dart';
import 'package:ivent_app/routes/page_creation.dart';

/// First step of page creation process - basic information
///
/// This page allows users to enter the page name and select a thumbnail image.
/// It includes form validation and progress indication.
///
/// Follows the architecture guide's page structure patterns with proper
/// lifecycle management and resource cleanup.
class PageCreationStep1 extends StatefulWidget {
  const PageCreationStep1({Key? key}) : super(key: key);

  @override
  State<PageCreationStep1> createState() => _PageCreationStep1State();
}

/// Private state class for the page creation step 1
class _PageCreationStep1State extends State<PageCreationStep1> {
  late final PageCreationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<PageCreationController>();
  }

  @override
  void dispose() {
    // Controller cleanup is handled by the binding
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: 'Sayfa Oluştur',
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.padding20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Progress indicator
            _buildProgressIndicator(),
            const SizedBox(height: AppDimensions.padding32),

            // Page thumbnail section
            _buildThumbnailSection(),
            const SizedBox(height: AppDimensions.padding24),

            // Page name section
            _buildPageNameSection(),

            const SizedBox(height: AppDimensions.padding32),

            // Continue button
            _buildContinueButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Row(
      children: [
        _buildProgressDot(0, true),
        _buildProgressLine(false),
        _buildProgressDot(1, false),
        _buildProgressLine(false),
        _buildProgressDot(2, false),
      ],
    );
  }

  Widget _buildProgressDot(int step, bool isActive) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: isActive ? AppColors.primary : AppColors.lightGrey,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          '${step + 1}',
          style: AppTextStyles.size14Bold.copyWith(
            color: isActive ? AppColors.white : AppColors.textSecondary,
          ),
        ),
      ),
    );
  }

  Widget _buildProgressLine(bool isActive) {
    return Expanded(
      child: Container(
        height: 2,
        color: isActive ? AppColors.primary : AppColors.lightGrey,
      ),
    );
  }

  Widget _buildThumbnailSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sayfa Fotoğrafı',
          style: AppTextStyles.size16Bold,
        ),
        const SizedBox(height: AppDimensions.padding8),
        Text(
          'Sayfanızı temsil edecek bir fotoğraf seçin (opsiyonel)',
          style: AppTextStyles.size14Regular.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.padding16),
        GestureDetector(
          onTap: _controller.selectThumbnailFromGallery,
          child: Obx(() => Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  color: AppColors.lightGrey.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: AppColors.lightGrey,
                    style: BorderStyle.solid,
                    width: 1,
                  ),
                ),
                child: _controller.selectedThumbnail != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                        child: Image.file(
                          _controller.selectedThumbnail!,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.add_photo_alternate_outlined,
                            size: 48,
                            color: AppColors.textSecondary,
                          ),
                          const SizedBox(height: AppDimensions.padding8),
                          Text(
                            'Fotoğraf Seç',
                            style: AppTextStyles.size14Medium.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
              )),
        ),
      ],
    );
  }

  Widget _buildPageNameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Sayfa Adı',
              style: AppTextStyles.size16Bold,
            ),
            Text(
              ' *',
              style: AppTextStyles.size16Bold.copyWith(color: AppColors.error),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.padding8),
        Text(
          'Sayfanızın görüneceği ismi girin',
          style: AppTextStyles.size14Regular.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.padding16),
        TextField(
          controller: _controller.pageNameController,
          onChanged: (value) {
            // Trigger rebuild for the continue button
            setState(() {});
          },
          decoration: InputDecoration(
            hintText: 'Örn: İTÜ Bilgisayar Topluluğu',
            hintStyle: AppTextStyles.size14Regular.copyWith(
              color: AppColors.textSecondary,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.lightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color:
                    _controller.canProceedFromStep(0) ? AppColors.primary.withValues(alpha: 0.5) : AppColors.lightGrey,
              ),
            ),
            contentPadding: const EdgeInsets.all(AppDimensions.padding16),
          ),
          maxLength: 50,
          style: AppTextStyles.size14Regular,
        ),
      ],
    );
  }

  Widget _buildContinueButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _controller.canProceedFromStep(0) ? () => Get.toNamed(PageCreationRoutes.STEP2) : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          disabledBackgroundColor: AppColors.lightGrey,
          padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
        child: Text(
          'Devam Et',
          style: AppTextStyles.size16Bold.copyWith(
            color: _controller.canProceedFromStep(0) ? AppColors.white : AppColors.textSecondary,
          ),
        ),
      ),
    );
  }
}
