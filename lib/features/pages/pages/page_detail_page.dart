import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/pages/controllers/page_detail_controller.dart';

/// Page detail page displaying comprehensive information about a page
///
/// Shows page information, details, created ivents, vibes, and follow/subscribe options.
/// Uses backend APIs for all data operations and follows the project's design language.
///
/// Follows the architecture guide's page structure patterns with proper
/// lifecycle management and resource cleanup.
class PageDetailPage extends StatefulWidget {
  /// The page ID to display details for
  final String pageId;

  /// Optional page name for display purposes
  final String? pageName;

  const PageDetailPage({
    Key? key,
    required this.pageId,
    this.pageName,
  }) : super(key: key);

  @override
  State<PageDetailPage> createState() => _PageDetailPageState();
}

/// Private state class for the page detail page
class _PageDetailPageState extends State<PageDetailPage> with SingleTickerProviderStateMixin {
  late final PageDetailController _controller;
  late final TabController _tabController;

  @override
  void initState() {
    super.initState();

    // Initialize controller with proper dependency injection
    _controller = Get.put(
      PageDetailController(Get.find(), widget.pageId),
      tag: widget.pageId,
    );

    // Initialize tab controller
    _tabController = TabController(length: 3, vsync: this);

    // Initialize controller after widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _controller.initController();
    });
  }

  @override
  void dispose() {
    // Dispose resources in reverse order
    _tabController.dispose();
    Get.delete<PageDetailController>(tag: widget.pageId);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (_controller.isLoading() && _controller.pageInfo == null) {
        return IaScaffold.loading();
      }

      return IaScaffold.noSearch(
        title: _controller.pageInfo?.pageName ?? widget.pageName ?? 'Sayfa Detayı',
        trailing: _buildMoreButton(),
        body: Column(
          children: [
            // Page header
            _buildPageHeader(),

            // Tab bar
            _buildTabBar(),

            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildAboutTab(),
                  _buildIventsTab(),
                  _buildVibesTab(),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildMoreButton() {
    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'block':
            _showBlockConfirmation();
            break;
          case 'share':
            _sharePageWithLoading();
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'share',
          child: Row(
            children: [
              Icon(Icons.share_outlined, size: 20),
              SizedBox(width: 8),
              Text('Paylaş'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'block',
          child: Row(
            children: [
              Icon(Icons.block_outlined, size: 20, color: Colors.red),
              SizedBox(width: 8),
              Text('Engelle', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
      child: const Icon(Icons.more_vert),
    );
  }

  Widget _buildPageHeader() {
    return Obx(() {
      final pageInfo = _controller.pageInfo;
      if (pageInfo == null) return const SizedBox.shrink();

      return Container(
        padding: const EdgeInsets.all(AppDimensions.padding20),
        child: Column(
          children: [
            // Page thumbnail and basic info
            Row(
              children: [
                // Thumbnail
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: AppColors.lightGrey.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    image: pageInfo.thumbnailUrl != null
                        ? DecorationImage(
                            image: NetworkImage(pageInfo.thumbnailUrl!),
                            fit: BoxFit.cover,
                          )
                        : null,
                  ),
                  child: pageInfo.thumbnailUrl == null
                      ? const Icon(
                          Icons.business_outlined,
                          size: 40,
                          color: AppColors.textSecondary,
                        )
                      : null,
                ),
                const SizedBox(width: AppDimensions.padding16),

                // Page info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        pageInfo.pageName,
                        style: AppTextStyles.size16Bold,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: AppDimensions.padding4),

                      // Stats row
                      Row(
                        children: [
                          Text(
                            '${pageInfo.followerCount} takipçi',
                            style: AppTextStyles.size14Regular.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                          const SizedBox(width: AppDimensions.padding12),
                          Text(
                            '${pageInfo.createdIventCount} iVent',
                            style: AppTextStyles.size14Regular.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),

                      // Tags
                      if (pageInfo.tagIds.isNotEmpty) ...[
                        const SizedBox(height: AppDimensions.padding8),
                        Wrap(
                          spacing: AppDimensions.padding4,
                          children: pageInfo.tagIds.take(3).map((tagId) {
                            return Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppDimensions.padding8,
                                vertical: AppDimensions.padding4,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                              ),
                              child: Text(
                                '#$tagId',
                                style: AppTextStyles.size12Regular.copyWith(
                                  color: AppColors.primary,
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppDimensions.padding20),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _controller.toggleFollow,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _controller.isFollowing ? AppColors.lightGrey : AppColors.primary,
                      foregroundColor: _controller.isFollowing ? AppColors.textSecondary : AppColors.white,
                      padding: const EdgeInsets.symmetric(
                        vertical: AppDimensions.padding12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                      ),
                    ),
                    child: Text(
                      _controller.isFollowing ? 'Takip Ediliyor' : 'Takip Et',
                      style: AppTextStyles.size14Bold,
                    ),
                  ),
                ),
                const SizedBox(width: AppDimensions.padding12),
                Expanded(
                  child: OutlinedButton(
                    onPressed: _controller.toggleSubscription,
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(
                        color: _controller.isSubscribed ? AppColors.primary : AppColors.lightGrey,
                      ),
                      padding: const EdgeInsets.symmetric(
                        vertical: AppDimensions.padding12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                      ),
                    ),
                    child: Text(
                      _controller.isSubscribed ? 'Bildirimleri Kapat' : 'Bildirimleri Aç',
                      style: AppTextStyles.size14Bold.copyWith(
                        color: _controller.isSubscribed ? AppColors.primary : AppColors.textSecondary,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.lightGrey.withOpacity(0.3),
            width: 1,
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        indicatorColor: AppColors.primary,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        labelStyle: AppTextStyles.size14Bold,
        unselectedLabelStyle: AppTextStyles.size14Regular,
        tabs: const [
          Tab(text: 'Hakkında'),
          Tab(text: 'iVentler'),
          Tab(text: 'Vibes'),
        ],
      ),
    );
  }

  Widget _buildAboutTab() {
    return Obx(() {
      final pageDetails = _controller.pageDetails;

      return SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.padding20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Description
            if (pageDetails?.description != null) ...[
              Text(
                'Açıklama',
                style: AppTextStyles.size16Bold,
              ),
              const SizedBox(height: AppDimensions.padding8),
              Text(
                pageDetails!.description!,
                style: AppTextStyles.size14Regular,
              ),
              const SizedBox(height: AppDimensions.padding20),
            ],

            // Website
            if (pageDetails?.websiteUrl != null) ...[
              Text(
                'Web Sitesi',
                style: AppTextStyles.size16Bold,
              ),
              const SizedBox(height: AppDimensions.padding8),
              GestureDetector(
                onTap: () {
                  // Open website URL
                  debugPrint('Opening website: ${pageDetails.websiteUrl}');
                },
                child: Text(
                  pageDetails!.websiteUrl!,
                  style: AppTextStyles.size14Regular.copyWith(
                    color: AppColors.primary,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
              const SizedBox(height: AppDimensions.padding20),
            ],

            // Location
            if (pageDetails?.locationAdress != null) ...[
              Text(
                'Konum',
                style: AppTextStyles.size16Bold,
              ),
              const SizedBox(height: AppDimensions.padding8),
              Row(
                children: [
                  const Icon(
                    Icons.location_on_outlined,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: AppDimensions.padding4),
                  Expanded(
                    child: Text(
                      pageDetails!.locationAdress!,
                      style: AppTextStyles.size14Regular.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.padding20),
            ],

            // Page type info
            if (_controller.pageInfo != null) ...[
              Text(
                'Sayfa Bilgileri',
                style: AppTextStyles.size16Bold,
              ),
              const SizedBox(height: AppDimensions.padding12),
              if (_controller.pageInfo!.haveMembership) ...[
                Row(
                  children: [
                    const Icon(
                      Icons.groups_outlined,
                      size: 16,
                      color: AppColors.primary,
                    ),
                    const SizedBox(width: AppDimensions.padding8),
                    Text(
                      'Üyelik sistemi mevcut',
                      style: AppTextStyles.size14Regular,
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.padding8),
              ],
              if (_controller.pageInfo!.isFirstPerson) ...[
                Row(
                  children: [
                    const Icon(
                      Icons.admin_panel_settings_outlined,
                      size: 16,
                      color: AppColors.primary,
                    ),
                    const SizedBox(width: AppDimensions.padding8),
                    Text(
                      'Bu sayfanın sahibisiniz',
                      style: AppTextStyles.size14Regular,
                    ),
                  ],
                ),
              ],
            ],
          ],
        ),
      );
    });
  }

  Widget _buildIventsTab() {
    return Obx(() {
      final pageIvents = _controller.pageIvents;

      if (pageIvents == null || pageIvents.ivents.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.event_busy_outlined,
                size: 64,
                color: AppColors.textSecondary,
              ),
              const SizedBox(height: AppDimensions.padding16),
              Text(
                'Henüz iVent oluşturulmamış',
                style: AppTextStyles.size16Regular.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(AppDimensions.padding16),
        itemCount: pageIvents.ivents.length,
        itemBuilder: (context, index) {
          final ivent = pageIvents.ivents[index];
          return Card(
            margin: const EdgeInsets.only(bottom: AppDimensions.padding12),
            child: ListTile(
              leading: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppColors.lightGrey.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  image: ivent.thumbnailUrl != null
                      ? DecorationImage(
                          image: NetworkImage(ivent.thumbnailUrl!),
                          fit: BoxFit.cover,
                        )
                      : null,
                ),
                child: ivent.thumbnailUrl == null
                    ? const Icon(
                        Icons.event_outlined,
                        color: AppColors.textSecondary,
                      )
                    : null,
              ),
              title: Text(
                ivent.iventName,
                style: AppTextStyles.size14Bold,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (ivent.locationName.isNotEmpty) ...[
                    const SizedBox(height: AppDimensions.padding4),
                    Row(
                      children: [
                        const Icon(
                          Icons.location_on_outlined,
                          size: 12,
                          color: AppColors.textSecondary,
                        ),
                        const SizedBox(width: AppDimensions.padding4),
                        Expanded(
                          child: Text(
                            ivent.locationName,
                            style: AppTextStyles.size12Regular.copyWith(
                              color: AppColors.textSecondary,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                  const SizedBox(height: AppDimensions.padding4),
                  Text(
                    'Katılımcı sayısı mevcut değil',
                    style: AppTextStyles.size12Regular.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              onTap: () => _controller.navigateToIventDetail(ivent.iventId),
            ),
          );
        },
      );
    });
  }

  Widget _buildVibesTab() {
    return Obx(() {
      final pageVibes = _controller.pageVibes;

      if (pageVibes == null || pageVibes.vibeFolders.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.music_off_outlined,
                size: 64,
                color: AppColors.textSecondary,
              ),
              const SizedBox(height: AppDimensions.padding16),
              Text(
                'Henüz Vibe paylaşılmamış',
                style: AppTextStyles.size16Regular.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        );
      }

      return GridView.builder(
        padding: const EdgeInsets.all(AppDimensions.padding16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: AppDimensions.padding12,
          mainAxisSpacing: AppDimensions.padding12,
          childAspectRatio: 0.8,
        ),
        itemCount: pageVibes.vibeFolders.length,
        itemBuilder: (context, index) {
          final vibeFolder = pageVibes.vibeFolders[index];
          return GestureDetector(
            onTap: () => _controller.navigateToVibeDetail(vibeFolder.vibeFolderId),
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.lightGrey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                image: vibeFolder.thumbnailUrl != null
                    ? DecorationImage(
                        image: NetworkImage(vibeFolder.thumbnailUrl!),
                        fit: BoxFit.cover,
                      )
                    : null,
              ),
              child: vibeFolder.thumbnailUrl == null
                  ? const Center(
                      child: Icon(
                        Icons.music_note_outlined,
                        size: 40,
                        color: AppColors.textSecondary,
                      ),
                    )
                  : Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withOpacity(0.6),
                          ],
                        ),
                      ),
                      child: Align(
                        alignment: Alignment.bottomLeft,
                        child: Padding(
                          padding: const EdgeInsets.all(AppDimensions.padding12),
                          child: Text(
                            vibeFolder.iventName,
                            style: AppTextStyles.size14Bold.copyWith(
                              color: Colors.white,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ),
            ),
          );
        },
      );
    });
  }

  void _showBlockConfirmation() {
    Get.dialog(
      AlertDialog(
        title: const Text('Sayfayı Engelle'),
        content: const Text(
          'Bu sayfayı engellemek istediğinizden emin misiniz? '
          'Engellenen sayfalar artık görünmeyecektir.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('İptal'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _controller.blockPage();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Engelle'),
          ),
        ],
      ),
    );
  }

  void _sharePageWithLoading() {
    final pageInfo = _controller.pageInfo;
    if (pageInfo == null) return;

    Get.snackbar(
      'Bilgi',
      '${pageInfo.pageName} sayfası paylaşılıyor...',
      snackPosition: SnackPosition.BOTTOM,
    );

    // Here you would implement actual sharing functionality
    debugPrint('Sharing page: ${pageInfo.pageName}');
  }
}
