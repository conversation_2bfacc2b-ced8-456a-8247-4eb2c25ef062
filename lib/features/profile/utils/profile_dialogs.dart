import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/profile/constants/profile_constants.dart';

class ProfileDialogs {
  static void showErrorDialog(String title, String message) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColors.background,
        title: Text(
          title,
          style: AppTextStyles.size16Bold.copyWith(color: AppColors.error),
        ),
        content: Text(
          message,
          style: AppTextStyles.size14Regular,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'Tamam',
              style: AppTextStyles.size14Medium.copyWith(color: AppColors.primary),
            ),
          ),
        ],
      ),
    );
  }

  static void showGenderPicker({
    required String currentGender,
    required Function(String) onGenderSelected,
  }) {
    Get.bottomSheet(
      Container(
        decoration: const BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppDimensions.radiusL),
            topRight: Radius.circular(AppDimensions.radiusL),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(AppDimensions.padding16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    ProfileConstants.genderLabel,
                    style: AppTextStyles.size16Bold,
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            ...ProfileConstants.genderOptions.entries.map((entry) {
              final isSelected = currentGender == entry.key;
              return ListTile(
                title: Text(
                  entry.value,
                  style: AppTextStyles.size16Regular.copyWith(
                    color: isSelected ? AppColors.primary : AppColors.textPrimary,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                trailing: isSelected
                    ? const Icon(Icons.check, color: AppColors.primary)
                    : null,
                onTap: () {
                  onGenderSelected(entry.key);
                  Get.back();
                },
              );
            }).toList(),
            const SizedBox(height: AppDimensions.padding16),
          ],
        ),
      ),
    );
  }

  static void showConfirmationDialog({
    required String title,
    required String message,
    required String confirmText,
    required VoidCallback onConfirm,
    String? cancelText,
    VoidCallback? onCancel,
  }) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColors.background,
        title: Text(
          title,
          style: AppTextStyles.size16Bold,
        ),
        content: Text(
          message,
          style: AppTextStyles.size14Regular,
        ),
        actions: [
          TextButton(
            onPressed: onCancel ?? () => Get.back(),
            child: Text(
              cancelText ?? ProfileConstants.cancelText,
              style: AppTextStyles.size14Medium.copyWith(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              onConfirm();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
            ),
            child: Text(
              confirmText,
              style: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
            ),
          ),
        ],
      ),
    );
  }

  static void showSuccessDialog({
    required String title,
    required String message,
    VoidCallback? onOk,
  }) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColors.background,
        title: Row(
          children: [
            const Icon(Icons.check_circle, color: AppColors.success, size: 24),
            const SizedBox(width: AppDimensions.padding8),
            Text(
              title,
              style: AppTextStyles.size16Bold.copyWith(color: AppColors.success),
            ),
          ],
        ),
        content: Text(
          message,
          style: AppTextStyles.size14Regular,
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Get.back();
              onOk?.call();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: AppColors.white,
            ),
            child: Text(
              'Tamam',
              style: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
            ),
          ),
        ],
      ),
    );
  }
}
