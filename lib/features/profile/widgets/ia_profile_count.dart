import 'package:flutter/widgets.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';

/// A widget for displaying profile statistics with count and label.
///
/// This widget provides a consistent interface for showing profile metrics
/// like ivent count, follower count, friend count, etc. It displays a number
/// with a descriptive label below it and supports tap interactions.
///
/// Follows the architecture guide's widget naming conventions with "Ia" prefix
/// and proper widget structure patterns.
class IaProfileCount extends StatelessWidget {
  /// The numeric count to display
  final int count;

  /// The descriptive text label
  final String text;

  /// Optional callback for tap interactions
  final VoidCallback? onTap;

  const IaProfileCount._({
    required this.count,
    required this.text,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Text(count.toString(), style: AppTextStyles.size16Bold),
          Text(text, style: AppTextStyles.size12MediumTextSecondary),
        ],
      ),
    );
  }

  /// Factory constructor for ivent count display
  static IaProfileCount iventCount(ProfileController controller) {
    final pageContent = controller.userInfoController.userPageInfo!;
    return IaProfileCount._(
      count: pageContent.iventCount,
      text: "iVent'ler",
      onTap: () => controller.iventsController.goToIventsPage(),
    );
  }

  /// Factory constructor for friend count display
  static IaProfileCount friendCount(ProfileController controller) {
    final pageContent = controller.userInfoController.userPageInfo!;
    return IaProfileCount._(
      count: pageContent.friendCount,
      text: 'Arkadaşlar',
      onTap: () => controller.socialController.goToFriendsPage(),
    );
  }

  /// Factory constructor for follower count display
  static IaProfileCount followerCount(ProfileController controller) {
    final pageContent = controller.userInfoController.userPageInfo!;
    return IaProfileCount._(
      count: pageContent.followerCount,
      text: 'Takipçiler',
      onTap: () => controller.socialController.goToCreatorFollowerPage(),
    );
  }
}
