import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';

class LevelStepsHeader extends StatelessWidget {
  final String title;
  final String description;

  const LevelStepsHeader({
    Key? key,
    required this.title,
    required this.description,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.size20Bold,
        ),
        const SizedBox(height: AppDimensions.padding8),
        Text(
          description,
          style: AppTextStyles.size14Regular.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
}
