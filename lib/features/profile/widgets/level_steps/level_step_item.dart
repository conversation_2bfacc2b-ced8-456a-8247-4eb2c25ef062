import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/profile_level_steps_controller.dart';

class LevelStepItem extends StatelessWidget {
  final LevelStep step;
  final int index;
  final Function(LevelStep) onStepAction;

  const LevelStepItem({
    Key? key,
    required this.step,
    required this.index,
    required this.onStepAction,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      decoration: BoxDecoration(
        color: step.isCompleted ? AppColors.primary.withOpacity(0.1) : AppColors.grey50,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: step.isCompleted ? AppColors.primary : AppColors.grey200,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: step.isCompleted ? AppColors.primary : AppColors.grey300,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: step.isCompleted
                  ? const Icon(
                      Icons.check,
                      color: AppColors.white,
                      size: 20,
                    )
                  : Text(
                      '${index + 1}',
                      style: AppTextStyles.size14Bold.copyWith(
                        color: AppColors.white,
                      ),
                    ),
            ),
          ),
          const SizedBox(width: AppDimensions.padding12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  step.title,
                  style: AppTextStyles.size16Bold.copyWith(
                    color: step.isCompleted ? AppColors.primary : AppColors.textPrimary,
                  ),
                ),
                if (step.description.isNotEmpty) ...[
                  const SizedBox(height: AppDimensions.padding4),
                  Text(
                    step.description,
                    style: AppTextStyles.size14Regular.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (step.isActionable && !step.isCompleted)
            GestureDetector(
              onTap: () => onStepAction(step),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.padding12,
                  vertical: AppDimensions.padding8,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Text(
                  step.actionText,
                  style: AppTextStyles.size12Bold.copyWith(
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
