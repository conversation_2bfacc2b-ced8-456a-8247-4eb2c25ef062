import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/profile/constants/drawer_constants.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/profile_side_menu_controller.dart';

class ProfileDrawerPagesSection extends StatelessWidget {
  const ProfileDrawerPagesSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfileSideMenuController>();

    return Obx(() {
      final pages = controller.userPages;

      if (pages == null) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
          child: Text(
            DrawerConstants.pagesLoadingText,
            style: AppTextStyles.size14Regular.copyWith(color: AppColors.textSecondary),
          ),
        );
      }

      return Container(
        margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              DrawerConstants.myPagesText,
              style: AppTextStyles.size16Bold.copyWith(color: AppColors.primary),
            ),
            const SizedBox(height: AppDimensions.padding12),
            _buildCreatePageItem(
              onTap: () => controller.onCreatePageTap(),
            ),
            if (pages.pages.isNotEmpty) ...[
              const SizedBox(height: AppDimensions.padding8),
              ...pages.pages
                  .map((page) => _buildPageItem(
                        pageId: page.pageId,
                        pageName: page.pageName,
                        pageImageUrl: page.thumbnailUrl,
                        membershipStatus: page.pageMembershipStatus.toString().split('.').last,
                        onTap: () => controller.onPageTap(page.pageId, page.pageName),
                      ))
                  .toList(),
            ],
          ],
        ),
      );
    });
  }

  Widget _buildCreatePageItem({
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.padding8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: AppColors.primary, width: 2),
          ),
          child: const Icon(
            Icons.add,
            color: AppColors.primary,
            size: 24,
          ),
        ),
        title: Text(
          DrawerConstants.createPageText,
          style: AppTextStyles.size14Medium.copyWith(color: AppColors.primary),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          DrawerConstants.createPageSubtext,
          style: AppTextStyles.size12Regular.copyWith(color: AppColors.textSecondary),
        ),
        onTap: onTap,
        dense: true,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.padding16,
          vertical: AppDimensions.padding4,
        ),
      ),
    );
  }

  Widget _buildPageItem({
    required String pageId,
    required String pageName,
    required String? pageImageUrl,
    required String membershipStatus,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.padding8),
      child: ListTile(
        leading: CircleAvatar(
          radius: 20,
          backgroundImage: pageImageUrl != null ? NetworkImage(pageImageUrl) : null,
          backgroundColor: AppColors.lightGrey,
          child: pageImageUrl == null
              ? Text(
                  pageName.substring(0, 1).toUpperCase(),
                  style: AppTextStyles.size14Bold,
                )
              : null,
        ),
        title: Text(
          pageName,
          style: AppTextStyles.size14Medium,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          DrawerConstants.getMembershipStatusText(membershipStatus),
          style: AppTextStyles.size12Regular.copyWith(color: AppColors.textSecondary),
        ),
        onTap: onTap,
        dense: true,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.padding16,
          vertical: AppDimensions.padding4,
        ),
      ),
    );
  }
}
