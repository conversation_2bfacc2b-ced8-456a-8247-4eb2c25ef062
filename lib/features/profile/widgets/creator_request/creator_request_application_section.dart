import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/profile/constants/creator_request_constants.dart';

class CreatorRequestApplicationSection extends StatelessWidget {
  final bool canApply;
  final bool isApplicationSubmitted;
  final bool isLoading;

  const CreatorRequestApplicationSection({
    super.key,
    required this.canApply,
    required this.isApplicationSubmitted,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          CreatorRequestConstants.applicationTitle,
          style: AppTextStyles.size16Bold,
        ),
        const SizedBox(height: AppDimensions.padding16),
        if (canApply) ...[
          Container(
            padding: const EdgeInsets.all(AppDimensions.padding16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  CreatorRequestConstants.applicationProcessTitle,
                  style: AppTextStyles.size14Medium,
                ),
                const SizedBox(height: AppDimensions.padding8),
                Text(
                  CreatorRequestConstants.applicationProcessSteps,
                  style: AppTextStyles.size14Regular.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppDimensions.padding24),
        ],
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: canApply ? () => Get.toNamed('/creator_request_step2') : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: canApply ? AppColors.primary : Colors.grey,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            child: isLoading
                ? const SizedBox(
                    height: CreatorRequestConstants.loadingIndicatorSize,
                    width: CreatorRequestConstants.loadingIndicatorSize,
                    child: CircularProgressIndicator(
                      strokeWidth: CreatorRequestConstants.loadingStrokeWidth,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    _getButtonText(),
                    style: AppTextStyles.size16Bold.copyWith(
                      color: Colors.white,
                    ),
                  ),
          ),
        ),
        if (!canApply && !isApplicationSubmitted) ...[
          const SizedBox(height: CreatorRequestConstants.cannotApplyTopPadding),
          Text(
            CreatorRequestConstants.cannotApplyMessage,
            style: AppTextStyles.size12Regular.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  String _getButtonText() {
    if (isApplicationSubmitted) {
      return CreatorRequestConstants.buttonSubmitted;
    } else if (canApply) {
      return CreatorRequestConstants.buttonContinue;
    } else {
      return CreatorRequestConstants.buttonCannotApply;
    }
  }
}
