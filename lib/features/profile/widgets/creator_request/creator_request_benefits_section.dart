import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/profile/constants/creator_request_constants.dart';

class CreatorRequestBenefitsSection extends StatelessWidget {
  final List<String> creatorBenefits;
  final VoidCallback onShowBenefitsDialog;

  const CreatorRequestBenefitsSection({
    super.key,
    required this.creatorBenefits,
    required this.onShowBenefitsDialog,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              CreatorRequestConstants.benefitsTitle,
              style: AppTextStyles.size16Bold,
            ),
            TextButton(
              onPressed: onShowBenefitsDialog,
              child: Text(
                CreatorRequestConstants.benefitsDetailsButton,
                style: AppTextStyles.size14Regular.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.padding8),
        ...creatorBenefits
            .take(4)
            .map((benefit) => Padding(
                  padding: const EdgeInsets.only(bottom: CreatorRequestConstants.benefitItemSpacing),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: CreatorRequestConstants.benefitIconSize,
                      ),
                      const SizedBox(width: CreatorRequestConstants.benefitItemSpacing),
                      Expanded(
                        child: Text(
                          benefit,
                          style: AppTextStyles.size14Regular,
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
        if (creatorBenefits.length > 4)
          Padding(
            padding: const EdgeInsets.only(top: CreatorRequestConstants.moreTextTopPadding),
            child: Text(
              CreatorRequestConstants.benefitsMoreText.replaceFirst('%d', '${creatorBenefits.length - 4}'),
              style: AppTextStyles.size12Regular.copyWith(
                color: AppColors.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }
}
