import 'package:flutter/material.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';

class UniversitySearchWidget extends StatelessWidget {
  final TextEditingController universitySearchController;
  final List<UniversityItem> universities;
  final UniversityItem? selectedUniversity;
  final Function(String) onSearchChanged;
  final Function(UniversityItem) onUniversitySelected;
  final VoidCallback? onSelectedUniversityRemoved;

  const UniversitySearchWidget({
    Key? key,
    required this.universitySearchController,
    required this.universities,
    required this.selectedUniversity,
    required this.onSearchChanged,
    required this.onUniversitySelected,
    this.onSelectedUniversityRemoved,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Üniversite *',
          style: AppTextStyles.size14Medium,
        ),
        const SizedBox(height: AppDimensions.padding8),
        _buildSearchField(),
        _buildSelectedUniversity(),
        _buildSearchResults(),
      ],
    );
  }

  Widget _buildSearchField() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.grey100,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: selectedUniversity != null ? AppColors.primary.withValues(alpha: 0.3) : AppColors.grey300,
        ),
      ),
      child: TextField(
        controller: universitySearchController,
        decoration: InputDecoration(
          hintText: 'Üniversite ara...',
          hintStyle: AppTextStyles.size16Regular.copyWith(
            color: AppColors.textSecondary,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(AppDimensions.padding16),
          suffixIcon: const Icon(
            Icons.search,
            color: AppColors.grey500,
          ),
        ),
        style: AppTextStyles.size16Regular,
        onChanged: onSearchChanged,
      ),
    );
  }

  Widget _buildSelectedUniversity() {
    if (selectedUniversity == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: AppDimensions.padding8),
      padding: const EdgeInsets.all(AppDimensions.padding12),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.school,
            color: AppColors.primary,
            size: 20,
          ),
          const SizedBox(width: AppDimensions.padding8),
          Expanded(
            child: Text(
              selectedUniversity!.universityName,
              style: AppTextStyles.size14Medium.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
          if (onSelectedUniversityRemoved != null)
            GestureDetector(
              onTap: onSelectedUniversityRemoved,
              child: const Icon(
                Icons.close,
                color: AppColors.primary,
                size: 20,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (universities.isEmpty || selectedUniversity != null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: AppDimensions.padding8),
      constraints: const BoxConstraints(maxHeight: 200),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(color: AppColors.grey300),
      ),
      child: ListView.separated(
        shrinkWrap: true,
        itemCount: universities.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final university = universities[index];
          return ListTile(
            dense: true,
            leading: const Icon(
              Icons.school,
              color: AppColors.primary,
              size: 20,
            ),
            title: Text(
              university.universityName,
              style: AppTextStyles.size14Regular,
            ),
            subtitle: university.universityLocationState != null
                ? Text(
                    university.universityLocationState!,
                    style: AppTextStyles.size12Regular.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  )
                : null,
            onTap: () => onUniversitySelected(university),
          );
        },
      ),
    );
  }
}
