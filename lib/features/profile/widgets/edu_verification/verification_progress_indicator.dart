import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/profile/constants/profile_constants.dart';

class VerificationProgressIndicator extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final String stepTitle;
  final String stepDescription;

  const VerificationProgressIndicator({
    Key? key,
    required this.currentStep,
    required this.totalSteps,
    required this.stepTitle,
    required this.stepDescription,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          ProfileConstants.getStepProgressText(currentStep, totalSteps),
          style: AppTextStyles.size14Medium.copyWith(
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: AppDimensions.padding8),
        LinearProgressIndicator(
          value: currentStep / totalSteps,
          backgroundColor: AppColors.grey200,
          valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
        const SizedBox(height: AppDimensions.padding16),
        Text(
          stepTitle,
          style: AppTextStyles.size20Bold,
        ),
        const SizedBox(height: AppDimensions.padding8),
        Text(
          stepDescription,
          style: AppTextStyles.size14Regular.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
}
