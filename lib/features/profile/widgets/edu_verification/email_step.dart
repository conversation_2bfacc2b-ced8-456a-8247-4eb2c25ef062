import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/profile/constants/profile_constants.dart';

class EmailStep extends StatelessWidget {
  final TextEditingController emailController;
  final bool isEmailValid;
  final VoidCallback onChanged;

  const EmailStep({
    Key? key,
    required this.emailController,
    required this.isEmailValid,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${ProfileConstants.emailLabel} *',
          style: AppTextStyles.size14Medium,
        ),
        const SizedBox(height: AppDimensions.padding8),
        Container(
          decoration: BoxDecoration(
            color: AppColors.grey100,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: isEmailValid ? AppColors.primary.withOpacity(0.3) : AppColors.grey300,
            ),
          ),
          child: TextField(
            controller: emailController,
            keyboardType: TextInputType.emailAddress,
            decoration: InputDecoration(
              hintText: ProfileConstants.emailHint,
              hintStyle: AppTextStyles.size16Regular.copyWith(
                color: AppColors.textSecondary,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(AppDimensions.padding16),
              suffixIcon: Icon(
                Icons.email_outlined,
                color: isEmailValid ? AppColors.primary : AppColors.grey500,
              ),
            ),
            style: AppTextStyles.size16Regular,
            onChanged: (_) => onChanged(),
          ),
        ),
        const SizedBox(height: AppDimensions.padding12),
        Container(
          padding: const EdgeInsets.all(AppDimensions.padding12),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            border: Border.all(
              color: AppColors.primary.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              const Icon(
                Icons.info_outline,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.padding8),
              Expanded(
                child: Text(
                  ProfileConstants.emailInfoText,
                  style: AppTextStyles.size12Regular.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
