import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/routes/vibes.dart';

/// A thumbnail widget for displaying vibe information in profile.
///
/// This widget shows a visual representation of a vibe or upcoming event
/// with participant information, dates, and navigation capabilities.
/// It supports both existing vibes (with images) and upcoming events.
///
/// Follows the architecture guide's widget naming conventions with "Ia" prefix
/// and proper widget structure patterns.
class IaVibeThumbnail extends StatelessWidget {
  /// Optional vibe ID for existing vibes
  final String? vibeId;

  /// Optional ivent ID for upcoming events
  final String? iventId;

  /// The name of the ivent
  final String iventName;

  /// Number of participants
  final int participantCount;

  /// List of participant names
  final List<String> participantNames;

  /// Optional image URL for the vibe
  final String? imageUrl;

  /// Optional date list for upcoming events
  final List<DateTime>? date;

  const IaVibeThumbnail({
    super.key,
    this.vibeId,
    this.iventId,
    required this.iventName,
    required this.participantCount,
    required this.participantNames,
    this.imageUrl,
    this.date,
  }) : assert((vibeId != null && imageUrl != null) || (date != null && iventId != null));

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        _buildThumbnail(),
        _buildOverlay(),
      ],
    );
  }

  Widget _buildThumbnail() {
    if (imageUrl != null) {
      return IaImageContainer.withImageUrl(
        imageUrl: imageUrl!,
        roundness: AppDimensions.radiusXL,
        aspectRatio: 0.6,
      );
    }
    return IaImageContainer.withChild(
      child: Container(),
      roundness: AppDimensions.radiusXL,
      borderColor: AppColors.mediumGrey,
      borderWidth: 2,
      aspectRatio: 0.6,
    );
  }

  Widget _buildOverlay() {
    return IaRoundedContainer(
      onTap: () {
        if (vibeId == null) return;
        Get.toNamed(VibesRoutes.SINGLE_VIBE_PAGE, arguments: vibeId);
      },
      padding: const EdgeInsets.all(AppDimensions.padding16),
      roundness: AppDimensions.radiusXL,
      gradient: date == null ? AppColors.gradientBlackL : null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle(),
          _buildParticipantsText(),
          if (date != null) const Spacer(),
          if (date != null) _buildDateRow(),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      iventName,
      style: AppTextStyles.size20Bold.copyWith(color: date == null ? Colors.white : AppColors.textPrimary),
      maxLines: 1,
      overflow: TextOverflow.fade,
      softWrap: false,
    );
  }

  Widget _buildParticipantsText() {
    return Text(
      _getParticipantsText(),
      style: AppTextStyles.size14Medium.copyWith(color: AppColors.textTertiary),
      maxLines: 1,
      overflow: TextOverflow.fade,
      softWrap: false,
    );
  }

  String _getParticipantsText() {
    if (participantCount == 0)
      return '';
    else if (participantCount == 1)
      return participantNames[0];
    else if (participantCount == 2)
      return '${participantNames[0]}, ${participantNames[1]}';
    else
      return '${participantNames[0]}, ${participantNames[1]} ve ${participantCount - 2} diğer';
  }

  Widget _buildDateRow() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Text(
            DateFormat('d MMMM yyyy, HH:mm').format(date![0]), // TODO: FIX
            style: AppTextStyles.size12MediumTextSecondary,
            maxLines: 2,
            softWrap: false,
          ),
        ),
        const SizedBox(width: AppDimensions.padding8),
        ProfileButtons.createVibe(),
      ],
    );
  }
}
