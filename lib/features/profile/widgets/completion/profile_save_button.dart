import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/profile/constants/profile_constants.dart';

class ProfileSaveButton extends StatelessWidget {
  final RxBool isLoading;
  final bool isProfileComplete;
  final VoidCallback onSave;

  const ProfileSaveButton({
    Key? key,
    required this.isLoading,
    required this.isProfileComplete,
    required this.onSave,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Obx(() => ElevatedButton(
            onPressed: isLoading.value ? null : onSave,
            style: ElevatedButton.styleFrom(
              backgroundColor: isProfileComplete ? AppColors.primary : AppColors.grey400,
              foregroundColor: AppColors.white,
              padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
              elevation: 0,
            ),
            child: isLoading.value
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: AppColors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (isProfileComplete) ...[
                        const Icon(Icons.check_circle, size: 20),
                        const SizedBox(width: 8),
                      ],
                      Text(
                        isProfileComplete ? ProfileConstants.completeProfileText : ProfileConstants.saveText,
                        style: AppTextStyles.size16Bold.copyWith(
                          color: AppColors.white,
                        ),
                      ),
                    ],
                  ),
          )),
    );
  }
}
