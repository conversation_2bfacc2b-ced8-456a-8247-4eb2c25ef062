class ProfileConstants {
  static const String levelStepsTitle = 'Bilinçli Hesap';
  static const String profileCompletionTitle = 'Hesap Bilgileri';
  static const String eduVerificationTitle = 'edu.tr Doğrulama';
  static const String creatorRequestTitle = 'iVent Creator Başvurusu';
  static const String creatorStepTitle = 'iVent Creator';

  static const String retryButtonText = 'Tekrar Dene';
  static const String congratulationsText = 'Tebrikler! Tüm adımlar tamamlandı';
  static const String loadingText = 'Yükleniyor...';
  static const String errorOccurredText = 'Bir hata oluştu';
  static const String sessionRequiredText = 'Oturum açmanız gerekiyor.';

  static const String fullNameLabel = 'Ad Soyad';
  static const String fullNameReadOnlyNote = 'Ad soyad bilginiz değiştirilemez';
  static const String usernameLabel = 'Kullanıcı Adı';
  static const String usernameHint = 'Kullanıcı adınızı giriniz';
  static const String usernameNote = 'Sadece harf, rakam, alt çizgi ve tire kullanabilirsiniz';
  static const String birthDateLabel = 'Doğum Tarihi';
  static const String birthDatePlaceholder = 'GG/AA/YYYY';
  static const String genderLabel = 'Cinsiyet';
  static const String genderPlaceholder = 'Cinsiyet seçiniz';

  static const String changePhotoText = 'Fotoğraf Değiştir';
  static const String profileCompletionText = 'Profil Tamamlanma';
  static const String fieldsCompletedText = ' alan tamamlandı';
  static const String completeProfileText = 'Profili Tamamla';
  static const String saveText = 'Kaydet';

  static const String completeAccountText = 'Tam Hesap olmak';
  static const String consciousAccountText = 'Bilinçli Hesap olmak';
  static const String inviteFriendsText = '10 Arkadaşını davet et';
  static const String createEventText = 'İlk iVent\'ini oluştur';
  static const String joinEventText = 'Bir iVent\'e katıl';
  static const String createMemoryText = 'İlk Memories\'ini oluştur';

  static const String nextStepText = 'Sonraki Adım';
  static const String previousStepText = 'Önceki Adım';
  static const String continueText = 'Devam Et';
  static const String finishText = 'Bitir';
  static const String submitText = 'Gönder';
  static const String cancelText = 'İptal';

  static const String creatorBenefitsTitle = 'Creator Avantajları';
  static const String creatorRequirementsTitle = 'Gereksinimler';
  static const String applicationStatusTitle = 'Başvuru Durumu';

  static const String pendingStatusText = 'Beklemede';
  static const String approvedStatusText = 'Onaylandı';
  static const String rejectedStatusText = 'Reddedildi';
  static const String notAppliedStatusText = 'Başvuru Yapılmadı';

  static const String step1Title = 'Adım 1: Temel Gereksinimler';
  static const String step2Title = 'Adım 2: Kişisel Bilgiler';
  static const String step3Title = 'Adım 3: Deneyim ve Motivasyon';
  static const String step4Title = 'Adım 4: Onay ve Gönderim';

  static const Map<String, String> genderOptions = {
    'male': 'Erkek',
    'female': 'Kadın',
    'other': 'Diğer',
    'prefer_not_to_say': 'Belirtmek İstemiyorum',
  };

  static const List<String> creatorBenefits = [
    'Özel Creator rozeti',
    'Öncelikli destek',
    'Gelişmiş analitik araçlar',
    'Özel etkinlik türleri',
    'Gelir paylaşımı imkanı',
    'Creator topluluğuna erişim',
  ];

  static const List<String> creatorRequirements = [
    'Tam hesap sahibi olmak',
    'Bilinçli hesap seviyesinde olmak',
    'En az 10 arkadaş davet etmiş olmak',
    'En az 1 etkinlik oluşturmuş olmak',
    'En az 1 etkinliğe katılmış olmak',
    'Topluluk kurallarına uygun davranış',
  ];

  static String getGenderText(String genderKey) {
    return genderOptions[genderKey] ?? genderKey;
  }

  static String getInviteFriendsProgress(int current, int total) {
    return '$inviteFriendsText. ($current/$total)';
  }

  static String getFieldsCompletedText(int completed, int total) {
    return '$completed / $total$fieldsCompletedText';
  }

  static const String emailLabel = 'E-posta Adresi';
  static const String emailHint = '<EMAIL>';
  static const String emailInfoText = 'Sadece .edu.tr uzantılı e-posta adresleri kabul edilir.';
  static const String verificationCodeLabel = 'Doğrulama Kodu';
  static const String verificationCodeHint = 'Doğrulama kodunu girin';
  static const String codeSentText = 'Kod gönderildi';
  static const String resendCodeText = 'Kodu Tekrar Gönder';
  static const String universitySelectionText = 'Üniversite Seçimi';
  static const String selectUniversityText = 'Üniversitenizi seçin';
  static const String backButtonText = 'Geri';

  static const String sendVerificationText = 'Doğrulama Kodu Gönder';
  static const String validateCodeText = 'Kodu Doğrula';
  static const String completeVerificationText = 'Doğrulamayı Tamamla';

  static String getStepProgressText(int current, int total) {
    return 'Adım $current / $total';
  }

  static String getResendTimerText(int seconds) {
    return 'Tekrar gönder ($seconds)';
  }
}
