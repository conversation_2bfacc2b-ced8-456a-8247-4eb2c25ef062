/// Validation constants for profile feature.
///
/// This class contains validation rules and limits used throughout the profile
/// feature, organized by functional areas for better maintainability and consistency
/// with the project's constants management patterns.
class ProfileValidationConstants {
  ProfileValidationConstants._(); // Private constructor to prevent instantiation

  // ============================================================================
  // PROFILE INFORMATION VALIDATION
  // ============================================================================

  /// Minimum length for user bio/description
  static const int minBioLength = 0;

  /// Maximum length for user bio/description
  static const int maxBioLength = 500;

  /// Minimum length for display name
  static const int minDisplayNameLength = 1;

  /// Maximum length for display name
  static const int maxDisplayNameLength = 50;

  /// Minimum length for username
  static const int minUsernameLength = 3;

  /// Maximum length for username
  static const int maxUsernameLength = 30;

  // ============================================================================
  // SOCIAL INTERACTION LIMITS
  // ============================================================================

  /// Maximum number of friends a user can have
  static const int maxFriendsCount = 5000;

  /// Maximum number of users a user can follow
  static const int maxFollowingCount = 10000;

  /// Maximum number of followers a user can have
  static const int maxFollowersCount = 1000000;

  /// Maximum number of favorite items
  static const int maxFavoritesCount = 1000;

  // ============================================================================
  // CONTENT LIMITS
  // ============================================================================

  /// Maximum number of hobbies/interests a user can select
  static const int maxHobbiesCount = 10;

  /// Minimum number of hobbies/interests required
  static const int minHobbiesCount = 1;

  /// Maximum number of profile images in gallery
  static const int maxProfileImagesCount = 6;

  /// Maximum file size for profile image (in MB)
  static const int maxProfileImageSizeMB = 5;

  // ============================================================================
  // SEARCH AND PAGINATION
  // ============================================================================

  /// Minimum characters required for search query
  static const int minSearchQueryLength = 2;

  /// Maximum characters allowed for search query
  static const int maxSearchQueryLength = 100;

  /// Default page size for paginated results
  static const int defaultPageSize = 20;

  /// Maximum page size for paginated results
  static const int maxPageSize = 100;

  // ============================================================================
  // UI LIMITS
  // ============================================================================

  /// Default timeout for profile operations (in seconds)
  static const int defaultOperationTimeoutSeconds = 30;

  /// Maximum number of recent searches to store
  static const int maxRecentSearchesCount = 10;

  /// Debounce delay for search input (in milliseconds)
  static const int searchDebounceDelayMs = 500;
}
