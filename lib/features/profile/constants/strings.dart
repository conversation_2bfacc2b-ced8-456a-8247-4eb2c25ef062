/// String constants for profile feature.
///
/// This class contains all the localized strings used throughout the profile
/// feature, organized by functional areas for better maintainability.
///
/// Follows the architecture guide's constants management patterns with proper
/// private constructor and static const declarations.
class ProfileStrings {
  ProfileStrings._(); // Private constructor to prevent instantiation
  static String iventler = "ivent'ler";
  static String takipEttiklerin = 'Takip Ettiklerin';
  static String arkadslar = 'Arkadaşlar';
  static String fav = 'Favoriler';
  static String adminGorunumu = 'Admin Görünümü';
  static String creatorYonet = 'Admin ve Creator’ları Yönet';
  static String uyeler = 'Üyeler';
  static String vibes = 'Vibes';
  static String uyeEklemekIcin = 'Üye Eklemek İçin Tıklayınız';
  static String yoneticiler = 'Yöneticiler';
  static String takipciler = 'Takipçiler';
  static String ivenCreatorEklemekicin = 'ivent Creator Eklemek İçin Tıklayınız';
  static String adminEklemekicin = 'Admin Eklemek İçin Tıklayınız';
  static String sayfaAdmini = 'Sayfa Admini';
  static String iventCreator = 'ivent Creator';
  static String yardimciAdmin = 'Yardımcı Admin';
  static String detayliBilgi = 'Detaylı Bilgi...';
  static String takipEt = 'Takip Et';

  static String link = 'Link';
  static String arkadasEkle = 'Arkadaş Ekle';
  static String uyeOl = 'Üye Ol';
  static String konum = 'Konum';
  static String istekGonderildi = 'İstek Gönderildi';
  static String takipArkadas = 'Takip & Arkadaş';
}
