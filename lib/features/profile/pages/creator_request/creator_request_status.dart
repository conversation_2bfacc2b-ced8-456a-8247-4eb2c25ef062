import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';

class CreatorRequestStatus extends StatefulWidget {
  const CreatorRequestStatus({super.key});

  @override
  State<CreatorRequestStatus> createState() => _CreatorRequestStatusState();
}

class _CreatorRequestStatusState extends State<CreatorRequestStatus> {

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: 'Creator Başvuru Durumu',
      showBackButton: true,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Header
            _buildStatusHeader(),
            const SizedBox(height: AppDimensions.padding32),
            
            // Status Timeline
            _buildStatusTimeline(),
            
            const SizedBox(height: AppDimensions.padding32),
            
            // Next Steps
            _buildNextStepsSection(),
            
            const SizedBox(height: AppDimensions.padding32),
            
            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.padding24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
      ),
      child: Column(
        children: [
          Icon(
            _getStatusIcon(),
            size: 64,
            color: Colors.white,
          ),
          const SizedBox(height: AppDimensions.padding16),
          Text(
            'Başvuru Gönderildi!',
            style: AppTextStyles.size24Bold.copyWith(
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.padding8),
          Text(
            'Creator başvurunuz başarıyla alındı ve değerlendirme sürecine girdi.',
            style: AppTextStyles.size16Regular.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStatusTimeline() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Başvuru Süreci',
          style: AppTextStyles.size20Bold,
        ),
        const SizedBox(height: AppDimensions.padding16),
        
        _buildTimelineItem(
          title: 'Başvuru Alındı',
          description: 'Başvuru formunuz başarıyla gönderildi.',
          isCompleted: true,
          isActive: false,
          time: 'Az önce',
        ),
        
        _buildTimelineItem(
          title: 'İnceleme Aşaması',
          description: 'Başvurunuz ekibimiz tarafından değerlendiriliyor.',
          isCompleted: false,
          isActive: true,
          time: '1-3 iş günü',
        ),
        
        _buildTimelineItem(
          title: 'Sonuç Bildirimi',
          description: 'Başvuru sonucunuz e-posta ile bildirilecek.',
          isCompleted: false,
          isActive: false,
          time: 'Yakında',
        ),
        
        _buildTimelineItem(
          title: 'Hesap Aktivasyonu',
          description: 'Onay durumunda Creator hesabınız aktifleşecek.',
          isCompleted: false,
          isActive: false,
          time: 'Onay sonrası',
        ),
      ],
    );
  }

  Widget _buildTimelineItem({
    required String title,
    required String description,
    required bool isCompleted,
    required bool isActive,
    required String time,
  }) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator
          Column(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: isCompleted 
                      ? AppColors.primary 
                      : isActive 
                          ? AppColors.primary.withValues(alpha: 0.3)
                          : Colors.grey.shade300,
                  shape: BoxShape.circle,
                  border: isActive && !isCompleted
                      ? Border.all(color: AppColors.primary, width: 2)
                      : null,
                ),
                child: isCompleted
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 16,
                      )
                    : isActive
                        ? Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: AppColors.primary,
                              shape: BoxShape.circle,
                            ),
                          )
                        : null,
              ),
              Expanded(
                child: Container(
                  width: 2,
                  color: isCompleted 
                      ? AppColors.primary 
                      : Colors.grey.shade300,
                ),
              ),
            ],
          ),
          
          const SizedBox(width: AppDimensions.padding16),
          
          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(bottom: AppDimensions.padding24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        title,
                        style: AppTextStyles.size16Bold.copyWith(
                          color: isCompleted || isActive 
                              ? Colors.black 
                              : Colors.grey.shade600,
                        ),
                      ),
                      Text(
                        time,
                        style: AppTextStyles.size12Regular.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: AppTextStyles.size14Regular.copyWith(
                      color: isCompleted || isActive 
                          ? AppColors.textSecondary 
                          : Colors.grey.shade500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNextStepsSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.padding8),
              Text(
                'Sonraki Adımlar',
                style: AppTextStyles.size16Bold.copyWith(
                  color: Colors.blue.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.padding12),
          Text(
            '• E-posta adresinizi düzenli olarak kontrol edin\n'
            '• Başvuru sonucunuz 1-3 iş günü içinde bildirilecektir\n'
            '• Onay durumunda Creator paneline erişim sağlayacaksınız\n'
            '• Ek bilgi gerekirse sizinle iletişime geçilecektir',
            style: AppTextStyles.size14Regular.copyWith(
              color: Colors.blue.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              // Profile sayfasına dön
              Get.offAllNamed('/app_navigation');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Text(
              'Profil\'e Dön',
              style: AppTextStyles.size16Bold.copyWith(
                color: Colors.white,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: AppDimensions.padding12),
        
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: () {
              // Support/Help functionality
              _showSupportDialog();
            },
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
              side: const BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Text(
              'Destek Al',
              style: AppTextStyles.size16Bold.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  IconData _getStatusIcon() {
    // Bu method başvuru durumuna göre icon döndürür
    return Icons.check_circle;
  }

  void _showSupportDialog() {
    Get.dialog(
      AlertDialog(
        title: Text(
          'Destek Talebi',
          style: AppTextStyles.size16Bold,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Creator başvurunuz ile ilgili yardıma mı ihtiyacınız var?',
              style: AppTextStyles.size14Regular,
            ),
            const SizedBox(height: 16),
            Text(
              'Destek kanalları:',
              style: AppTextStyles.size14Bold,
            ),
            const SizedBox(height: 8),
            Text(
              '• E-posta: <EMAIL>',
              style: AppTextStyles.size14Regular,
            ),
            Text(
              '• Telefon: +90 (*************',
              style: AppTextStyles.size14Regular,
            ),
            Text(
              '• Çalışma saatleri: 09:00 - 18:00',
              style: AppTextStyles.size14Regular,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'Kapat',
              style: AppTextStyles.size14Bold.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.snackbar(
                'Destek Talebi',
                'Destek ekibimiz en kısa sürede sizinle iletişime geçecektir.',
                backgroundColor: AppColors.primary,
                colorText: Colors.white,
                duration: const Duration(seconds: 3),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
            child: Text(
              'İletişime Geç',
              style: AppTextStyles.size14Bold.copyWith(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
} 