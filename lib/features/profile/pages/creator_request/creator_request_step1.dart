import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/creator_request_controller.dart';

import 'constants/creator_request_constants.dart';
import 'models/requirement_item.dart';
import 'widgets/requirement_item_widget.dart';

class CreatorRequestStep1 extends StatefulWidget {
  const CreatorRequestStep1({Key? key}) : super(key: key);

  @override
  State<CreatorRequestStep1> createState() => _CreatorRequestStep1State();
}

class _CreatorRequestStep1State extends State<CreatorRequestStep1> {
  CreatorRequestController? controller;

  @override
  void initState() {
    super.initState();
    // Create controller manually
    try {
      final authService = Get.find<AuthService>();
      controller =
          CreatorRequestController(authService, Get.find<ProfileSharedState>());
      Get.put(controller!);
    } catch (e) {
      // If AuthService is not found, navigate back
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.back();
        Get.snackbar(
          CreatorRequestConstants.errorTitle,
          CreatorRequestConstants.authErrorMessage,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      });
    }
  }

  @override
  void dispose() {
    if (controller != null) {
      Get.delete<CreatorRequestController>();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (controller == null) {
      return IaScaffold.noSearch(
        title: CreatorRequestConstants.appTitle,
        body: const Center(
          child: CircularProgressIndicator(),
        ),
        showBackButton: true,
      );
    }

    return IaScaffold.noSearch(
      title: CreatorRequestConstants.appTitle,
      body: Obx(() {
        if (controller!.isLoading()) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RequirementItemWidget(
                      requirement: RequirementItem(
                        isCompleted: controller!.isCompleteAccount,
                        title: CreatorRequestConstants.step1Requirement1,
                      ),
                    ),
                    const SizedBox(height: 20),
                    RequirementItemWidget(
                      requirement: RequirementItem(
                        isCompleted: controller!.isConsciousAccount,
                        title: CreatorRequestConstants.step1Requirement2,
                      ),
                    ),
                    const SizedBox(height: 20),
                    RequirementItemWidget(
                      requirement: RequirementItem(
                        isCompleted: controller!.hasInvitedFriends,
                        title:
                            '${CreatorRequestConstants.step1Requirement3} (${controller!.invitedFriendsCount}/10)',
                        isOrange: true,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed:
                      controller!.allRequirementsMet ? _onNextPressed : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: controller!.allRequirementsMet
                        ? AppColors.primary
                        : AppColors.grey400,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    CreatorRequestConstants.continueButton,
                    style: AppTextStyles.size16Bold.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
      showBackButton: true,
    );
  }

  void _onNextPressed() {
    if (controller!.allRequirementsMet) {
      controller!.goToStep2();
    }
  }
}
