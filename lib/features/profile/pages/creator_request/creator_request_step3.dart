import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/creator_request_controller.dart';

import 'constants/creator_request_constants.dart';
import 'widgets/city_dropdown.dart';
import 'widgets/custom_checkbox.dart';
import 'widgets/custom_text_field.dart';
import 'widgets/progress_indicator_widget.dart';
import 'widgets/social_media_field.dart';

class CreatorRequestStep3 extends StatefulWidget {
  const CreatorRequestStep3({super.key});

  @override
  State<CreatorRequestStep3> createState() => _CreatorRequestStep3State();
}

class _CreatorRequestStep3State extends State<CreatorRequestStep3> {
  final _formKey = GlobalKey<FormState>();
  late CreatorRequestController controller;

  // Social media controllers
  final _instagramController = TextEditingController();
  final _facebookController = TextEditingController();
  final _twitterController = TextEditingController();

  // Checkbox states
  bool _vibesShareEnabled = true;
  bool _socialMediaBioEnabled = false;

  // City dropdown
  String? _selectedCity;

  @override
  void initState() {
    super.initState();
    controller = Get.find<CreatorRequestController>();
    _loadExistingData();
  }

  void _loadExistingData() {
    // Pre-fill with existing user data
    controller.emailController.text =
        controller.email.isNotEmpty ? controller.email : '';

    if (controller.city.isNotEmpty) {
      _selectedCity = controller.city;
    }

    _instagramController.text = controller.instagramHandle;
    _twitterController.text = controller.twitterHandle;
  }

  @override
  void dispose() {
    _instagramController.dispose();
    _facebookController.dispose();
    _twitterController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: CreatorRequestConstants.appTitle,
      showBackButton: true,
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ProgressIndicatorWidget(
                progress: controller.progressPercentage,
                stepText: '3/3 Adım',
              ),
              const SizedBox(height: 30),
              CustomTextField(
                controller: controller.emailController,
                label: CreatorRequestConstants.emailLabel,
                isRequired: true,
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return CreatorRequestConstants.emailRequiredError;
                  }
                  if (!GetUtils.isEmail(value)) {
                    return CreatorRequestConstants.emailInvalidError;
                  }
                  return null;
                },
                onChanged: (value) => controller.email = value,
              ),
              const SizedBox(height: 20),
              CityDropdown(
                selectedCity: _selectedCity,
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedCity = newValue;
                    controller.city = newValue ?? '';
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return CreatorRequestConstants.cityRequiredError;
                  }
                  return null;
                },
              ),
              const SizedBox(height: 30),
              _buildSocialMediaSection(),
              const SizedBox(height: 40),
              _buildCheckboxSection(),
              const SizedBox(height: 40),
              _buildSubmitButton(),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSocialMediaSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: AppColors.grey100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.add,
                color: AppColors.textSecondary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                CreatorRequestConstants.socialMediaLabel,
                style: AppTextStyles.size16Regular.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        SocialMediaField(
          controller: _instagramController,
          placeholder: CreatorRequestConstants.instagramPlaceholder,
          onChanged: (value) => controller.instagramHandle = value,
        ),
        const SizedBox(height: 16),
        SocialMediaField(
          controller: _facebookController,
          placeholder: CreatorRequestConstants.facebookPlaceholder,
          onChanged: (value) {},
        ),
        const SizedBox(height: 16),
        SocialMediaField(
          controller: _twitterController,
          placeholder: CreatorRequestConstants.twitterPlaceholder,
          onChanged: (value) => controller.twitterHandle = value,
        ),
      ],
    );
  }

  Widget _buildCheckboxSection() {
    return Column(
      children: [
        CustomCheckbox(
          value: _vibesShareEnabled,
          text: CreatorRequestConstants.vibesCheckboxText,
          onChanged: (bool? value) {
            setState(() {
              _vibesShareEnabled = value ?? false;
            });
          },
        ),
        const SizedBox(height: 10),
        CustomCheckbox(
          value: _socialMediaBioEnabled,
          text: CreatorRequestConstants.socialMediaBioCheckboxText,
          onChanged: (bool? value) {
            setState(() {
              _socialMediaBioEnabled = value ?? false;
            });
          },
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: Obx(() => ElevatedButton(
            onPressed: controller.isLoading() ? null : _submitForm,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: controller.isLoading()
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    CreatorRequestConstants.submitButton,
                    style: AppTextStyles.size16Bold.copyWith(
                      color: Colors.white,
                    ),
                  ),
          )),
    );
  }

  void _submitForm() async {
    if (_formKey.currentState!.validate()) {
      // Validate required fields
      if (_selectedCity == null || _selectedCity!.isEmpty) {
        Get.snackbar(
          CreatorRequestConstants.errorTitle,
          CreatorRequestConstants.citySelectionError,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // Save checkbox states to controller if needed
      // controller.vibesShareEnabled = _vibesShareEnabled;
      // controller.socialMediaBioEnabled = _socialMediaBioEnabled;

      await controller.sendCreatorRequest();
    }
  }
}
