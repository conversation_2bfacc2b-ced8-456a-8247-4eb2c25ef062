import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import '../constants/creator_request_constants.dart';

class SupportDialog {
  static void show() {
    Get.dialog(
      AlertDialog(
        title: Text(
          CreatorRequestConstants.supportDialogTitle,
          style: AppTextStyles.size16Bold,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              CreatorRequestConstants.supportDialogContent,
              style: AppTextStyles.size14Regular,
            ),
            const SizedBox(height: 16),
            Text(
              CreatorRequestConstants.supportChannelsTitle,
              style: AppTextStyles.size14Bold,
            ),
            const SizedBox(height: 8),
            Text(
              CreatorRequestConstants.supportEmail,
              style: AppTextStyles.size14Regular,
            ),
            Text(
              CreatorRequestConstants.supportPhone,
              style: AppTextStyles.size14Regular,
            ),
            Text(
              CreatorRequestConstants.supportHours,
              style: AppTextStyles.size14Regular,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              CreatorRequestConstants.supportCloseButton,
              style: AppTextStyles.size14Bold.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.snackbar(
                CreatorRequestConstants.supportSuccessTitle,
                CreatorRequestConstants.supportSuccessMessage,
                backgroundColor: AppColors.primary,
                colorText: Colors.white,
                duration: const Duration(seconds: 3),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
            child: Text(
              CreatorRequestConstants.supportContactButton,
              style: AppTextStyles.size14Bold.copyWith(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
