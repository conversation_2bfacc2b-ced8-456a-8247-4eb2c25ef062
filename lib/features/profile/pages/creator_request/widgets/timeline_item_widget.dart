import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import '../models/timeline_item.dart';

class TimelineItemWidget extends StatelessWidget {
  final TimelineItem item;

  const TimelineItemWidget({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: item.isCompleted
                      ? AppColors.primary
                      : item.isActive
                          ? AppColors.primary.withValues(alpha: 0.3)
                          : Colors.grey.shade300,
                  shape: BoxShape.circle,
                  border: item.isActive && !item.isCompleted
                      ? Border.all(color: AppColors.primary, width: 2)
                      : null,
                ),
                child: item.isCompleted
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 16,
                      )
                    : item.isActive
                        ? Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: AppColors.primary,
                              shape: BoxShape.circle,
                            ),
                          )
                        : null,
              ),
              Expanded(
                child: Container(
                  width: 2,
                  color: item.isCompleted
                      ? AppColors.primary
                      : Colors.grey.shade300,
                ),
              ),
            ],
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        item.title,
                        style: AppTextStyles.size16Bold.copyWith(
                          color: item.isCompleted || item.isActive
                              ? Colors.black
                              : Colors.grey.shade600,
                        ),
                      ),
                      Text(
                        item.time,
                        style: AppTextStyles.size12Regular.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.description,
                    style: AppTextStyles.size14Regular.copyWith(
                      color: item.isCompleted || item.isActive
                          ? AppColors.textSecondary
                          : Colors.grey.shade500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
