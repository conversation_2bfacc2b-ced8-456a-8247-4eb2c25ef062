import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import '../constants/creator_request_constants.dart';

class StatusHeader extends StatelessWidget {
  final IconData statusIcon;

  const StatusHeader({
    super.key,
    required this.statusIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.padding24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
      ),
      child: Column(
        children: [
          Icon(
            statusIcon,
            size: 64,
            color: Colors.white,
          ),
          const SizedBox(height: AppDimensions.padding16),
          Text(
            CreatorRequestConstants.statusHeaderTitle,
            style: AppTextStyles.size24Bold.copyWith(
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.padding8),
          Text(
            CreatorRequestConstants.statusHeaderDescription,
            style: AppTextStyles.size16Regular.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
