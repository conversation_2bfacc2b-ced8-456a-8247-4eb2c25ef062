import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import '../models/requirement_item.dart';

class RequirementItemWidget extends StatelessWidget {
  final RequirementItem requirement;

  const RequirementItemWidget({
    super.key,
    required this.requirement,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: requirement.isCompleted
                ? AppColors.primary
                : Colors.transparent,
            border: Border.all(
              color: requirement.isCompleted
                  ? AppColors.primary
                  : AppColors.grey400,
              width: 2,
            ),
          ),
          child: requirement.isCompleted
              ? const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 16,
                )
              : null,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            requirement.title,
            style: AppTextStyles.size16Regular.copyWith(
              color: requirement.isOrange
                  ? AppColors.orange
                  : AppColors.textPrimary,
              fontWeight:
                  requirement.isCompleted ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ),
      ],
    );
  }
}
