class CreatorRequestConstants {
  static const String appTitle = 'iVent Creator';
  static const String statusTitle = 'Creator Başvuru Durumu';

  static const String step1Title = 'Gereksinimler';
  static const String step2Title = 'Açıklama Yazınız';
  static const String step3Title = 'Kişisel Bilgiler';
  static const String step4Title = 'Başvuru Özeti';

  static const String continueButton = 'Devam Et';
  static const String backButton = 'Geri';
  static const String submitButton = 'iVent Creator Başvurusu Gönder';
  static const String submitFinalButton = 'Başvuruyu Gönder';
  static const String profileButton = 'Profil\'e Dön';
  static const String supportButton = 'Destek Al';

  static const String step1Requirement1 = 'Tam Hesap olmak';
  static const String step1Requirement2 = 'Bilinçli Hesap olmak';
  static const String step1Requirement3 = '10 Arkadaşını davet et';

  static const String step2Description =
      'Neden iVent Creator olmak istiyorsun? Ne tarzda etkinlikler organize etmekten hoşlanırsın? Kısa bir yazıyla iVent App ile nasıl bir işbirliği içerisinde olabileceğine dair bize kısa bir başvuru yazısı yollayabilirsin. Unutma minimum karakter sayısı 140 olacak. Daha kısa bir yazı yazarsan gönderemezsin.';
  static const String step2Placeholder = 'Açıklama Yazınız';
  static const int minDescriptionLength = 140;
  static const String minCharacterWarning = 'Minimum 140 karakter yazın';

  static const String emailLabel = 'E-Mail Adresi';
  static const String cityLabel = 'Yaşadığın Şehir';
  static const String socialMediaLabel = 'Sosyal Medya Bağlantısı Ekle';
  static const String instagramPlaceholder = 'instagram.com/kullaniciadi';
  static const String facebookPlaceholder = 'facebook.com/kullaniciadi';
  static const String twitterPlaceholder = 'twitter.com/kullaniciadi';

  static const String vibesCheckboxText =
      'Katıldığım etkinliklerde Vibes paylaşabilirim.';
  static const String socialMediaBioCheckboxText =
      'Sosyal medya profilime "iVent Creator" yazısı ve iVent app profil linki koyabilirim.';

  static const String emailRequiredError = 'E-posta adresi zorunludur';
  static const String emailInvalidError = 'Geçerli bir e-posta adresi girin';
  static const String cityRequiredError = 'Şehir seçimi zorunludur';
  static const String citySelectionError = 'Lütfen şehir seçimi yapın.';

  static const String statusHeaderTitle = 'Başvuru Gönderildi!';
  static const String statusHeaderDescription =
      'Creator başvurunuz başarıyla alındı ve değerlendirme sürecine girdi.';

  static const String timelineTitle = 'Başvuru Süreci';
  static const String timelineStep1Title = 'Başvuru Alındı';
  static const String timelineStep1Description =
      'Başvuru formunuz başarıyla gönderildi.';
  static const String timelineStep1Time = 'Az önce';

  static const String timelineStep2Title = 'İnceleme Aşaması';
  static const String timelineStep2Description =
      'Başvurunuz ekibimiz tarafından değerlendiriliyor.';
  static const String timelineStep2Time = '1-3 iş günü';

  static const String timelineStep3Title = 'Sonuç Bildirimi';
  static const String timelineStep3Description =
      'Başvuru sonucunuz e-posta ile bildirilecek.';
  static const String timelineStep3Time = 'Yakında';

  static const String timelineStep4Title = 'Hesap Aktivasyonu';
  static const String timelineStep4Description =
      'Onay durumunda Creator hesabınız aktifleşecek.';
  static const String timelineStep4Time = 'Onay sonrası';

  static const String nextStepsTitle = 'Sonraki Adımlar';
  static const String nextStepsContent =
      '• E-posta adresinizi düzenli olarak kontrol edin\n'
      '• Başvuru sonucunuz 1-3 iş günü içinde bildirilecektir\n'
      '• Onay durumunda Creator paneline erişim sağlayacaksınız\n'
      '• Ek bilgi gerekirse sizinle iletişime geçilecektir';

  static const String supportDialogTitle = 'Destek Talebi';
  static const String supportDialogContent =
      'Creator başvurunuz ile ilgili yardıma mı ihtiyacınız var?';
  static const String supportChannelsTitle = 'Destek kanalları:';
  static const String supportEmail = '• E-posta: <EMAIL>';
  static const String supportPhone = '• Telefon: +90 (*************';
  static const String supportHours = '• Çalışma saatleri: 09:00 - 18:00';
  static const String supportCloseButton = 'Kapat';
  static const String supportContactButton = 'İletişime Geç';
  static const String supportSuccessTitle = 'Destek Talebi';
  static const String supportSuccessMessage =
      'Destek ekibimiz en kısa sürede sizinle iletişime geçecektir.';

  static const String step4SummaryTitle = 'Başvuru Özeti';
  static const String step4SummaryDescription =
      'Başvuru bilgilerinizi kontrol edin ve gönderin.';
  static const String step4PersonalInfoTitle = 'Kişisel Bilgiler';
  static const String step4ExperienceInfoTitle = 'Deneyim Bilgileri';
  static const String step4VerificationInfoTitle = 'Doğrulama Bilgileri';

  static const String step4ImportantInfoTitle = 'Önemli Bilgiler';
  static const String step4ImportantInfoContent =
      '• Başvurunuz 1-3 iş günü içinde değerlendirilecektir.\n'
      '• Başvuru sonucunuz e-posta ile bildirilecektir.\n'
      '• Verdiğiniz bilgilerin doğruluğu kontrol edilecektir.\n'
      '• Yanlış bilgi vermek başvurunuzun reddedilmesine neden olur.\n'
      '• Creator olarak kabul edildiğinizde, iVent Creator Sözleşmesi\'ni kabul etmiş sayılırsınız.';

  static const String step4AgreementText =
      'Başvuruyu göndererek iVent Creator Sözleşmesi\'ni kabul etmiş olursunuz.';

  static const String errorTitle = 'Hata';
  static const String authErrorMessage = 'Oturum açmanız gerekiyor.';

  static const List<String> turkishCities = [
    'Adana',
    'Adıyaman',
    'Afyonkarahisar',
    'Ağrı',
    'Amasya',
    'Ankara',
    'Antalya',
    'Artvin',
    'Aydın',
    'Balıkesir',
    'Bilecik',
    'Bingöl',
    'Bitlis',
    'Bolu',
    'Burdur',
    'Bursa',
    'Çanakkale',
    'Çankırı',
    'Çorum',
    'Denizli',
    'Diyarbakır',
    'Edirne',
    'Elazığ',
    'Erzincan',
    'Erzurum',
    'Eskişehir',
    'Gaziantep',
    'Giresun',
    'Gümüşhane',
    'Hakkâri',
    'Hatay',
    'Isparta',
    'Mersin',
    'İstanbul',
    'İzmir',
    'Kars',
    'Kastamonu',
    'Kayseri',
    'Kırklareli',
    'Kırşehir',
    'Kocaeli',
    'Konya',
    'Kütahya',
    'Malatya',
    'Manisa',
    'Kahramanmaraş',
    'Mardin',
    'Muğla',
    'Muş',
    'Nevşehir',
    'Niğde',
    'Ordu',
    'Rize',
    'Sakarya',
    'Samsun',
    'Siirt',
    'Sinop',
    'Sivas',
    'Tekirdağ',
    'Tokat',
    'Trabzon',
    'Tunceli',
    'Şanlıurfa',
    'Uşak',
    'Van',
    'Yozgat',
    'Zonguldak',
    'Aksaray',
    'Bayburt',
    'Karaman',
    'Kırıkkale',
    'Batman',
    'Şırnak',
    'Bartın',
    'Ardahan',
    'Iğdır',
    'Yalova',
    'Karabük',
    'Kilis',
    'Osmaniye',
    'Düzce',
  ];
}
