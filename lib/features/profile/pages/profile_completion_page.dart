import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/constants/profile_constants.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/profile_completion_controller.dart';
import 'package:ivent_app/features/profile/utils/profile_dialogs.dart';
import 'package:ivent_app/features/profile/widgets/common/profile_form_field.dart';
import 'package:ivent_app/features/profile/widgets/common/profile_progress_indicator.dart';
import 'package:ivent_app/features/profile/widgets/completion/profile_image_section.dart';
import 'package:ivent_app/features/profile/widgets/completion/profile_save_button.dart';

class ProfileCompletionPage extends GetView<ProfileCompletionController> {
  const ProfileCompletionPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: ProfileConstants.profileCompletionTitle,
      body: Obx(() {
        if (controller.isLoading() && controller.currentUsername.isEmpty) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        return _buildBody();
      }),
      showBackButton: true,
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ProfileImageSection(
            profileImage: controller.profileImage,
            currentAvatarUrl: controller.currentAvatarUrl,
            onPickImage: controller.pickImage,
          ),
          const SizedBox(height: AppDimensions.padding24),
          Obx(() => ProfileFormField(
                label: ProfileConstants.fullNameLabel,
                note: ProfileConstants.fullNameReadOnlyNote,
                isReadOnly: true,
                value: controller.currentFullname.value,
              )),
          const SizedBox(height: AppDimensions.padding16),
          ProfileFormField(
            label: ProfileConstants.usernameLabel,
            hint: ProfileConstants.usernameHint,
            note: ProfileConstants.usernameNote,
            isRequired: true,
            controller: controller.usernameController,
            prefixIcon: const Icon(Icons.alternate_email, size: 20),
            onChanged: (value) => controller.usernameController.text = value,
          ),
          const SizedBox(height: AppDimensions.padding16),
          Obx(() => ProfileFormField(
                label: ProfileConstants.birthDateLabel,
                isRequired: true,
                value: controller.birthDateText.value,
                suffixIcon: Icon(
                  Icons.calendar_today,
                  color: controller.hasSelectedBirthDate ? Colors.blue : Colors.grey,
                  size: 20,
                ),
                onTap: controller.pickBirthDate,
              )),
          const SizedBox(height: AppDimensions.padding16),
          Obx(() => ProfileFormField(
                label: ProfileConstants.genderLabel,
                isRequired: true,
                value: controller.selectedGender.value.isEmpty
                    ? ProfileConstants.genderPlaceholder
                    : ProfileConstants.getGenderText(controller.selectedGender.value),
                suffixIcon: const Icon(Icons.keyboard_arrow_down, size: 24),
                onTap: () => ProfileDialogs.showGenderPicker(
                  currentGender: controller.selectedGender.value,
                  onGenderSelected: (gender) => controller.selectedGender.value = gender,
                ),
              )),
          const SizedBox(height: AppDimensions.padding32),
          Obx(() {
            final completedFields = [
              controller.hasProfileImage,
              controller.usernameController.text.isNotEmpty,
              controller.hasSelectedBirthDate,
              controller.hasSelectedGender,
            ].where((field) => field).length;

            const totalFields = 4;
            final progress = completedFields / totalFields;

            return ProfileProgressIndicator(
              title: ProfileConstants.profileCompletionText,
              progress: progress,
              progressText: ProfileConstants.getFieldsCompletedText(completedFields, totalFields),
            );
          }),
          const SizedBox(height: AppDimensions.padding16),
          Obx(() => ProfileSaveButton(
                isLoading: RxBool(controller.isLoading()),
                isProfileComplete: controller.isProfileComplete,
                onSave: controller.saveProfile,
              )),
        ],
      ),
    );
  }
}
