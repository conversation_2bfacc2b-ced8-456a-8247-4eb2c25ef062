import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/searchable/friends_controller.dart';
import 'package:ivent_app/routes/profile.dart';

/// Profile friends page for displaying and searching user's friends.
///
/// This page shows a searchable list of the user's friends with proper
/// search functionality and empty state handling.
///
/// Follows the architecture guide's page structure patterns with proper
/// lifecycle management and resource cleanup.
class ProfilePageFriends extends StatefulWidget {
  /// The user ID for the profile whose friends to display
  final String userId;

  const ProfilePageFriends({Key? key, required this.userId}) : super(key: key);

  @override
  State<ProfilePageFriends> createState() => _ProfilePageFriendsState();
}

/// Private state class for the profile friends page
class _ProfilePageFriendsState extends State<ProfilePageFriends> {
  late final ProfileController _controller;
  late final FriendsController _friendsController;

  @override
  void initState() {
    super.initState();
    _controller = Get.find(tag: widget.userId);
    _friendsController = _controller.friendsController;
  }

  @override
  void dispose() {
    // Controller cleanup is handled by the parent controller
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: 'Arkadaşlar',
      textEditingController: _friendsController.textEditingController,
      body: Obx(() {
        final friendsResult = _friendsController.friendsResult;
        return IaSearchPlaceholder(
          entityName: 'Kullanıcı',
          isSearching: _friendsController.isSearching,
          isResultsEmpty: _friendsController.isResultsEmpty,
          isQueryEmpty: _friendsController.isQueryEmpty,
          initialSearchResultsState: InitialSearchResultsState.LOADED,
          builder: (context) {
            return ListView.separated(
              padding: const EdgeInsets.only(bottom: 100),
              itemCount: friendsResult!.friendCount,
              itemBuilder: (context, index) {
                final friend = friendsResult.friends[index];
                return IaListTile.withImageUrl(
                  avatarUrl: friend.avatarUrl,
                  title: '@${friend.username}',
                  subtitle: friend.university,
                  onTap: () => Get.toNamed(ProfileRoutes.PROFILE_PAGE, arguments: friend.userId),
                  trailing: SharedButtons.moreVertical(),
                );
              },
              separatorBuilder: IaListTile.separatorBuilder20,
            );
          },
        );
      }),
    );
  }
}
