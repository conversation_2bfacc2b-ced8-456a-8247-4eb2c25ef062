import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/searchable/followings_controller.dart';
import 'package:ivent_app/routes/profile.dart';

class ProfilePageFollowings extends StatelessWidget {
  final String userId;

  const ProfilePageFollowings({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ProfileController controller = Get.find(tag: userId);
    final FollowingsController followingsController = controller.followingsController;

    return IaScaffold.search(
      title: '<PERSON><PERSON><PERSON>',
      textEditingController: followingsController.textEditingController,
      body: Obx(() {
        final followingsResult = followingsController.followingsResult;
        return IaSearchPlaceholder(
          entityName: '<PERSON><PERSON>ı<PERSON><PERSON>',
          isSearching: followingsController.isSearching,
          isResultsEmpty: followingsController.isResultsEmpty,
          isQueryEmpty: followingsController.isQueryEmpty,
          initialSearchResultsState: InitialSearchResultsState.LOADED,
          builder: (context) {
            return ListView.separated(
              padding: const EdgeInsets.only(bottom: 100),
              itemCount: followingsResult!.followingCount,
              itemBuilder: (context, index) {
                final following = followingsResult.followings[index];
                return IaListTile.withImageUrl(
                  avatarUrl: following.avatarUrl,
                  title: '@${following.username}',
                  subtitle: following.university,
                  onTap: () => Get.toNamed(ProfileRoutes.PROFILE_PAGE, arguments: following.userId),
                  trailing: SharedButtons.moreVertical(),
                );
              },
              separatorBuilder: IaListTile.separatorBuilder20,
            );
          },
        );
      }),
    );
  }
}
