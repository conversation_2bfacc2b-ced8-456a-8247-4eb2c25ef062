import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/enums/account_enum.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_ivent_tile.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/searchable/favorites_controller.dart';
import 'package:ivent_app/routes/ivent_detail.dart';

class ProfilePageFavorites extends StatelessWidget {
  final TextEditingController _searchBarController = TextEditingController();
  final String userId;

  ProfilePageFavorites({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ProfileController controller = Get.find(tag: userId);
    final FavoritesController favoritesController = controller.favoritesController;

    return IaScaffold.search(
      title: 'Favoriler',
      textEditingController: _searchBarController,
      body: Obx(() {
        final favoritesResult = favoritesController.favoritesResult;
        return IaSearchPlaceholder(
          entityName: 'iVent',
          isSearching: favoritesController.isSearching,
          isResultsEmpty: favoritesController.isResultsEmpty,
          isQueryEmpty: favoritesController.isQueryEmpty,
          initialSearchResultsState: InitialSearchResultsState.LOADED,
          builder: (context) {
            return _buildSearchResults(favoritesResult, favoritesController);
          },
        );
      }),
    );
  }

  ListView _buildSearchResults(GetFavoritesByUserIdReturn? favoritesResult, FavoritesController favoritesController) {
    return ListView.separated(
      padding: const EdgeInsets.only(bottom: 100),
      itemCount: favoritesResult!.iventCount,
      itemBuilder: (context, index) {
        final ivent = favoritesResult.ivents[index];
        return Obx(() {
          final isFavorited = favoritesController.favoritedIventIds.contains(ivent.iventId);
          return IaIventTile(
            imageUrl: ivent.thumbnailUrl,
            iventName: ivent.iventName,
            locationName: ivent.locationName,
            date: ivent.dates.map((e) => DateTime.parse(e)).toList(),
            memberAvatarUrls: ivent.memberAvatarUrls,
            memberCount: ivent.memberCount,
            memberNames: ivent.memberFirstnames,
            isOrganizerUser: ivent.creatorType != AccountEnum.DISTRIBUTOR,
            organizerName: ivent.creatorUsername,
            organizerAvatarUrl: ivent.creatorImageUrl,
            viewType: ivent.viewType,
            onTap: () => Get.toNamed(IventDetayRoutes.IVENT_DETAY, arguments: ivent.iventId),
            isFavorited: isFavorited,
            onFavorite: () => favoritesController.toggleFavorite(ivent.iventId),
          );
        });
      },
      separatorBuilder: IaListTile.separatorBuilder20,
    );
  }
}
