import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/constants/enums/user_type_enum.dart';
import 'package:ivent_app/core/utils/list_utils.dart';
import 'package:ivent_app/core/widgets/composite/buttons/profile_buttons.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/widgets/ia_profile_count.dart';
import 'package:ivent_app/features/profile/widgets/ia_profile_tabs.dart';
import 'package:ivent_app/routes/profile.dart';
import 'package:ivent_app/routes/settings.dart';

class ProfilePage extends StatefulWidget {
  final String userId;

  const ProfilePage({Key? key, required this.userId}) : super(key: key);

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  late final ProfileController _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.find(tag: widget.userId);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final pageContent = _controller.userInfoController.userPageInfo;
      final vibesContent = _controller.contentController.vibeFolders;
      if (pageContent == null || vibesContent == null) {
        return IaScaffold.loading();
      }
      final isFirstPerson = pageContent.isFirstPerson;
      final hobbiesList = pageContent.hobbies;
      return IaScaffold.profile(
        title: pageContent.fullname,
        subtitle: '@${pageContent.username}',
        onSettingsTap: isFirstPerson ? () => Get.toNamed(SettingRoutes.SETTINGS) : null,
        body: DefaultTabController(
          length: 2,
          child: NestedScrollView(
            headerSliverBuilder: (context, value) {
              return [
                SliverToBoxAdapter(child: _buildStatsAvatarRow(_controller, pageContent)),
                SliverToBoxAdapter(child: _buildProfileButtonsRow(_controller, pageContent)),
                if (hobbiesList.isNotEmpty || isFirstPerson)
                  SliverToBoxAdapter(
                    child: _buildHobbiesRow(hobbiesList, isFirstPerson),
                  ),
                SliverToBoxAdapter(child: _buildProfileTabButtons()),
              ];
            },
            body: IaProfileTabs(controller: _controller, vibesContent: vibesContent),
          ),
        ),
      );
    });
  }

  Widget _buildStatsAvatarRow(ProfileController controller, GetUserByUserIdReturn pageContent) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IaProfileCount.iventCount(controller),
          SizedBox(width: Get.width * 0.13),
          CircleAvatar(
            radius: Get.width * 0.084,
            backgroundImage: pageContent.avatarUrl != null ? NetworkImage(pageContent.avatarUrl!) : null,
          ),
          SizedBox(width: Get.width * 0.13),
          pageContent.userRole == UserTypeEnum.CREATOR
              ? IaProfileCount.followerCount(controller)
              : IaProfileCount.friendCount(controller),
        ],
      ),
    );
  }

  Widget _buildProfileButtonsRow(ProfileController controller, GetUserByUserIdReturn pageContent) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: insertBetween(
          _buildButtonLayout(controller, pageContent).map((buttonLayout) {
            return Expanded(child: buttonLayout);
          }).toList(),
          const SizedBox(width: AppDimensions.padding12),
        ),
      ),
    );
  }

  List<Widget> _buildButtonLayout(ProfileController controller, GetUserByUserIdReturn pageContent) {
    if (pageContent.isFirstPerson) {
      return [
        ProfileButtons.profileFollowings(onTap: () => Get.toNamed(ProfileRoutes.PROFILE_PAGE_TAKIP_ETTIKLERIN)),
        ProfileButtons.profileFavorites(onTap: () => Get.toNamed(ProfileRoutes.PROFILE_PAGE_FAVORILER)),
      ];
    } else {
      if (pageContent.userRole == UserTypeEnum.CREATOR) {
        return [
          Obx(() {
            return ProfileButtons.profileFollow(
              onTap: controller.userInfoController.toggleFollowing,
              isFollowing: controller.userInfoController.isFollowing,
            );
          }),
          Obx(() {
            return ProfileButtons.profileAddFriend(
              onTap: controller.userInfoController.toggleFriendship,
              relationshipStatus: controller.userInfoController.relationshipStatus,
            );
          }),
        ];
      } else {
        return [
          Obx(() {
            return ProfileButtons.profileAddFriend(
              onTap: controller.userInfoController.toggleFriendship,
              relationshipStatus: controller.userInfoController.relationshipStatus,
            );
          }),
        ];
      }
    }
  }

  Widget _buildHobbiesRow(List<String> hobbiesList, bool isFirstPerson) {
    return Padding(
      padding: const EdgeInsets.only(top: AppDimensions.padding20),
      child: Row(
        children: [
          if (isFirstPerson)
            Padding(
              padding: const EdgeInsets.only(left: AppDimensions.padding20, right: AppDimensions.padding8),
              child: ProfileButtons.profileTagEditButton(),
            ),
          Expanded(
            child: Container(
              height: AppDimensions.buttonHeightProfileTag,
              child: ListView.separated(
                padding: const EdgeInsets.only(right: AppDimensions.padding20),
                scrollDirection: Axis.horizontal,
                itemCount: hobbiesList.length,
                itemBuilder: (context, index) {
                  return ProfileButtons.profileTag(
                    onTap: () {},
                    text: hobbiesList[index],
                  );
                },
                separatorBuilder: (context, index) => const SizedBox(width: AppDimensions.padding8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileTabButtons() {
    return Padding(
      padding: const EdgeInsets.only(top: AppDimensions.padding20),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
        height: 40,
        child: TabBar(
          dividerHeight: 0,
          indicatorColor: Colors.transparent,
          indicatorSize: TabBarIndicatorSize.tab,
          overlayColor: WidgetStateProperty.all(Colors.transparent),
          labelStyle: AppTextStyles.size14Bold,
          unselectedLabelStyle: AppTextStyles.size14BoldTextSecondary,
          tabs: const [Tab(text: 'Vibes'), Tab(text: 'iVent Memories')],
        ),
      ),
    );
  }
}
