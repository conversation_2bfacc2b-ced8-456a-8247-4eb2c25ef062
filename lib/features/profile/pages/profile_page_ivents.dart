import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/enums/account_enum.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_ivent_tile.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/searchable/ivents_controller.dart';
import 'package:ivent_app/routes/ivent_detail.dart';

class ProfilePageIvents extends StatelessWidget {
  final String userId;

  const ProfilePageIvents({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ProfileController controller = Get.find(tag: userId);
    final IventsController iventsController = controller.iventsController;

    return IaScaffold.search(
      title: "iVent'ler",
      textEditingController: iventsController.textEditingControllerJoined,
      searchBarLabelText: "Tüm iVent'lerden ara",
      body: Obx(() {
        final joinedIventsResult = iventsController.joinedIventsResult;
        return IaSearchPlaceholder(
          entityName: 'iVent',
          isSearching: iventsController.isSearchingJoined,
          isResultsEmpty: iventsController.isResultsEmptyJoined,
          isQueryEmpty: iventsController.isQueryEmptyJoined,
          initialSearchResultsState: InitialSearchResultsState.LOADED,
          builder: (context) {
            return ListView.separated(
              padding: const EdgeInsets.only(bottom: 100),
              itemCount: joinedIventsResult!.iventCount,
              itemBuilder: (context, index) {
                final ivent = joinedIventsResult.ivents[index];
                return IaIventTile(
                  imageUrl: ivent.thumbnailUrl,
                  iventName: ivent.iventName,
                  locationName: ivent.locationName,
                  date: ivent.dates.map((e) => DateTime.parse(e)).toList(),
                  memberAvatarUrls: ivent.memberAvatarUrls,
                  memberCount: ivent.memberCount,
                  memberNames: ivent.memberFirstnames,
                  isOrganizerUser: ivent.creatorType != AccountEnum.DISTRIBUTOR,
                  organizerName: ivent.creatorUsername,
                  organizerAvatarUrl: ivent.creatorImageUrl,
                  viewType: ivent.viewType,
                  onTap: () => Get.toNamed(IventDetayRoutes.IVENT_DETAY, arguments: ivent.iventId),
                );
              },
              separatorBuilder: IaListTile.separatorBuilder20,
            );
          },
        );
      }),
    );
  }
}
