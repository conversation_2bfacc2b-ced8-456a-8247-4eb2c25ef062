import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/content_controller.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/searchable/favorites_controller.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/searchable/followers_controller.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/searchable/followings_controller.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/searchable/friends_controller.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/searchable/ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/social_controller.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/user_info_controller.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class ProfileController extends BaseController<ProfileSharedState> {
  // Child controllers - declared as late final for proper initialization order
  late final UserInfoController userInfoController;
  late final ContentController contentController;
  late final SocialController socialController;
  late final IventsController iventsController;
  late final FriendsController friendsController;
  late final FollowersController followersController;
  late final FollowingsController followingsController;
  late final FavoritesController favoritesController;

  /// Constructor with dependency injection
  ProfileController(AuthService authService, ProfileSharedState state) : super(authService, state);

  // Lifecycle methods

  @override
  Future<void> initController() async {
    super.initController();

    // Initialize child controllers with user-specific tags in proper order
    final tag = state.userId;

    try {
      // Initialize core controllers first
      userInfoController = Get.put(UserInfoController(authService, state), tag: tag);
      contentController = Get.put(ContentController(authService, state), tag: tag);
      socialController = Get.put(SocialController(authService, state), tag: tag);

      // Initialize searchable controllers
      iventsController = Get.put(IventsController(authService, state), tag: tag);
      friendsController = Get.put(FriendsController(authService, state), tag: tag);
      followersController = Get.put(FollowersController(authService, state), tag: tag);
      followingsController = Get.put(FollowingsController(authService, state), tag: tag);
      favoritesController = Get.put(FavoritesController(authService, state), tag: tag);

      // Load initial data
      await _loadInitialData();
    } catch (e) {
      handleError(e);
    }
  }

  @override
  void closeController() {
    // Clean up child controllers with user-specific tags in reverse order
    final tag = state.userId;

    try {
      // Delete searchable controllers first
      Get.delete<FavoritesController>(tag: tag);
      Get.delete<FollowingsController>(tag: tag);
      Get.delete<FollowersController>(tag: tag);
      Get.delete<FriendsController>(tag: tag);
      Get.delete<IventsController>(tag: tag);

      // Delete core controllers last
      Get.delete<SocialController>(tag: tag);
      Get.delete<ContentController>(tag: tag);
      Get.delete<UserInfoController>(tag: tag);
    } catch (e) {
      handleError(e);
    }

    super.closeController();
  }

  /// Refreshes the current profile data
  Future<void> refreshProfile() async => await _loadInitialData();

  // Private methods

  /// Loads initial data for the profile
  Future<void> _loadInitialData() async {
    await runWithLoading(
      () async {
        // Load any required initial data here
        // Individual controllers handle their own data loading
      },
      loadingTag: 'initData',
    );
  }
}
