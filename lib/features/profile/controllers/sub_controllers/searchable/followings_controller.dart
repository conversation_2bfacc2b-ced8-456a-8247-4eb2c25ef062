import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/routes/profile.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class FollowingsController extends BaseControllerWithSearch<ProfileSharedState> {
  FollowingsController(AuthService authService, ProfileSharedState state) : super(authService, state);

  final _followingsResult = Rxn<GetFollowingsByUserIdReturn>();

  GetFollowingsByUserIdReturn? get followingsResult => _followingsResult.value;

  @override
  bool get isResultsEmpty => followingsResult?.followings.isEmpty ?? true;

  @override
  Future<void> onSearch([String? query]) async {
    await runWithLoading(
      () async {
        _followingsResult.value = await authService.usersApi.getFollowingsByUserId(
          state.userId,
          q: query,
        );
      },
      loadingTag: 'searchFollowings',
    );
  }

  Future<void> goToFollowingsPage() async {
    Get.toNamed(ProfileRoutes.PROFILE_PAGE_TAKIP_ETTIKLERIN, arguments: state.userId);
    if (followingsResult == null) await onSearch();
  }
}
