import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/routes/profile.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class FollowersController extends BaseControllerWithSearch<ProfileSharedState> {
  FollowersController(AuthService authService, ProfileSharedState state) : super(authService, state);

  @override
  bool get isResultsEmpty => followersResult?.followers.isEmpty ?? true;

  @override
  Future<void> onSearch([String? query]) async {
    _followersResult.value = await authService.usersApi.getFollowersByUserId(state.userId, q: query);
  }

  final _followersResult = Rxn<GetFollowersByUserIdReturn>();
  
  GetFollowersByUserIdReturn? get followersResult => _followersResult.value;

  Future<void> goToFollowersPage() async {
    Get.toNamed(ProfileRoutes.PROFILE_PAGE_TAKIPCILER, arguments: state.userId);
    if (followersResult == null) await onSearch();
  }
}
