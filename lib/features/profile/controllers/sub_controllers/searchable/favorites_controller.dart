import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/routes/profile.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class FavoritesController extends BaseControllerWithSearch<ProfileSharedState> {
  FavoritesController(AuthService authService, ProfileSharedState state) : super(authService, state);

  final _favoritesResult = Rxn<GetFavoritesByUserIdReturn>();
  final _favoritedIventIds = RxList<String>([]);

  GetFavoritesByUserIdReturn? get favoritesResult => _favoritesResult.value;
  List<String> get favoritedIventIds => _favoritedIventIds;

  @override
  bool get isResultsEmpty => favoritesResult?.ivents.isEmpty ?? true;

  @override
  Future<void> onSearch([String? query]) async {
    await runWithLoading(
      () async {
        _favoritesResult.value = await authService.usersApi.getFavoritesByUserId(
          state.userId,
          q: query,
        );
        if (favoritesResult != null) {
          _favoritedIventIds.value = favoritesResult!.ivents.map((e) => e.iventId).toList();
        }
      },
      loadingTag: 'searchFavorites',
    );
  }

  Future<void> toggleFavorite(String iventId) async {
    await runWithLoading(
      () async {
        if (favoritedIventIds.contains(iventId)) {
          favoritedIventIds.remove(iventId);
          await authService.iventsApi.unfavoriteIventByIventId(iventId);
        } else {
          favoritedIventIds.add(iventId);
          await authService.iventsApi.favoriteIventByIventId(iventId);
        }
      },
      loadingTag: 'toggleFavorite',
    );
  }

  Future<void> goToFavoritesPage() async {
    Get.toNamed(ProfileRoutes.PROFILE_PAGE_FAVORILER, arguments: state.userId);
    if (favoritesResult == null) await onSearch();
  }
}
