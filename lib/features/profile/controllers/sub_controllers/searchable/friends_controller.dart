import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/routes/profile.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class FriendsController extends BaseControllerWithSearch<ProfileSharedState> {
  FriendsController(AuthService authService, ProfileSharedState state) : super(authService, state);

  final _friendsResult = Rxn<SearchFriendsByUserIdReturn>();

  SearchFriendsByUserIdReturn? get friendsResult => _friendsResult.value;

  @override
  bool get isResultsEmpty => friendsResult?.friends.isEmpty ?? true;

  @override
  Future<void> onSearch([String? query]) async {
    await runWithLoading(
      () async {
        _friendsResult.value = await authService.userRelationshipsApi.searchFriendsByUserId(
          state.userId,
          FriendListingTypeEnum.user,
          q: query,
        );
      },
      loadingTag: 'searchFriends',
    );
  }

  Future<void> goToFriendsPage() async {
    Get.toNamed(ProfileRoutes.PROFILE_PAGE_ARKADASLAR, arguments: state.userId);
    if (friendsResult == null) await onSearch();
  }
}
