import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/creator_request_controller.dart';
import 'package:ivent_app/features/profile/pages/creator_request/creator_request_step1.dart';
import 'package:ivent_app/features/profile/pages/edu_verification_page.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Controller for managing profile side menu data and interactions
///
/// Handles user level information, pages data, and account type management.
/// Provides data for the side menu UI components.
class ProfileSideMenuController extends BaseController<ProfileSharedState> {
  // Reactive state
  final _userBanner = Rxn<GetUserBannerByUserIdReturn>();
  final _userPages = Rxn<GetPagesByUserIdReturn>();

  // Constructor
  ProfileSideMenuController(AuthService authService, ProfileSharedState state) : super(authService, state);

  // Getters
  GetUserBannerByUserIdReturn? get userBanner => _userBanner.value;
  GetPagesByUserIdReturn? get userPages => _userPages.value;

  /// Gets current user level based on user role
  int get currentLevel {
    if (!authService.isLoggedIn) return 0;
    final userRole = sessionUser.sessionRole;
    switch (userRole) {
      case UserRoleEnum.creator:
        return 5;
      case UserRoleEnum.level5:
        return 5;
      case UserRoleEnum.level4:
        return 4;
      case UserRoleEnum.level3:
        return 3;
      case UserRoleEnum.level2:
        return 2;
      case UserRoleEnum.level1:
        return 1;
      case UserRoleEnum.level0:
      default:
        return 0;
    }
  }

  // User level helpers
  String get levelText {
    if (!authService.isLoggedIn) return 'Bilinçli Hesap (0/1)';

    // Use userBanner data if available for most up-to-date info
    final bannerData = _userBanner.value;
    if (bannerData != null) {
      // Since banner doesn't have role, use session role but with updated banner data
      final role = sessionUser.sessionRole;
      switch (role) {
        case UserRoleEnum.level0:
          return 'Bilinçli Hesap (0/1)';
        case UserRoleEnum.level1:
          return 'Bilinçli Hesap (1/4)';
        case UserRoleEnum.level2:
          return 'Bilinçli Hesap (2/4)';
        case UserRoleEnum.level3:
          return 'Bilinçli Hesap (3/4)';
        case UserRoleEnum.level4:
          return 'Bilinçli Hesap (4/4)';
        case UserRoleEnum.level5:
          return 'Tam Hesap (5/5)';
        case UserRoleEnum.creator:
          return 'iVent Creator Olmak';
        default:
          return 'Bilinçli Hesap (0/1)';
      }
    }

    // Fallback to session role
    final role = sessionUser.sessionRole;
    switch (role) {
      case UserRoleEnum.level0:
        return 'Bilinçli Hesap (0/1)';
      case UserRoleEnum.level1:
        return 'Bilinçli Hesap (1/4)';
      case UserRoleEnum.level2:
        return 'Bilinçli Hesap (2/4)';
      case UserRoleEnum.level3:
        return 'Bilinçli Hesap (3/4)';
      case UserRoleEnum.level4:
        return 'Bilinçli Hesap (4/4)';
      case UserRoleEnum.level5:
        return 'Tam Hesap (5/5)';
      case UserRoleEnum.creator:
        return 'iVent Creator Olmak';
      default:
        return 'Bilinçli Hesap (0/1)';
    }
  }

  String get levelDescription {
    if (!authService.isLoggedIn) return 'Hesap bilgilerini tamamla.';
    final role = sessionUser.sessionRole;
    switch (role) {
      case UserRoleEnum.level0:
        return 'Hesap bilgilerini tamamla.';
      case UserRoleEnum.level1:
      case UserRoleEnum.level2:
      case UserRoleEnum.level3:
      case UserRoleEnum.level4:
      case UserRoleEnum.level5:
        return 'Favorilere 5 iVent ekle.\nBir Arkadaşınla iVent\'e katıl.\nEn az 2 kişilik bir Memories oluştur.\nArkadaş Grubu oluştur.';
      case UserRoleEnum.creator:
        return 'Eşsiz Deneyimler ve Çok Daha Fazlası...';
      default:
        return 'Hesap bilgilerini tamamla.';
    }
  }

  bool get showEduVerification {
    if (!authService.isLoggedIn) return false;
    return sessionUser.sessionRole != UserRoleEnum.creator;
  }

  bool get showCreatorRequest {
    if (!authService.isLoggedIn) return false;
    return sessionUser.sessionRole != UserRoleEnum.creator;
  }

  // Setters

  // Lifecycle methods
  @override
  Future<void> initController() async {
    super.initController();
    await loadSideMenuData();
  }

  // Public methods

  /// Loads all necessary data for side menu
  Future<void> loadSideMenuData() async {
    try {
      await Future.wait([
        _loadUserBanner(),
        _loadUserPages(),
      ]);
    } catch (e) {
      // Handle error silently
    } finally {}
  }

  /// Refreshes side menu data after profile updates
  Future<void> refreshSideMenuData() async {
    debugPrint('ProfileSideMenu: Refreshing side menu data...');
    await loadSideMenuData();
    // Force UI update by triggering reactive updates
    update();
  }

  /// Handles edu.tr verification button tap
  void onEduVerificationTap() {
    debugPrint('ProfileSideMenu: Navigating to edu.tr verification');
    Get.to(() => const EduVerificationPage());
  }

  /// Handles iVent Creator request button tap
  void onCreatorRequestTap() {
    debugPrint('ProfileSideMenu: Navigating to creator request');
    try {
      // Binding ile controller otomatik oluşturulacak
      Get.toNamed('/creator_request_step1');
    } catch (e) {
      debugPrint('ProfileSideMenu: Creator request navigation failed: $e');
      // Fallback olarak direct navigation
      try {
        if (!Get.isRegistered<CreatorRequestController>()) {
          Get.lazyPut(() => CreatorRequestController(authService, state));
        }
        Get.to(() => const CreatorRequestStep1());
      } catch (e2) {
        debugPrint('ProfileSideMenu: Direct navigation also failed: $e2');
        Get.snackbar(
          'Hata',
          'Creator başvuru sayfası açılamadı. Lütfen tekrar deneyin.',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// Handles page navigation
  void onPageTap(String pageId, String pageName) {
    debugPrint('ProfileSideMenu: Navigating to page: $pageName ($pageId)');

    // Page detail sayfasına yönlendir
    // Bu route'un mevcut olup olmadığını kontrol etmek gerekir
    try {
      Get.toNamed('/page_detail', arguments: {
        'pageId': pageId,
        'pageName': pageName,
      });
    } catch (e) {
      debugPrint('ProfileSideMenu: Page navigation failed: $e');

      // Geçici olarak bilgi mesajı göster
      Get.snackbar(
        'Bilgi',
        '$pageName sayfası açılıyor...',
        duration: const Duration(seconds: 2),
      );
    }
  }

  /// Handles create page button tap
  void onCreatePageTap() {
    Get.toNamed('/page_creation_step1');
  }

  /// Handles level info tap - navigates to level steps page or profile completion
  void onLevelInfoTap() {
    if (currentLevel >= 2) {
      Get.toNamed('/level_steps');
    } else {
      // Level 0 veya 1 kullanıcılar için profil tamamlama sayfasına yönlendir
      Get.snackbar(
        'Bilgi',
        'Hesap bilgilerinizi doldurursanız tam hesap olacaksınız!',
        duration: const Duration(seconds: 2),
      );
      Get.toNamed('/profile_completion');
    }
  }

  /// Handles account management tap - navigates to profile completion page
  void onAccountManagementTap() {
    Get.toNamed('/profile_completion');
  }

  /// Handles delete account tap - shows confirmation dialog and deletes account
  void onDeleteAccountTap() {
    debugPrint('ProfileSideMenu: Delete account requested');
    try {
      Get.find<AuthService>().deleteAccount();
    } catch (e) {
      debugPrint('ProfileSideMenu: Delete account failed: $e');
      Get.snackbar(
        'Hata',
        'Hesap silme işlemi başarısız oldu. Lütfen tekrar deneyin.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Private methods

  /// Loads user banner information
  Future<void> _loadUserBanner() async {
    if (!authService.isLoggedIn) return;

    await runWithLoading(
      () async {
        _userBanner.value = await authService.usersApi.getUserBannerByUserId(sessionUser.sessionId);
      },
      loadingTag: 'loadUserBanner',
    );
  }

  /// Loads user pages
  Future<void> _loadUserPages() async {
    if (!authService.isLoggedIn) {
      _userPages.value = GetPagesByUserIdReturn(
        pages: [],
        pageCount: 0,
      );
      return;
    }

    await runWithLoading(
      () async {
        _userPages.value = await authService.usersApi.getPagesByUserId(sessionUser.sessionId);
      },
      loadingTag: 'loadUserPages',
      onError: () {
        // API başarısız olursa boş liste göster
        _userPages.value = GetPagesByUserIdReturn(
          pages: [],
          pageCount: 0,
        );
      },
    );
  }
}
