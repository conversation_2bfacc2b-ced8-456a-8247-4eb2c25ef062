import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Controller for managing user content (Vibes and memories)
///
/// Handles loading and managing user's Vibe folders and memory folders.
/// Manages the content tab functionality in user profiles.
class ContentController extends BaseController<ProfileSharedState> {
  // Reactive state
  final _vibeFolders = Rxn<GetVibeFoldersByUserIdReturn>();
  final _memoryFolders = Rxn<GetMemoryFoldersByUserIdReturn>();

  // Constructor
  ContentController(AuthService authService, ProfileSharedState state) : super(authService, state);

  // Getters
  GetVibeFoldersByUserIdReturn? get vibeFolders => _vibeFolders.value;
  GetMemoryFoldersByUserIdReturn? get memoryFolders => _memoryFolders.value;

  // Setters
  set vibeFolders(GetVibeFoldersByUserIdReturn? value) => _vibeFolders.value = value;
  set memoryFolders(GetMemoryFoldersByUserIdReturn? value) => _memoryFolders.value = value;

  // Lifecycle methods

  @override
  Future<void> initController() async {
    super.initController();
    await loadFolders();
  }

  // Public methods

  /// Loads user's Vibe and memory folders
  Future<void> loadFolders() async {
    await runWithLoading(
      () async {
        // Load both vibe and memory folders in parallel
        final results = await Future.wait([
          authService.usersApi.getVibeFoldersByUserId(state.userId),
          authService.usersApi.getMemoryFoldersByUserId(state.userId),
        ]);

        vibeFolders = results[0] as GetVibeFoldersByUserIdReturn?;
        memoryFolders = results[1] as GetMemoryFoldersByUserIdReturn?;
      },
      loadingTag: 'loadFolders',
    );
  }
}
