import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/shared/controllers/shared_state.dart';

/// Shared state manager for profile feature
///
/// Manages reactive state for profile-related data including user information,
/// social interactions, and various profile metrics. Provides comprehensive
/// getters, setters, and helper methods for state management.
///
/// Follows the architecture guide's state management patterns with proper
/// reactive variables and helper methods.
class ProfileSharedState extends SharedState {
  /// The user ID for this profile instance
  final String userId;

  /// Constructor
  ProfileSharedState(this.userId);

  // Reactive state variables
  final _userRole = Rx<UserRoleEnum?>(null);
  final _isFollowing = RxBool(false);
  final _relationshipStatus = Rx<UserRelationshipStatusEnum?>(null);
  final _profileViewCount = RxInt(0);
  final _isProfileLoading = RxBool(false);
  final _lastUpdated = Rx<DateTime?>(null);

  // Getters and setters with proper reactive patterns

  /// User role getter
  UserRoleEnum? get userRole => _userRole.value;

  /// User role setter
  set userRole(UserRoleEnum? value) => _userRole.value = value;

  /// Following status getter
  bool get isFollowing => _isFollowing.value;

  /// Following status setter
  set isFollowing(bool value) => _isFollowing.value = value;

  /// Relationship status getter
  UserRelationshipStatusEnum? get relationshipStatus => _relationshipStatus.value;

  /// Relationship status setter
  set relationshipStatus(UserRelationshipStatusEnum? value) => _relationshipStatus.value = value;

  /// Profile view count getter
  int get profileViewCount => _profileViewCount.value;

  /// Profile view count setter
  set profileViewCount(int value) => _profileViewCount.value = value;

  /// Profile loading status getter
  bool get isProfileLoading => _isProfileLoading.value;

  /// Profile loading status setter
  set isProfileLoading(bool value) => _isProfileLoading.value = value;

  /// Last updated timestamp getter
  DateTime? get lastUpdated => _lastUpdated.value;

  /// Last updated timestamp setter
  set lastUpdated(DateTime? value) => _lastUpdated.value = value;

  // Helper methods

  /// Checks if user is a creator
  bool get isCreator => userRole == UserRoleEnum.creator;

  /// Checks if user has a high level role (level 5 or 6)
  bool get isHighLevel => userRole == UserRoleEnum.level5 || userRole == UserRoleEnum.level6;

  /// Checks if friendship is pending
  bool get isFriendshipPending => relationshipStatus == UserRelationshipStatusEnum.pending;

  /// Checks if users are friends
  bool get areFriends => relationshipStatus == UserRelationshipStatusEnum.accepted;

  /// Checks if this is the current user's own profile
  bool isOwnProfile(String currentUserId) => userId == currentUserId;

  /// Increments profile view count
  void incrementViewCount() {
    profileViewCount = profileViewCount + 1;
  }

  /// Updates the last updated timestamp to now
  void updateTimestamp() {
    lastUpdated = DateTime.now();
  }

  /// Resets all profile data to initial state
  void resetProfileData() {
    userRole = null;
    isFollowing = false;
    relationshipStatus = null;
    profileViewCount = 0;
    isProfileLoading = false;
    lastUpdated = null;
  }

  /// Toggles following status
  void toggleFollowing() {
    isFollowing = !isFollowing;
    updateTimestamp();
  }

  /// Updates relationship status and timestamp
  void updateRelationshipStatus(UserRelationshipStatusEnum? status) {
    relationshipStatus = status;
    updateTimestamp();
  }

  /// Validates if profile data is complete
  bool get isProfileDataComplete {
    return userRole != null && lastUpdated != null;
  }

  /// Gets a summary of the current profile state
  Map<String, dynamic> get profileSummary {
    return {
      'userId': userId,
      'userRole': userRole?.value,
      'isFollowing': isFollowing,
      'relationshipStatus': relationshipStatus?.value,
      'profileViewCount': profileViewCount,
      'isProfileLoading': isProfileLoading,
      'lastUpdated': lastUpdated?.toIso8601String(),
      'isCreator': isCreator,
      'isHighLevel': isHighLevel,
      'isFriendshipPending': isFriendshipPending,
      'areFriends': areFriends,
    };
  }
}
