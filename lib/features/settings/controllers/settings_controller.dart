import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/settings/controllers/settings_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';
import 'package:url_launcher/url_launcher.dart';

/// Main settings controller
///
/// Handles navigation between different settings pages and manages
/// common settings operations like logout and account deletion.
///
/// Follows the architecture guide's controller patterns with proper BaseController
/// inheritance, initialization order, and comprehensive lifecycle management.
class SettingsController extends BaseController<SettingsSharedState> {
  /// Constructor with dependency injection
  SettingsController() : super(Get.find<AuthService>(), Get.find<SettingsSharedState>());

  // Lifecycle methods

  @override
  Future<void> initController() async {
    super.initController();

    try {
      debugPrint('SettingsController initialized');

      // Initialize any required data
      await _loadInitialData();
    } catch (e) {
      handleError(e);
    }
  }

  @override
  void closeController() {
    try {
      debugPrint('SettingsController cleaned up');
    } catch (e) {
      debugPrint('Error cleaning up SettingsController: $e');
    }

    super.closeController();
  }

  // Navigation methods
  void goToPrivacySettings() {
    Get.toNamed('/settings/privacy');
  }

  void goToSecuritySettings() {
    Get.toNamed('/settings/security');
  }

  void goToSupportPage() {
    Get.toNamed('/settings/support');
  }

  void goToAboutPage() {
    Get.toNamed('/settings/about');
  }

  // Auth methods
  Future<void> logout() async {
    await authService.logout();
  }

  Future<void> deleteAccount(BuildContext context) async {
    await authService.deleteAccountWithContext(context);
  }

  // External link methods
  Future<void> openWebsite() async {
    final Uri url = Uri.parse('https://ivent.app/#');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> openPrivacyPolicy() async {
    Get.toNamed('/settings/pdf-viewer', arguments: {
      'title': 'Gizlilik Politikası',
      'url': 'https://ivent.app/I%20Vent%20Gizlilik%20Politikas%C4%B1.pdf'
    });
  }

  Future<void> openTermsOfService() async {
    Get.toNamed('/settings/pdf-viewer', arguments: {
      'title': 'Kullanım Şartları',
      'url': 'https://ivent.app/I%20Vent%20Kullan%C4%B1m%20%C5%9Eartlar%C4%B1.pdf'
    });
  }

  // Private methods

  /// Loads initial data for settings
  Future<void> _loadInitialData() async {
    await runWithLoading(
      () async {
        // Load any required initial data here
        // For example, load user preferences, settings, etc.
      },
      loadingTag: 'initData',
    );
  }
}
