import 'package:get/get.dart';
import 'package:ivent_app/shared/controllers/shared_state.dart';

/// Shared state manager for settings feature
///
/// Manages reactive state for settings functionality including user preferences,
/// privacy settings, security settings, and notification preferences. Provides
/// comprehensive getters, setters, and helper methods for state management.
///
/// Follows the architecture guide's state management patterns with proper
/// reactive variables and helper methods.
class SettingsSharedState extends SharedState {
  /// Constructor
  SettingsSharedState();

  // Reactive state variables
  final _isDarkMode = RxBool(false);
  final _language = RxString('tr');
  final _notificationsEnabled = RxBool(true);
  final _pushNotificationsEnabled = RxBool(true);
  final _emailNotificationsEnabled = RxBool(false);
  final _smsNotificationsEnabled = RxBool(false);
  final _isPrivateAccount = RxBool(false);
  final _twoFactorEnabled = RxBool(false);
  final _loginAlertsEnabled = RxBool(true);
  final _lastUpdated = Rx<DateTime?>(null);
  final _isDirty = RxBool(false);
  final _settingsVersion = RxInt(1);
  final _autoSaveEnabled = RxBool(true);

  // Getters and setters with proper reactive patterns

  /// Dark mode getter
  bool get isDarkMode => _isDarkMode.value;

  /// Dark mode setter
  set isDarkMode(bool value) {
    _isDarkMode.value = value;
    updateTimestamp();
  }

  /// Language getter
  String get language => _language.value;

  /// Language setter
  set language(String value) {
    _language.value = value;
    updateTimestamp();
  }

  /// Notifications enabled getter
  bool get notificationsEnabled => _notificationsEnabled.value;

  /// Notifications enabled setter
  set notificationsEnabled(bool value) {
    _notificationsEnabled.value = value;
    updateTimestamp();
  }

  /// Push notifications enabled getter
  bool get pushNotificationsEnabled => _pushNotificationsEnabled.value;

  /// Push notifications enabled setter
  set pushNotificationsEnabled(bool value) {
    _pushNotificationsEnabled.value = value;
    updateTimestamp();
  }

  /// Email notifications enabled getter
  bool get emailNotificationsEnabled => _emailNotificationsEnabled.value;

  /// Email notifications enabled setter
  set emailNotificationsEnabled(bool value) {
    _emailNotificationsEnabled.value = value;
    updateTimestamp();
  }

  /// SMS notifications enabled getter
  bool get smsNotificationsEnabled => _smsNotificationsEnabled.value;

  /// SMS notifications enabled setter
  set smsNotificationsEnabled(bool value) {
    _smsNotificationsEnabled.value = value;
    updateTimestamp();
  }

  /// Private account getter
  bool get isPrivateAccount => _isPrivateAccount.value;

  /// Private account setter
  set isPrivateAccount(bool value) {
    _isPrivateAccount.value = value;
    updateTimestamp();
  }

  /// Two factor enabled getter
  bool get twoFactorEnabled => _twoFactorEnabled.value;

  /// Two factor enabled setter
  set twoFactorEnabled(bool value) {
    _twoFactorEnabled.value = value;
    updateTimestamp();
  }

  /// Login alerts enabled getter
  bool get loginAlertsEnabled => _loginAlertsEnabled.value;

  /// Login alerts enabled setter
  set loginAlertsEnabled(bool value) {
    _loginAlertsEnabled.value = value;
    updateTimestamp();
  }

  /// Last updated timestamp getter
  DateTime? get lastUpdated => _lastUpdated.value;

  /// Last updated timestamp setter
  set lastUpdated(DateTime? value) => _lastUpdated.value = value;

  /// Dirty state getter
  bool get isDirty => _isDirty.value;

  /// Dirty state setter
  set isDirty(bool value) => _isDirty.value = value;

  /// Settings version getter
  int get settingsVersion => _settingsVersion.value;

  /// Settings version setter
  set settingsVersion(int value) {
    _settingsVersion.value = value;
    updateTimestamp();
  }

  /// Auto save enabled getter
  bool get autoSaveEnabled => _autoSaveEnabled.value;

  /// Auto save enabled setter
  set autoSaveEnabled(bool value) {
    _autoSaveEnabled.value = value;
    updateTimestamp();
  }

  // Helper methods

  /// Updates the last updated timestamp to now
  void updateTimestamp() {
    lastUpdated = DateTime.now();
    isDirty = true;
  }

  /// Toggles dark mode
  void toggleDarkMode() {
    isDarkMode = !isDarkMode;
  }

  /// Toggles notifications
  void toggleNotifications() {
    notificationsEnabled = !notificationsEnabled;
  }

  /// Toggles push notifications
  void togglePushNotifications() {
    pushNotificationsEnabled = !pushNotificationsEnabled;
  }

  /// Toggles email notifications
  void toggleEmailNotifications() {
    emailNotificationsEnabled = !emailNotificationsEnabled;
  }

  /// Toggles SMS notifications
  void toggleSmsNotifications() {
    smsNotificationsEnabled = !smsNotificationsEnabled;
  }

  /// Toggles private account
  void togglePrivateAccount() {
    isPrivateAccount = !isPrivateAccount;
  }

  /// Toggles two factor authentication
  void toggleTwoFactor() {
    twoFactorEnabled = !twoFactorEnabled;
  }

  /// Toggles login alerts
  void toggleLoginAlerts() {
    loginAlertsEnabled = !loginAlertsEnabled;
  }

  /// Enables all notifications
  void enableAllNotifications() {
    notificationsEnabled = true;
    pushNotificationsEnabled = true;
    emailNotificationsEnabled = true;
    smsNotificationsEnabled = true;
  }

  /// Disables all notifications
  void disableAllNotifications() {
    notificationsEnabled = false;
    pushNotificationsEnabled = false;
    emailNotificationsEnabled = false;
    smsNotificationsEnabled = false;
  }

  /// Resets all settings to default values
  void resetToDefaults() {
    isDarkMode = false;
    language = 'tr';
    notificationsEnabled = true;
    pushNotificationsEnabled = true;
    emailNotificationsEnabled = false;
    smsNotificationsEnabled = false;
    isPrivateAccount = false;
    twoFactorEnabled = false;
    loginAlertsEnabled = true;
    autoSaveEnabled = true;
    settingsVersion = 1;
    lastUpdated = null;
    isDirty = false;
  }

  /// Checks if any notification is enabled
  bool get hasAnyNotificationEnabled =>
      notificationsEnabled || pushNotificationsEnabled || emailNotificationsEnabled || smsNotificationsEnabled;

  /// Checks if security features are enabled
  bool get hasSecurityFeaturesEnabled => twoFactorEnabled || loginAlertsEnabled;

  /// Gets notification settings summary
  Map<String, bool> get notificationSettings => {
        'notifications': notificationsEnabled,
        'push': pushNotificationsEnabled,
        'email': emailNotificationsEnabled,
        'sms': smsNotificationsEnabled,
      };

  /// Gets security settings summary
  Map<String, bool> get securitySettings => {
        'twoFactor': twoFactorEnabled,
        'loginAlerts': loginAlertsEnabled,
        'privateAccount': isPrivateAccount,
      };

  /// Gets privacy settings summary
  Map<String, bool> get privacySettings => {
        'privateAccount': isPrivateAccount,
      };

  /// Gets general settings summary
  Map<String, dynamic> get generalSettings => {
        'darkMode': isDarkMode,
        'language': language,
        'autoSave': autoSaveEnabled,
      };

  /// Gets a summary of all settings
  Map<String, dynamic> get settingsSummary {
    return {
      'general': generalSettings,
      'notifications': notificationSettings,
      'security': securitySettings,
      'privacy': privacySettings,
      'metadata': {
        'version': settingsVersion,
        'lastUpdated': lastUpdated?.toIso8601String(),
        'isDirty': isDirty,
        'hasAnyNotificationEnabled': hasAnyNotificationEnabled,
        'hasSecurityFeaturesEnabled': hasSecurityFeaturesEnabled,
      },
    };
  }

  /// Validates settings consistency
  bool validateSettings() {
    // If notifications are disabled, all sub-notifications should be disabled
    if (!notificationsEnabled) {
      return !pushNotificationsEnabled && !emailNotificationsEnabled && !smsNotificationsEnabled;
    }
    return true;
  }

  /// Applies settings from a map
  void applySettings(Map<String, dynamic> settings) {
    if (settings.containsKey('darkMode')) {
      isDarkMode = settings['darkMode'] as bool;
    }
    if (settings.containsKey('language')) {
      language = settings['language'] as String;
    }
    if (settings.containsKey('notifications')) {
      notificationsEnabled = settings['notifications'] as bool;
    }
    if (settings.containsKey('pushNotifications')) {
      pushNotificationsEnabled = settings['pushNotifications'] as bool;
    }
    if (settings.containsKey('emailNotifications')) {
      emailNotificationsEnabled = settings['emailNotifications'] as bool;
    }
    if (settings.containsKey('smsNotifications')) {
      smsNotificationsEnabled = settings['smsNotifications'] as bool;
    }
    if (settings.containsKey('privateAccount')) {
      isPrivateAccount = settings['privateAccount'] as bool;
    }
    if (settings.containsKey('twoFactor')) {
      twoFactorEnabled = settings['twoFactor'] as bool;
    }
    if (settings.containsKey('loginAlerts')) {
      loginAlertsEnabled = settings['loginAlerts'] as bool;
    }
    if (settings.containsKey('autoSave')) {
      autoSaveEnabled = settings['autoSave'] as bool;
    }
  }
}
