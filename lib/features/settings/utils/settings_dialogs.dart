import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/constants/community_rules_constants.dart';
import 'package:ivent_app/features/settings/controllers/privacy_settings_controller.dart';

class SettingsDialogs {
  static void showDeleteAccountDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: Text(SettingsConstants.deleteAccountDialogTitle),
        content: Text(SettingsConstants.deleteAccountDialogContent),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(SettingsConstants.cancelButtonText),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.snackbar(
                'Bilgi',
                SettingsConstants.accountDeletionComingSoon,
                backgroundColor: Colors.orange,
                colorText: Colors.white,
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(
              SettingsConstants.deleteButtonText,
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  static void showUnblockDialog(
    BuildContext context,
    PrivacySettingsController controller,
    String userId,
    String username,
  ) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColors.background,
        title: Text(
          SettingsConstants.unblockDialogTitle,
          style: AppTextStyles.size16Bold,
        ),
        content: Text(
          '$username${SettingsConstants.unblockDialogContent}',
          style: AppTextStyles.size14Regular,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              SettingsConstants.cancelButtonText,
              style: AppTextStyles.size14Medium.copyWith(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.unblockUser(userId, username);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.white,
            ),
            child: Text(
              SettingsConstants.unblockButtonText,
              style: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
            ),
          ),
        ],
      ),
    );
  }

  static void showFaqDialog(String title, String content) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColors.background,
        title: Text(
          title,
          style: AppTextStyles.size16Bold,
        ),
        content: SingleChildScrollView(
          child: Text(
            content,
            style: AppTextStyles.size14Regular,
          ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Get.back(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
            ),
            child: Text(
              SettingsConstants.understoodButtonText,
              style: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
            ),
          ),
        ],
      ),
    );
  }

  static void showCommunityRulesDialog() {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColors.background,
        title: Text(
          SettingsConstants.communityRulesDialogTitle,
          style: AppTextStyles.size16Bold,
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                SettingsConstants.communityRulesIntro,
                style: AppTextStyles.size14Regular,
              ),
              const SizedBox(height: AppDimensions.padding16),
              ...CommunityRulesConstants.rules.map((rule) => _buildRuleItem(rule.title, rule.description)),
              const SizedBox(height: AppDimensions.padding16),
              Text(
                SettingsConstants.communityRulesWarning,
                style: AppTextStyles.size12Regular.copyWith(
                  color: AppColors.warning,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Get.back(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
            ),
            child: Text(
              SettingsConstants.understoodButtonText,
              style: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildRuleItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.padding12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTextStyles.size14Bold.copyWith(color: AppColors.primary),
          ),
          const SizedBox(height: AppDimensions.padding4),
          Text(
            description,
            style: AppTextStyles.size12Regular.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
