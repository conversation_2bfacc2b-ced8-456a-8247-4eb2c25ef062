class FaqConstants {
  static const String accountCreationTitle = 'Hesap nasıl oluşturulur?';
  static const String accountCreationSubtitle = 'Telefon numaranızla kayıt olun';
  static const String accountCreationContent = 'iVent\'e kaydolmak için:\n\n'
      '1. Telefon numaranızı girin\n'
      '2. SMS ile gelen kodu doğrulayın\n'
      '3. Ad-soyad ve ilgi alanlarınızı seçin\n'
      '4. Profil fotoğrafınızı ekleyin';

  static const String eventCreationTitle = 'iVent nasıl oluşturulur?';
  static const String eventCreationSubtitle = 'Etkinlik oluşturma rehberi';
  static const String eventCreationContent = 'iVent oluşturmak için:\n\n'
      '1. Ana sayfada + butonuna tıklayın\n'
      '2. Etkinlik detaylarını doldurun\n'
      '3. Konum ve tarih seçin\n'
      '4. Fotoğra<PERSON> ekleyin\n'
      '5. Yayınlayın';

  static const String addFriendsTitle = 'Nasıl arkadaş eklerim?';
  static const String addFriendsSubtitle = 'Arkadaş ekleme ve takip etme';
  static const String addFriendsContent = 'Arkadaş eklemek için:\n\n'
      '1. Profil sayfasına gidin\n'
      '2. "Arkadaş Ekle" butonuna tıklayın\n'
      '3. Veya arama yaparak kişileri bulun\n'
      '4. Creator hesapları için "Takip Et" butonunu kullanın';

  static const String accountLevelsTitle = 'Hesap seviyesi nasıl artar?';
  static const String accountLevelsSubtitle = 'Level sistemi hakkında';
  static const String accountLevelsContent = 'Hesap seviyeleri:\n\n'
      '• Level 0: Profil eksik\n'
      '• Level 1: Profil tamamlandı\n'
      '• Level 2: 5 favoriye ekledi\n'
      '• Level 3: iVent\'e katıldı\n'
      '• Level 4: Memories oluşturdu\n'
      '• Level 5: Grup oluşturdu';

  static const String becomeCreatorTitle = 'Creator nasıl olunur?';
  static const String becomeCreatorSubtitle = 'iVent Creator başvurusu';
  static const String becomeCreatorContent = 'Creator olmak için:\n\n'
      '1. Profil sayfasından "Creator Ol" seçin\n'
      '2. Gerekli seviyeleri tamamlayın\n'
      '3. Başvuru formunu doldurun\n'
      '4. İnceleme sürecini bekleyin\n'
      '5. Onaylandıktan sonra Creator özelliklerini kullanın';

  static const List<FaqItem> faqItems = [
    FaqItem(
      title: accountCreationTitle,
      subtitle: accountCreationSubtitle,
      content: accountCreationContent,
    ),
    FaqItem(
      title: eventCreationTitle,
      subtitle: eventCreationSubtitle,
      content: eventCreationContent,
    ),
    FaqItem(
      title: addFriendsTitle,
      subtitle: addFriendsSubtitle,
      content: addFriendsContent,
    ),
    FaqItem(
      title: accountLevelsTitle,
      subtitle: accountLevelsSubtitle,
      content: accountLevelsContent,
    ),
    FaqItem(
      title: becomeCreatorTitle,
      subtitle: becomeCreatorSubtitle,
      content: becomeCreatorContent,
    ),
  ];
}

class FaqItem {
  final String title;
  final String subtitle;
  final String content;

  const FaqItem({
    required this.title,
    required this.subtitle,
    required this.content,
  });
}
