/// Validation constants for settings feature.
///
/// This class contains all validation rules, patterns, and constraints used
/// throughout the settings feature for form validation and data integrity.
///
/// Follows the architecture guide's constants management patterns with proper
/// private constructor and static const declarations.
class SettingsValidation {
  SettingsValidation._(); // Private constructor to prevent instantiation

  // ============================================================================
  // EMAIL VALIDATION
  // ============================================================================

  /// Email regex pattern for validation
  static const String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';

  /// Email minimum length
  static const int emailMinLength = 5;

  /// Email maximum length
  static const int emailMaxLength = 254;

  /// Email validation regex
  static final RegExp emailRegex = RegExp(emailPattern);

  // ============================================================================
  // PHONE VALIDATION
  // ============================================================================

  /// Turkish phone number pattern (with or without country code)
  static const String phonePattern = r'^(\+90|0)?[5][0-9]{9}$';

  /// Phone minimum length (without country code)
  static const int phoneMinLength = 10;

  /// Phone maximum length (with country code)
  static const int phoneMaxLength = 13;

  /// Phone validation regex
  static final RegExp phoneRegex = RegExp(phonePattern);

  /// International phone pattern
  static const String internationalPhonePattern = r'^\+[1-9]\d{1,14}$';

  /// International phone validation regex
  static final RegExp internationalPhoneRegex = RegExp(internationalPhonePattern);

  // ============================================================================
  // PASSWORD VALIDATION
  // ============================================================================

  /// Password minimum length
  static const int passwordMinLength = 8;

  /// Password maximum length
  static const int passwordMaxLength = 128;

  /// Password must contain at least one uppercase letter
  static const String passwordUppercasePattern = r'[A-Z]';

  /// Password must contain at least one lowercase letter
  static const String passwordLowercasePattern = r'[a-z]';

  /// Password must contain at least one digit
  static const String passwordDigitPattern = r'[0-9]';

  /// Password must contain at least one special character
  static const String passwordSpecialCharPattern = r'[!@#$%^&*(),.?":{}|<>]';

  /// Password validation regexes
  static final RegExp passwordUppercaseRegex = RegExp(passwordUppercasePattern);
  static final RegExp passwordLowercaseRegex = RegExp(passwordLowercasePattern);
  static final RegExp passwordDigitRegex = RegExp(passwordDigitPattern);
  static final RegExp passwordSpecialCharRegex = RegExp(passwordSpecialCharPattern);

  // ============================================================================
  // USERNAME VALIDATION
  // ============================================================================

  /// Username minimum length
  static const int usernameMinLength = 3;

  /// Username maximum length
  static const int usernameMaxLength = 30;

  /// Username pattern (alphanumeric and underscore only)
  static const String usernamePattern = r'^[a-zA-Z0-9_]+$';

  /// Username validation regex
  static final RegExp usernameRegex = RegExp(usernamePattern);

  // ============================================================================
  // NAME VALIDATION
  // ============================================================================

  /// Name minimum length
  static const int nameMinLength = 2;

  /// Name maximum length
  static const int nameMaxLength = 50;

  /// Name pattern (letters, spaces, and common name characters)
  static const String namePattern = r'^[a-zA-ZçğıöşüÇĞIİÖŞÜ\s\-\.]+$';

  /// Name validation regex
  static final RegExp nameRegex = RegExp(namePattern);

  // ============================================================================
  // VERIFICATION CODE VALIDATION
  // ============================================================================

  /// Verification code length
  static const int verificationCodeLength = 6;

  /// Verification code pattern (digits only)
  static const String verificationCodePattern = r'^[0-9]{6}$';

  /// Verification code validation regex
  static final RegExp verificationCodeRegex = RegExp(verificationCodePattern);

  // ============================================================================
  // SETTINGS SPECIFIC VALIDATION
  // ============================================================================

  /// Maximum number of blocked users
  static const int maxBlockedUsers = 1000;

  /// Minimum age for account creation
  static const int minAge = 13;

  /// Maximum age for account creation
  static const int maxAge = 120;

  /// Bio maximum length
  static const int bioMaxLength = 500;

  /// Bio minimum length
  static const int bioMinLength = 0;

  // ============================================================================
  // VALIDATION METHODS
  // ============================================================================

  /// Validates email address
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    if (email.length < emailMinLength || email.length > emailMaxLength) return false;
    return emailRegex.hasMatch(email);
  }

  /// Validates Turkish phone number
  static bool isValidPhone(String phone) {
    if (phone.isEmpty) return false;
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    if (cleanPhone.length < phoneMinLength || cleanPhone.length > phoneMaxLength) return false;
    return phoneRegex.hasMatch(cleanPhone);
  }

  /// Validates international phone number
  static bool isValidInternationalPhone(String phone) {
    if (phone.isEmpty) return false;
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    return internationalPhoneRegex.hasMatch(cleanPhone);
  }

  /// Validates password strength
  static bool isValidPassword(String password) {
    if (password.isEmpty) return false;
    if (password.length < passwordMinLength || password.length > passwordMaxLength) return false;
    
    // Check for at least one uppercase letter
    if (!passwordUppercaseRegex.hasMatch(password)) return false;
    
    // Check for at least one lowercase letter
    if (!passwordLowercaseRegex.hasMatch(password)) return false;
    
    // Check for at least one digit
    if (!passwordDigitRegex.hasMatch(password)) return false;
    
    // Check for at least one special character
    if (!passwordSpecialCharRegex.hasMatch(password)) return false;
    
    return true;
  }

  /// Validates username
  static bool isValidUsername(String username) {
    if (username.isEmpty) return false;
    if (username.length < usernameMinLength || username.length > usernameMaxLength) return false;
    return usernameRegex.hasMatch(username);
  }

  /// Validates name
  static bool isValidName(String name) {
    if (name.isEmpty) return false;
    if (name.length < nameMinLength || name.length > nameMaxLength) return false;
    return nameRegex.hasMatch(name);
  }

  /// Validates verification code
  static bool isValidVerificationCode(String code) {
    if (code.isEmpty) return false;
    if (code.length != verificationCodeLength) return false;
    return verificationCodeRegex.hasMatch(code);
  }

  /// Validates bio text
  static bool isValidBio(String bio) {
    if (bio.length < bioMinLength || bio.length > bioMaxLength) return false;
    return true;
  }

  /// Validates age
  static bool isValidAge(int age) {
    return age >= minAge && age <= maxAge;
  }

  /// Gets password strength score (0-4)
  static int getPasswordStrength(String password) {
    int score = 0;
    
    if (password.length >= passwordMinLength) score++;
    if (passwordUppercaseRegex.hasMatch(password)) score++;
    if (passwordLowercaseRegex.hasMatch(password)) score++;
    if (passwordDigitRegex.hasMatch(password)) score++;
    if (passwordSpecialCharRegex.hasMatch(password)) score++;
    
    return score;
  }

  /// Gets password strength description
  static String getPasswordStrengthDescription(int strength) {
    switch (strength) {
      case 0:
      case 1:
        return 'Çok Zayıf';
      case 2:
        return 'Zayıf';
      case 3:
        return 'Orta';
      case 4:
        return 'Güçlü';
      case 5:
        return 'Çok Güçlü';
      default:
        return 'Bilinmiyor';
    }
  }

  /// Formats phone number for display
  static String formatPhoneNumber(String phone) {
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    if (cleanPhone.startsWith('+90')) {
      final number = cleanPhone.substring(3);
      return '+90 ${number.substring(0, 3)} ${number.substring(3, 6)} ${number.substring(6, 8)} ${number.substring(8)}';
    } else if (cleanPhone.startsWith('0')) {
      final number = cleanPhone.substring(1);
      return '0${number.substring(0, 3)} ${number.substring(3, 6)} ${number.substring(6, 8)} ${number.substring(8)}';
    } else if (cleanPhone.length == 10) {
      return '${cleanPhone.substring(0, 3)} ${cleanPhone.substring(3, 6)} ${cleanPhone.substring(6, 8)} ${cleanPhone.substring(8)}';
    }
    
    return phone;
  }

  /// Cleans phone number for API calls
  static String cleanPhoneNumber(String phone) {
    return phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
  }
}
