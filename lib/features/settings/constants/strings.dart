/// String constants for settings feature.
///
/// This class contains all the localized strings used throughout the settings
/// feature, organized by functional areas for better maintainability.
///
/// Follows the architecture guide's constants management patterns with proper
/// private constructor and static const declarations.
class SettingsStrings {
  SettingsStrings._(); // Private constructor to prevent instantiation

  // ============================================================================
  // GENERAL SETTINGS
  // ============================================================================

  /// Settings title
  static const String ayarlar = 'Ayarlar';

  /// Privacy settings
  static const String gizlilikAyarlari = 'Gizlilik Ayarları';

  /// Security settings
  static const String guvenlikAyarlari = 'Güvenlik Ayarları';

  /// Support
  static const String destek = 'Destek';

  /// About
  static const String hakkinda = 'Hakkında';

  /// Account
  static const String hesap = 'Hesap';

  // ============================================================================
  // PRIVACY SETTINGS
  // ============================================================================

  /// Account privacy
  static const String hesapGizliligi = 'Hesap Gizliliği';

  /// Blocked users
  static const String engellenenKullanicilar = 'Engellenen Kullanıcılar';

  /// Private account
  static const String gizliHesap = 'Gizli Hesap';

  /// Block user
  static const String kullaniciEngelle = 'Kullanıcı Engelle';

  /// Unblock user
  static const String engelKaldir = 'Engeli Kaldır';

  /// No blocked users
  static const String engelliKullaniciYok = 'Engelli kullanıcı yok';

  // ============================================================================
  // SECURITY SETTINGS
  // ============================================================================

  /// Email and phone
  static const String epostaVeTelefon = 'E-posta ve Telefon';

  /// Two factor authentication
  static const String ikiFactorDogrulama = 'İki Faktörlü Doğrulama';

  /// Login alerts
  static const String girisUyarilari = 'Giriş Uyarıları';

  /// Change email
  static const String epostaDegistir = 'E-posta Değiştir';

  /// Change phone
  static const String telefonDegistir = 'Telefon Değiştir';

  /// Education status
  static const String egitimDurumu = 'Eğitim Durumu';

  /// Notifications
  static const String bildirimler = 'Bildirimler';

  // ============================================================================
  // SUPPORT
  // ============================================================================

  /// Contact us
  static const String bizimleIletisim = 'Bizimle İletişim';

  /// FAQ
  static const String sikSorulanSorular = 'Sık Sorulan Sorular';

  /// Report a problem
  static const String problemBildir = 'Problem Bildir';

  /// Send feedback
  static const String geriBildirimGonder = 'Geri Bildirim Gönder';

  // ============================================================================
  // ABOUT
  // ============================================================================

  /// Privacy policy
  static const String gizlilikPolitikasi = 'Gizlilik Politikası';

  /// Terms of service
  static const String kullanimSartlari = 'Kullanım Şartları';

  /// Community guidelines
  static const String toplulukKurallari = 'Topluluk Kuralları';

  /// Website
  static const String webSitesi = 'Web Sitesi';

  /// Version
  static const String surum = 'Sürüm';

  /// Legal documents
  static const String yasalBelgeler = 'Yasal Belgeler';

  // ============================================================================
  // ACCOUNT ACTIONS
  // ============================================================================

  /// Logout
  static const String cikisYap = 'Çıkış Yap';

  /// Delete account
  static const String hesabiSil = 'Hesabı Sil';

  /// Logout confirmation
  static const String cikisOnay = 'Çıkış yapmak istediğinizden emin misiniz?';

  /// Delete account confirmation
  static const String hesapSilmeOnay = 'Hesabınızı silmek istediğinizden emin misiniz?';

  /// Account deleted
  static const String hesapSilindi = 'Hesap silindi';

  /// Logged out
  static const String cikisYapildi = 'Çıkış yapıldı';

  // ============================================================================
  // BUTTONS AND ACTIONS
  // ============================================================================

  /// Save
  static const String kaydet = 'Kaydet';

  /// Cancel
  static const String iptal = 'İptal';

  /// Confirm
  static const String onayla = 'Onayla';

  /// Update
  static const String guncelle = 'Güncelle';

  /// Enable
  static const String etkinlestir = 'Etkinleştir';

  /// Disable
  static const String devreDisi = 'Devre Dışı';

  /// Change
  static const String degistir = 'Değiştir';

  /// Remove
  static const String kaldir = 'Kaldır';

  /// Add
  static const String ekle = 'Ekle';

  /// Edit
  static const String duzenle = 'Düzenle';

  /// Delete
  static const String sil = 'Sil';

  /// Block
  static const String engelle = 'Engelle';

  /// Unblock
  static const String engelKaldir2 = 'Engeli Kaldır';

  // ============================================================================
  // VALIDATION MESSAGES
  // ============================================================================

  /// Email required
  static const String epostaGerekli = 'E-posta gereklidir';

  /// Invalid email
  static const String gecersizEposta = 'Geçersiz e-posta adresi';

  /// Phone required
  static const String telefonGerekli = 'Telefon numarası gereklidir';

  /// Invalid phone
  static const String gecersizTelefon = 'Geçersiz telefon numarası';

  /// Password required
  static const String sifreGerekli = 'Şifre gereklidir';

  /// Password too short
  static const String sifreCokKisa = 'Şifre çok kısa';

  /// Passwords do not match
  static const String sifrelerUyusmuyor = 'Şifreler uyuşmuyor';

  // ============================================================================
  // SUCCESS MESSAGES
  // ============================================================================

  /// Settings saved
  static const String ayarlarKaydedildi = 'Ayarlar kaydedildi';

  /// Email updated
  static const String epostaGuncellendi = 'E-posta güncellendi';

  /// Phone updated
  static const String telefonGuncellendi = 'Telefon güncellendi';

  /// User blocked
  static const String kullaniciEngellendi = 'Kullanıcı engellendi';

  /// User unblocked
  static const String kullaniciEngelKaldirildi = 'Kullanıcı engeli kaldırıldı';

  /// Two factor enabled
  static const String ikiFactorEtkinlestirildi = 'İki faktörlü doğrulama etkinleştirildi';

  /// Two factor disabled
  static const String ikiFactorDevreDisi = 'İki faktörlü doğrulama devre dışı bırakıldı';

  // ============================================================================
  // ERROR MESSAGES
  // ============================================================================

  /// Settings save failed
  static const String ayarlarKaydedilemedi = 'Ayarlar kaydedilemedi';

  /// Email update failed
  static const String epostaGuncellenemedi = 'E-posta güncellenemedi';

  /// Phone update failed
  static const String telefonGuncellenemedi = 'Telefon güncellenemedi';

  /// Block user failed
  static const String kullaniciEngellenemedi = 'Kullanıcı engellenemedi';

  /// Unblock user failed
  static const String engelKaldirilamadi = 'Engel kaldırılamadı';

  /// Network error
  static const String agHatasi = 'Ağ bağlantısı hatası';

  /// Unknown error
  static const String bilinmeyenHata = 'Bilinmeyen bir hata oluştu';

  /// Permission denied
  static const String izinReddedildi = 'İzin reddedildi';

  /// Operation failed
  static const String islemBasarisiz = 'İşlem başarısız';

  // ============================================================================
  // LOADING STATES
  // ============================================================================

  /// Loading
  static const String yukleniyor = 'Yükleniyor...';

  /// Saving
  static const String kaydediliyor = 'Kaydediliyor...';

  /// Updating
  static const String guncelleniyor = 'Güncelleniyor...';

  /// Processing
  static const String isleniyor = 'İşleniyor...';

  /// Please wait
  static const String lutfenBekleyin = 'Lütfen bekleyin...';
}
