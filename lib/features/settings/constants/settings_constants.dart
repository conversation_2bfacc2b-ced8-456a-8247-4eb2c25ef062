class SettingsConstants {
  static const String settingsTitle = 'Ayarlar';
  static const String privacySettingsTitle = 'Gizlilik Ayarları';
  static const String securitySettingsTitle = 'Güvenlik';
  static const String supportTitle = 'Destek';
  static const String aboutTitle = 'Hakkında';
  static const String blockedUsersTitle = 'Engellenen Kişiler';
  static const String pdfViewerTitle = 'PDF Görüntüleyici';

  static const String privacySectionTitle = 'Gizlilik';
  static const String securitySectionTitle = 'Güvenlik';
  static const String supportSectionTitle = 'Destek';
  static const String aboutSectionTitle = 'Hakkında';
  static const String accountSectionTitle = 'Hesap';
  static const String blockedUsersSectionTitle = 'Engellenen Kullanıcılar';
  static const String accountInfoSectionTitle = 'Hesap Bilgileri';
  static const String educationStatusSectionTitle = 'Eğitim Durumu';
  static const String notificationSettingsSectionTitle = 'Bildirim Ayarları';
  static const String accountSecuritySectionTitle = 'Hesap Güvenliği';
  static const String contactSectionTitle = 'İletişim';
  static const String faqSectionTitle = 'Sıkça Sorulan Sorular';
  static const String applicationSectionTitle = 'Uygulama';
  static const String legalDocumentsSectionTitle = 'Yasal Belgeler';
  static const String communityRulesSectionTitle = 'Topluluk Kuralları';

  static const String privacySettingsItem = 'Gizlilik Ayarları';
  static const String privacySettingsSubtitle = 'Hesap gizliliği ve engellenen kişiler';
  static const String securitySettingsItem = 'Güvenlik Ayarları';
  static const String securitySettingsSubtitle = 'E-posta, telefon ve hesap güvenliği';
  static const String supportItem = 'Destek';
  static const String supportSubtitle = 'İletişim ve sıkça sorulan sorular';
  static const String aboutItem = 'Hakkında';
  static const String aboutSubtitle = 'Gizlilik politikası ve kullanım şartları';
  static const String logoutItem = 'Çıkış Yap';
  static const String logoutSubtitle = 'Hesabınızdan çıkış yapın';
  static const String deleteAccountItem = 'Hesabı Sil';
  static const String deleteAccountSubtitle = 'Hesabınızı kalıcı olarak silin';

  static const String blockedUsersItem = 'Engellenen Kişiler';
  static const String noBlockedUsersSubtitle = 'Henüz kimseyi engellemediniz';
  static const String blockedUsersCountSubtitle = ' kişi engellendi';

  static const String privacyInfoTitle = 'Gizlilik Bilgisi';
  static const String privacyInfoContent = 'Şu anda sadece engellenen kullanıcılar listesi backend ile entegre çalışmaktadır. '
      'Diğer gizlilik ayarları backend API\'leri hazır olduğunda eklenecektir.';

  static const String noBlockedUsersMessage = 'Engellenen kullanıcı yok';
  static const String noBlockedUsersDescription = 'Henüz hiç kimseyi engellemediniz.';
  static const String unblockButtonText = 'Engeli Kaldır';
  static const String unblockDialogTitle = 'Engeli Kaldır';
  static const String unblockDialogContent = ' kullanıcısının engelini kaldırmak istediğinizden emin misiniz?';
  static const String cancelButtonText = 'İptal';

  static const String emailUpdateItem = 'E-posta Adresi Güncelle';
  static const String emailUpdateSubtitle = 'Yeni e-posta adresi ekle';
  static const String phoneUpdateItem = 'Telefon Numarası Güncelle';
  static const String phoneUpdateSubtitle = 'Yeni telefon numarası ekle';
  static const String emailVerificationItem = 'E-posta Doğrulama Kodu Gönder';
  static const String emailVerificationSubtitle = 'Hesabınızı doğrulamak için kod gönderin';

  static const String educationStatusItem = 'Eğitim Durumu';
  static const String pushNotificationsItem = 'Push Bildirimleri';
  static const String pushNotificationsSubtitle = 'Uygulama bildirimleri';
  static const String emailNotificationsItem = 'E-posta Bildirimleri';
  static const String emailNotificationsSubtitle = 'E-posta ile bildirim al';
  static const String smsNotificationsItem = 'SMS Bildirimleri';
  static const String smsNotificationsSubtitle = 'SMS ile bildirim al';
  static const String eventRemindersItem = 'Etkinlik Hatırlatıcıları';
  static const String eventRemindersSubtitle = 'Etkinlik öncesi hatırlatma';
  static const String socialNotificationsItem = 'Sosyal Bildirimler';
  static const String socialNotificationsSubtitle = 'Arkadaş istekleri, takip bildirimleri';
  static const String marketingEmailsItem = 'Pazarlama E-postaları';
  static const String marketingEmailsSubtitle = 'Kampanya ve duyuru e-postaları';

  static const String twoFactorAuthItem = 'İki Faktörlü Doğrulama';
  static const String twoFactorAuthSubtitle = 'Hesabınıza ekstra güvenlik katmanı ekleyin';
  static const String loginAlertsItem = 'Giriş Uyarıları';
  static const String loginAlertsSubtitle = 'Hesabınıza yeni bir cihazdan giriş yapıldığında bildirim alın';

  static const String deleteAccountDialogTitle = 'Hesabı Sil';
  static const String deleteAccountDialogContent = 'Hesabınızı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.';
  static const String deleteButtonText = 'Sil';
  static const String accountDeletionComingSoon = 'Hesap silme özelliği yakında aktif olacak.';

  static const String contactItem = 'İletişim';
  static const String contactSubtitle = 'Bizimle iletişime geçin';

  static const String helpInfoTitle = 'Yardım';
  static const String helpInfoContent = 'Başka sorularınız varsa "İletişim" bölümünden bizimle iletişime geçebilirsiniz. '
      'Size en kısa sürede yardımcı olmaya çalışacağız.';

  static const String appName = 'iVent';
  static const String appVersion = 'Sürüm 1.0.0';
  static const String appDescription = 'Hepimizin şehrin içinde şehirden uzak hissettiği anlar olmuştur. '
      'Henüz varlığını bile bilmediğin nice tecrübeler çok yakınında. '
      'Ve iVent App\'de bunları keşfedeceksin!';

  static const String privacyPolicyItem = 'Gizlilik Politikası';
  static const String privacyPolicySubtitle = 'Verilerinizin nasıl korunduğu';
  static const String termsOfServiceItem = 'Kullanım Şartları';
  static const String termsOfServiceSubtitle = 'Uygulama kullanım koşulları';
  static const String communityRulesItem = 'Topluluk Kuralları';
  static const String communityRulesSubtitle = 'Güvenli ve saygılı bir ortam için';
  static const String websiteItem = 'Web Sitesi';
  static const String websiteSubtitle = 'ivent.app';

  static const String copyrightText = '© 2025 iVent App';
  static const String rightsReservedText = 'Tüm hakları saklıdır.';

  static const String communityRulesDialogTitle = 'Topluluk Kuralları';
  static const String communityRulesIntro = 'iVent topluluğunda güvenli ve saygılı bir ortam için:';
  static const String communityRulesWarning = 'Bu kurallara uymayan kullanıcılar uyarı alabilir veya hesapları kapatılabilir.';
  static const String understoodButtonText = 'Anladım';

  static const String pdfLoadingText = 'PDF yükleniyor...';
  static const String pdfNotFoundText = 'PDF bulunamadı';
  static const String pdfLoadErrorText = 'PDF yüklenemedi: ';
  static const String pdfLoadingErrorText = 'PDF yüklenirken hata oluştu: ';
  static const String pdfDisplayErrorText = 'PDF görüntülenirken hata oluştu';
  static const String retryButtonText = 'Tekrar Dene';

  static const Map<String, String> educationStatusMap = {
    'unverified': 'Doğrulanmadı',
    'student': 'Öğrenci',
    'grad': 'Mezun',
    'verified': 'Doğrulandı',
  };

  static String getEducationStatusText(String status) {
    return educationStatusMap[status] ?? 'Bilinmiyor';
  }
}
