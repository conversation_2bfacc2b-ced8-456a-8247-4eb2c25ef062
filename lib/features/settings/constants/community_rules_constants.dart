class CommunityRulesConstants {
  static const String rule1Title = '1. Sayg<PERSON><PERSON><PERSON> Olun';
  static const String rule1Description = '<PERSON><PERSON><PERSON> kullan<PERSON>cı<PERSON>a karşı saygılı davranın. <PERSON><PERSON><PERSON>, taciz veya tehdit yasaktır.';

  static const String rule2Title = '2. Gerçek Bilgiler';
  static const String rule2Description = 'Sahte bilgiler paylaşmayın. Etkinlik bilgileri doğru ve güncel olmalıdır.';

  static const String rule3Title = '3. Uygun İçerik';
  static const String rule3Description = '<PERSON><PERSON><PERSON>hcen, şiddet içeren veya uygunsuz içerik paylaşmayın.';

  static const String rule4Title = '4. Telif Hakları';
  static const String rule4Description = 'Başkalarına ait içerikleri izinsiz paylaşmayın.';

  static const String rule5Title = '5. Spam Yasak';
  static const String rule5Description = 'Spam içerik veya tekrarlayan mesajlar göndermeyin.';

  static const String rule6Title = '6. Kişisel Bilgiler';
  static const String rule6Description = 'Kendi veya başkalarının kişisel bilgilerini paylaşmayın.';

  static const List<CommunityRule> rules = [
    CommunityRule(title: rule1Title, description: rule1Description),
    CommunityRule(title: rule2Title, description: rule2Description),
    CommunityRule(title: rule3Title, description: rule3Description),
    CommunityRule(title: rule4Title, description: rule4Description),
    CommunityRule(title: rule5Title, description: rule5Description),
    CommunityRule(title: rule6Title, description: rule6Description),
  ];
}

class CommunityRule {
  final String title;
  final String description;

  const CommunityRule({
    required this.title,
    required this.description,
  });
}
