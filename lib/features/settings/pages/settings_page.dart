import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/common/settings_menu_section.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SettingsController());

    return IaScaffold.noSearch(
      title: SettingsConstants.settingsTitle,
      body: ListView(
        padding: const EdgeInsets.all(AppDimensions.padding20),
        children: [
          SettingsMenuSection(controller: controller),
        ],
      ),
    );
  }
}
