import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/common/settings_info_box.dart';
import 'package:ivent_app/features/settings/widgets/common/settings_section_header.dart';
import 'package:ivent_app/features/settings/widgets/ia_settings_list_tile.dart';
import 'package:ivent_app/features/settings/widgets/support/faq_section.dart';

class SupportPage extends StatelessWidget {
  const SupportPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();

    return IaScaffold.noSearch(
      title: SettingsConstants.supportTitle,
      body: ListView(
        padding: const EdgeInsets.all(AppDimensions.padding20),
        children: [
          const SettingsSectionHeader(title: SettingsConstants.contactSectionTitle),
          IaSettingsListTile(
            icon: AppAssets.mail,
            title: SettingsConstants.contactItem,
            subtitle: SettingsConstants.contactSubtitle,
            onTap: controller.openWebsite,
          ),
          const SizedBox(height: AppDimensions.padding20),
          const SettingsSectionHeader(title: SettingsConstants.faqSectionTitle),
          const FaqSection(),
          const SizedBox(height: AppDimensions.padding20),
          SettingsInfoBox.primary(
            title: SettingsConstants.helpInfoTitle,
            content: SettingsConstants.helpInfoContent,
            iconPath: AppAssets.info,
          ),
        ],
      ),
    );
  }
}
