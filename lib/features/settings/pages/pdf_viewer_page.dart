import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/index.dart';

class PdfViewerPage extends StatefulWidget {
  const PdfViewerPage({Key? key}) : super(key: key);

  @override
  State<PdfViewerPage> createState() => _PdfViewerPageState();
}

class _PdfViewerPageState extends State<PdfViewerPage> {
  String? localPath;
  bool isLoading = true;
  String? error;
  late String title;
  late String url;

  @override
  void initState() {
    super.initState();
    final arguments = Get.arguments as Map<String, String>;
    title = arguments['title'] ?? 'PDF Görüntüleyici';
    url = arguments['url'] ?? '';
    _loadPdf();
  }

  Future<void> _loadPdf() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      // Download PDF
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        // Get temporary directory
        final dir = await getTemporaryDirectory();
        final fileName = path.basename(url);
        final file = File('${dir.path}/$fileName');
        
        // Write PDF to file
        await file.writeAsBytes(response.bodyBytes);
        
        setState(() {
          localPath = file.path;
          isLoading = false;
        });
      } else {
        throw Exception('PDF yüklenemedi: ${response.statusCode}');
      }
    } catch (e) {
      setState(() {
        error = 'PDF yüklenirken hata oluştu: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: title,
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: AppColors.primary),
            SizedBox(height: 16),
            Text('PDF yükleniyor...'),
          ],
        ),
      );
    }

    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              error!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: AppColors.error),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPdf,
              child: const Text('Tekrar Dene'),
            ),
          ],
        ),
      );
    }

    if (localPath == null) {
      return const Center(child: Text('PDF bulunamadı'));
    }

    return PDFView(
      filePath: localPath!,
      enableSwipe: true,
      swipeHorizontal: false,
      autoSpacing: true,
      pageFling: true,
      pageSnap: true,
      defaultPage: 0,
      fitPolicy: FitPolicy.BOTH,
      preventLinkNavigation: false,
      onRender: (pages) {
        debugPrint('PDF rendered with $pages pages');
      },
      onError: (error) {
        debugPrint('PDF error: $error');
        setState(() {
          this.error = 'PDF görüntülenirken hata oluştu';
        });
      },
      onPageError: (page, error) {
        debugPrint('PDF page $page error: $error');
      },
    );
  }
} 