import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/privacy_settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/common/empty_state.dart';
import 'package:ivent_app/features/settings/widgets/privacy/blocked_user_item.dart';

class BlockedUsersPage extends StatelessWidget {
  const BlockedUsersPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<PrivacySettingsController>();

    return IaScaffold.noSearch(
      title: SettingsConstants.blockedUsersTitle,
      body: Obx(() {
        if (controller.isLoading()) {
          return const Center(child: CircularProgressIndicator(color: AppColors.primary));
        }

        if (controller.blockedUsers.isEmpty) {
          return const EmptyState(
            icon: Icons.block,
            title: SettingsConstants.noBlockedUsersMessage,
            description: SettingsConstants.noBlockedUsersDescription,
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(AppDimensions.padding20),
          itemCount: controller.blockedUsers.length,
          itemBuilder: (context, index) {
            final user = controller.blockedUsers[index];
            return BlockedUserItem(
              user: user,
              controller: controller,
            );
          },
        );
      }),
    );
  }
}
