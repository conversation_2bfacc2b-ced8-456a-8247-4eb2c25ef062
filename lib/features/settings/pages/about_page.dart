import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/settings_controller.dart';
import 'package:ivent_app/features/settings/utils/settings_dialogs.dart';
import 'package:ivent_app/features/settings/widgets/about/app_info_section.dart';
import 'package:ivent_app/features/settings/widgets/about/copyright_section.dart';
import 'package:ivent_app/features/settings/widgets/common/settings_section_header.dart';
import 'package:ivent_app/features/settings/widgets/ia_settings_list_tile.dart';

class AboutPage extends StatelessWidget {
  const AboutPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();

    return IaScaffold.noSearch(
      title: SettingsConstants.aboutTitle,
      body: ListView(
        padding: const EdgeInsets.all(AppDimensions.padding20),
        children: [
          const SettingsSectionHeader(title: SettingsConstants.applicationSectionTitle),
          const AppInfoSection(),
          const SizedBox(height: AppDimensions.padding20),
          const SettingsSectionHeader(title: SettingsConstants.legalDocumentsSectionTitle),
          IaSettingsListTile(
            icon: AppAssets.bookOpen,
            title: SettingsConstants.privacyPolicyItem,
            subtitle: SettingsConstants.privacyPolicySubtitle,
            onTap: controller.openPrivacyPolicy,
          ),
          const SizedBox(height: AppDimensions.padding12),
          IaSettingsListTile(
            icon: AppAssets.bookOpen,
            title: SettingsConstants.termsOfServiceItem,
            subtitle: SettingsConstants.termsOfServiceSubtitle,
            onTap: controller.openTermsOfService,
          ),
          const SizedBox(height: AppDimensions.padding20),
          const SettingsSectionHeader(title: SettingsConstants.communityRulesSectionTitle),
          const IaSettingsListTile(
            icon: AppAssets.users,
            title: SettingsConstants.communityRulesItem,
            subtitle: SettingsConstants.communityRulesSubtitle,
            onTap: SettingsDialogs.showCommunityRulesDialog,
          ),
          const SizedBox(height: AppDimensions.padding20),
          const SettingsSectionHeader(title: SettingsConstants.contactSectionTitle),
          IaSettingsListTile(
            icon: AppAssets.mail,
            title: SettingsConstants.websiteItem,
            subtitle: SettingsConstants.websiteSubtitle,
            onTap: controller.openWebsite,
          ),
          const SizedBox(height: AppDimensions.padding20),
          const CopyrightSection(),
        ],
      ),
    );
  }
}
