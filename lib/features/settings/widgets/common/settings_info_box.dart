import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';

class SettingsInfoBox extends StatelessWidget {
  final String title;
  final String content;
  final Color? backgroundColor;
  final Color? borderColor;
  final Color? iconColor;
  final String? iconPath;
  final IconData? icon;

  const SettingsInfoBox({
    Key? key,
    required this.title,
    required this.content,
    this.backgroundColor,
    this.borderColor,
    this.iconColor,
    this.iconPath,
    this.icon,
  }) : super(key: key);

  factory SettingsInfoBox.info({
    required String title,
    required String content,
  }) {
    return SettingsInfoBox(
      title: title,
      content: content,
      backgroundColor: AppColors.info.withValues(alpha: 0.1),
      borderColor: AppColors.info.withValues(alpha: 0.2),
      iconColor: AppColors.info,
      icon: Icons.info_outline,
    );
  }

  factory SettingsInfoBox.primary({
    required String title,
    required String content,
    String? iconPath,
  }) {
    return SettingsInfoBox(
      title: title,
      content: content,
      backgroundColor: AppColors.primary.withValues(alpha: 0.1),
      borderColor: AppColors.primary.withValues(alpha: 0.2),
      iconColor: AppColors.primary,
      iconPath: iconPath,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: borderColor ?? AppColors.info.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (iconPath != null)
                IaSvgIcon(
                  iconPath: iconPath!,
                  iconColor: iconColor ?? AppColors.info,
                  iconSize: 20,
                )
              else
                Icon(
                  icon ?? Icons.info_outline,
                  color: iconColor ?? AppColors.info,
                  size: 20,
                ),
              const SizedBox(width: AppDimensions.padding8),
              Text(
                title,
                style: AppTextStyles.size14BoldPrimary,
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.padding8),
          Text(
            content,
            style: AppTextStyles.size12Regular.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
