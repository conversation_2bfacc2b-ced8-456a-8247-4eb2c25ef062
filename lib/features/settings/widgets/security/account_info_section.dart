import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/security_settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/ia_settings_list_tile.dart';

class AccountInfoSection extends StatelessWidget {
  final SecuritySettingsController controller;

  const AccountInfoSection({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        IaSettingsListTile(
          icon: AppAssets.mail,
          title: SettingsConstants.emailUpdateItem,
          subtitle: SettingsConstants.emailUpdateSubtitle,
          onTap: controller.showEmailUpdateDialog,
        ),
        const SizedBox(height: 8),
        IaSettingsListTile(
          icon: AppAssets.phone,
          title: SettingsConstants.phoneUpdateItem,
          subtitle: SettingsConstants.phoneUpdateSubtitle,
          onTap: controller.showPhoneUpdateDialog,
        ),
        const SizedBox(height: 8),
        IaSettingsListTile(
          icon: AppAssets.mail,
          title: SettingsConstants.emailVerificationItem,
          subtitle: SettingsConstants.emailVerificationSubtitle,
          onTap: controller.sendVerificationEmail,
        ),
      ],
    );
  }
}
