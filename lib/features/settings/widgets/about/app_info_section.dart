import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';

class AppInfoSection extends StatelessWidget {
  const AppInfoSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.grey200, width: 1),
      ),
      child: Column(
        children: [
          Image.asset(
            AppAssets.iventLogo,
            height: 60,
            width: 60,
          ),
          const SizedBox(height: AppDimensions.padding12),
          Text(
            SettingsConstants.appName,
            style: AppTextStyles.size16Bold,
          ),
          const SizedBox(height: AppDimensions.padding4),
          Text(
            SettingsConstants.appVersion,
            style: AppTextStyles.size12Regular.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppDimensions.padding8),
          Text(
            SettingsConstants.appDescription,
            style: AppTextStyles.size12Regular.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
