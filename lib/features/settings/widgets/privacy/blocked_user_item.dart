import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/privacy_settings_controller.dart';
import 'package:ivent_app/features/settings/utils/settings_dialogs.dart';

class BlockedUserItem extends StatelessWidget {
  final dynamic user;
  final PrivacySettingsController controller;

  const BlockedUserItem({
    Key? key,
    required this.user,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.padding12),
      padding: const EdgeInsets.all(AppDimensions.padding16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.grey200, width: 1),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 24,
            backgroundColor: AppColors.grey200,
            backgroundImage: user.avatarUrl != null ? NetworkImage(user.avatarUrl!) : null,
            child: user.avatarUrl == null
                ? const Icon(Icons.person, color: AppColors.grey600)
                : null,
          ),
          const SizedBox(width: AppDimensions.padding16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.username,
                  style: AppTextStyles.size14Bold,
                ),
                if (user.university != null) ...[
                  const SizedBox(height: AppDimensions.padding4),
                  Text(
                    user.university!,
                    style: AppTextStyles.size12Regular.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(width: AppDimensions.padding8),
          ElevatedButton(
            onPressed: () => SettingsDialogs.showUnblockDialog(
              context,
              controller,
              user.userId,
              user.username,
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.padding16,
                vertical: AppDimensions.padding8,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
            ),
            child: Text(
              SettingsConstants.unblockButtonText,
              style: AppTextStyles.size12Bold.copyWith(color: AppColors.white),
            ),
          ),
        ],
      ),
    );
  }
}
