import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_page_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_state_manager.dart';
import 'package:ivent_app/features/vibes/controllers/video_manager.dart';
import 'package:ivent_app/features/vibes/models/vibe.dart';
import 'package:ivent_app/features/vibes/widgets/ia_vibe_widget.dart';
import 'package:visibility_detector/visibility_detector.dart';

/// Main vibes page with vertical scrolling feed
///
/// Displays vibes in a TikTok-style vertical PageView with:
/// - Infinite scroll with pagination
/// - Video playback management
/// - Visibility detection for performance
///
/// Follows the architecture guide's page structure patterns with proper
/// lifecycle management and resource cleanup.
class VibesPage extends StatefulWidget {
  const VibesPage({super.key});

  @override
  State<VibesPage> createState() => _VibesPageState();
}

/// Private state class for the vibes page
class _VibesPageState extends State<VibesPage> with AutomaticKeepAliveClientMixin {
  // Controllers
  final PageController _pageController = PageController();
  late final AuthService _authService;
  late final VibesPageController _vibeController;

  // Getters
  VideoManager get videoManager => _vibeController.videoManager;

  // Lifecycle methods
  @override
  void initState() {
    super.initState();

    // Initialize services and controllers
    _authService = Get.find();
    _vibeController = Get.put(VibesPageController(_authService, VibesSharedState('')));

    // Initialize controller after widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _vibeController.initController();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    Get.delete<VibesPageController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return IaScaffold.empty(
      body: Obx(() {
        final vibeFolders = _vibeController.vibeFolders;
        return PageView.builder(
          controller: _pageController,
          scrollDirection: Axis.vertical,
          itemCount: vibeFolders.length,
          onPageChanged: (index) {
            _vibeController.currentVibePage = index;
          },
          itemBuilder: (context, index) {
            return Obx(() {
              return _buildVibe(vibeFolders[index].currentVibelet);
            });
          },
        );
      }),
    );
  }

  Widget _buildVibe(Vibe vibe) {
    return VisibilityDetector(
      key: Key('vibe-${vibe.content.vibeId}'),
      onVisibilityChanged: (info) {
        if (vibe.isVideo) {
          info.visibleFraction > 0.9 ? vibe.video!.play() : vibe.video!.pause();
        }
      },
      child: IaVibeWidget(
        vibe: vibe,
        onLeftSideTap: _vibeController.getPreviousVibe,
        onRightSideTap: _vibeController.getNextVibe,
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
