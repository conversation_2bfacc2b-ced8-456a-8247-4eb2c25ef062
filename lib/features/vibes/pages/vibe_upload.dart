import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/buttons/vibe_buttons.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_basic_info_tile.dart';
import 'package:ivent_app/features/vibes/controllers/vibe_upload_controller.dart';

/// Vibe upload page for reviewing and sharing captured media
///
/// Displays captured photo/video in full-screen with options to:
/// - Download media to device
/// - Add to memories
/// - Share as vibe
class VibeUpload extends StatefulWidget {
  const VibeUpload({super.key});

  @override
  State<VibeUpload> createState() => _VibeUploadState();
}

class _VibeUploadState extends State<VibeUpload> {
  final VibeUploadController _controller = Get.find();

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            child: Container(
              color: AppColors.black,
              child: Obx(() {
                return Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.rotationY(_controller.isFrontCamera ? pi : 0),
                  child: SizedBox(
                    width: size.width,
                    height: size.height,
                    child: FittedBox(
                      fit: BoxFit.cover,
                      child: Image.file(
                        File(_controller.capturedMediaPath),
                      ),
                    ),
                  ),
                );
              }),
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 40),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  const Spacer(),
                  IaBasicInfoTile.withImageUrl(
                    avatarUrl: _controller.sessionUser.sessionAvatarUrl,
                    avatarSize: 20,
                    title: '@${_controller.sessionUser.sessionUsername}',
                    titleStyle: AppTextStyles.size16Bold.copyWith(color: AppColors.white),
                    // subtitle: 'Açıklamayı Düzenle',
                    // subtitleStyle: AppTextStyles.size14Bold,
                  ),
                  const SizedBox(height: AppDimensions.padding12),
                  Row(
                    children: [
                      VibeButtons.vibesDownload(
                        onTap: () async {
                          final sourceFile = File(_controller.capturedMediaPath);
                          final String destinationPath =
                              '/storage/emulated/0/Download/Ivent_${DateTime.now().millisecondsSinceEpoch}.jpg';
                          await sourceFile.copy(destinationPath);
                        },
                        isDownloaded: false,
                      ),
                      const SizedBox(width: AppDimensions.padding8),
                      Expanded(child: VibeButtons.vibesAddMemories(onTap: () {}, isMemoriesAdded: false)),
                      const SizedBox(width: AppDimensions.padding8),
                      Expanded(child: VibeButtons.vibesShare(onTap: _controller.uploadVibe)),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
