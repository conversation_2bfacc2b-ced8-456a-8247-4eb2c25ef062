import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import 'package:ivent_app/features/vibes/controllers/video_manager.dart';
import 'package:video_player/video_player.dart';

class VideoVibeItem {
  final String videoUrl;
  final String vibeFolderId;
  final VideoManager videoManager;
  late final VideoPlayerController controller;
  late final double aspectRatio;

  VideoVibeItem({
    required this.videoUrl,
    required this.vibeFolderId,
    required this.videoManager,
  });

  var _isInitialized = false.obs;
  var _isLoading = false.obs;

  bool get isInitialized => _isInitialized.value;
  bool get isLoading => _isLoading.value;
  bool get isAvailable => isInitialized || !isLoading;

  set isInitialized(bool value) => _isInitialized.value = value;
  set isLoading(bool value) => _isLoading.value = value;

  Future<void> initializeVideo(CacheManager cacheManager) async {
    if (isInitialized || isLoading) return;
    isLoading = true;

    try {
      final file = await cacheManager.getSingleFile(videoUrl);
      controller = await videoManager.getControllerForVideoByFile(file, vibeFolderId);
    } catch (e) {
      print('Error initializing video: $e, ${e.runtimeType}');
      controller = await videoManager.getControllerForVideoByUrl(videoUrl, vibeFolderId);
    } finally {
      aspectRatio = controller.value.aspectRatio;
      isInitialized = true;
      isLoading = false;
      controller.setLooping(true);
    }
  }

  void play() => isAvailable ? controller.play() : null;
  void pause() => isAvailable ? controller.pause() : null;
  void disposeVideoPlayerController() => isAvailable ? controller.dispose() : null;
}
