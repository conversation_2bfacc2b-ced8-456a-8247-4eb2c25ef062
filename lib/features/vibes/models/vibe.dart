import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/features/vibes/controllers/video_manager.dart';
import 'package:ivent_app/features/vibes/models/video_vibe_item.dart';
import 'package:video_player/video_player.dart';

/// Represents a single vibe (photo or video) with its content and metadata
///
/// Wraps VibeItem from API with additional functionality for video playback
/// management and provides convenient getters for common operations.
class Vibe {
  // Core properties
  final VibeItem content;
  final String vibeFolderId;
  final VideoManager? videoManager;
  final VideoVibeItem? video;

  // Getters
  VideoPlayerController? get videoController => video?.controller;
  bool get isVideo => content.mediaFormat == MediaFormatEnum.video;

  Vibe({
    required this.content,
    required this.vibeFolderId,
    this.videoManager,
  })  : assert(videoManager != null || content.mediaFormat != MediaFormatEnum.video),
        video = content.mediaFormat == MediaFormatEnum.video
            ? VideoVibeItem(videoUrl: content.mediaUrl, vibeFolderId: vibeFolderId, videoManager: videoManager!)
            : null;

  @override
  String toString() {
    return 'Vibe{content: $content, vibeFolderId: $vibeFolderId, isVideo: $isVideo}';
  }
}
