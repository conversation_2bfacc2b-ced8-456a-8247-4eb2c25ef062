/// Validation constants for vibes feature.
///
/// This class contains validation rules and limits used throughout the vibes
/// feature, organized by functional areas for better maintainability and consistency
/// with the project's constants management patterns.
class VibesValidationConstants {
  VibesValidationConstants._(); // Private constructor to prevent instantiation

  // ============================================================================
  // FILE SIZE LIMITS
  // ============================================================================

  /// Maximum file size for images (in MB)
  static const int maxImageSizeMB = 10;

  /// Maximum file size for videos (in MB)
  static const int maxVideoSizeMB = 100;

  /// Maximum file size in bytes for images
  static const int maxImageSizeBytes = maxImageSizeMB * 1024 * 1024;

  /// Maximum file size in bytes for videos
  static const int maxVideoSizeBytes = maxVideoSizeMB * 1024 * 1024;

  // ============================================================================
  // MEDIA DIMENSIONS
  // ============================================================================

  /// Maximum image width in pixels
  static const int maxImageWidth = 4096;

  /// Maximum image height in pixels
  static const int maxImageHeight = 4096;

  /// Minimum image width in pixels
  static const int minImageWidth = 100;

  /// Minimum image height in pixels
  static const int minImageHeight = 100;

  /// Maximum video width in pixels
  static const int maxVideoWidth = 1920;

  /// Maximum video height in pixels
  static const int maxVideoHeight = 1080;

  /// Minimum video width in pixels
  static const int minVideoWidth = 240;

  /// Minimum video height in pixels
  static const int minVideoHeight = 240;

  // ============================================================================
  // VIDEO DURATION LIMITS
  // ============================================================================

  /// Maximum video duration in seconds
  static const int maxVideoDurationSeconds = 300; // 5 minutes

  /// Minimum video duration in seconds
  static const int minVideoDurationSeconds = 1;

  /// Maximum video duration in milliseconds
  static const int maxVideoDurationMs = maxVideoDurationSeconds * 1000;

  /// Minimum video duration in milliseconds
  static const int minVideoDurationMs = minVideoDurationSeconds * 1000;

  // ============================================================================
  // SUPPORTED FORMATS
  // ============================================================================

  /// Supported image formats
  static const List<String> supportedImageFormats = [
    'jpg',
    'jpeg',
    'png',
    'webp',
    'gif'
  ];

  /// Supported video formats
  static const List<String> supportedVideoFormats = [
    'mp4',
    'mov',
    'avi',
    'mkv',
    'webm'
  ];

  /// Supported image MIME types
  static const List<String> supportedImageMimeTypes = [
    'image/jpeg',
    'image/png',
    'image/webp',
    'image/gif'
  ];

  /// Supported video MIME types
  static const List<String> supportedVideoMimeTypes = [
    'video/mp4',
    'video/quicktime',
    'video/x-msvideo',
    'video/x-matroska',
    'video/webm'
  ];

  // ============================================================================
  // PLAYBACK SETTINGS
  // ============================================================================

  /// Minimum volume level (0.0 to 1.0)
  static const double minVolume = 0.0;

  /// Maximum volume level (0.0 to 1.0)
  static const double maxVolume = 1.0;

  /// Default volume level
  static const double defaultVolume = 1.0;

  /// Minimum playback speed
  static const double minPlaybackSpeed = 0.25;

  /// Maximum playback speed
  static const double maxPlaybackSpeed = 2.0;

  /// Default playback speed
  static const double defaultPlaybackSpeed = 1.0;

  /// Available playback speeds
  static const List<double> availablePlaybackSpeeds = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

  // ============================================================================
  // CACHING LIMITS
  // ============================================================================

  /// Maximum number of cached vibes
  static const int maxCachedVibes = 50;

  /// Maximum number of cached video controllers
  static const int maxCachedVideoControllers = 10;

  /// Cache expiration time in hours
  static const int cacheExpirationHours = 24;

  /// Maximum cache size in MB
  static const int maxCacheSizeMB = 500;

  // ============================================================================
  // PAGINATION LIMITS
  // ============================================================================

  /// Default number of vibes to load per page
  static const int defaultVibesPerPage = 10;

  /// Maximum number of vibes to load per page
  static const int maxVibesPerPage = 50;

  /// Minimum number of vibes to load per page
  static const int minVibesPerPage = 1;

  /// Preload count for smooth scrolling
  static const int preloadCount = 3;

  // ============================================================================
  // UI LIMITS
  // ============================================================================

  /// Maximum number of recent searches to store
  static const int maxRecentSearchesCount = 20;

  /// Debounce delay for search input (in milliseconds)
  static const int searchDebounceDelayMs = 300;

  /// Maximum number of view history items
  static const int maxViewHistoryCount = 100;

  /// Maximum number of favorites
  static const int maxFavoritesCount = 1000;

  /// Auto-hide controls delay (in milliseconds)
  static const int autoHideControlsDelayMs = 3000;

  // ============================================================================
  // NETWORK TIMEOUTS
  // ============================================================================

  /// Default timeout for vibe operations (in seconds)
  static const int defaultOperationTimeoutSeconds = 30;

  /// Upload timeout (in seconds)
  static const int uploadTimeoutSeconds = 120;

  /// Download timeout (in seconds)
  static const int downloadTimeoutSeconds = 60;

  /// Video loading timeout (in seconds)
  static const int videoLoadingTimeoutSeconds = 15;

  // ============================================================================
  // VALIDATION PATTERNS
  // ============================================================================

  /// File extension validation pattern
  static const String fileExtensionPattern = r'\.(jpg|jpeg|png|webp|gif|mp4|mov|avi|mkv|webm)$';

  /// Video file extension pattern
  static const String videoExtensionPattern = r'\.(mp4|mov|avi|mkv|webm)$';

  /// Image file extension pattern
  static const String imageExtensionPattern = r'\.(jpg|jpeg|png|webp|gif)$';

  // ============================================================================
  // ERROR CODES
  // ============================================================================

  /// File size validation error codes
  static const String errorFileTooLarge = 'FILE_TOO_LARGE';
  static const String errorImageTooLarge = 'IMAGE_TOO_LARGE';
  static const String errorVideoTooLarge = 'VIDEO_TOO_LARGE';

  /// Format validation error codes
  static const String errorUnsupportedFormat = 'UNSUPPORTED_FORMAT';
  static const String errorUnsupportedImageFormat = 'UNSUPPORTED_IMAGE_FORMAT';
  static const String errorUnsupportedVideoFormat = 'UNSUPPORTED_VIDEO_FORMAT';

  /// Dimension validation error codes
  static const String errorImageDimensionsTooLarge = 'IMAGE_DIMENSIONS_TOO_LARGE';
  static const String errorImageDimensionsTooSmall = 'IMAGE_DIMENSIONS_TOO_SMALL';
  static const String errorVideoDimensionsTooLarge = 'VIDEO_DIMENSIONS_TOO_LARGE';
  static const String errorVideoDimensionsTooSmall = 'VIDEO_DIMENSIONS_TOO_SMALL';

  /// Duration validation error codes
  static const String errorVideoDurationTooLong = 'VIDEO_DURATION_TOO_LONG';
  static const String errorVideoDurationTooShort = 'VIDEO_DURATION_TOO_SHORT';

  /// Playback validation error codes
  static const String errorInvalidVolume = 'INVALID_VOLUME';
  static const String errorInvalidPlaybackSpeed = 'INVALID_PLAYBACK_SPEED';

  /// Network error codes
  static const String errorNetworkTimeout = 'NETWORK_TIMEOUT';
  static const String errorUploadFailed = 'UPLOAD_FAILED';
  static const String errorDownloadFailed = 'DOWNLOAD_FAILED';
  static const String errorVideoLoadFailed = 'VIDEO_LOAD_FAILED';

  /// Permission error codes
  static const String errorCameraPermissionDenied = 'CAMERA_PERMISSION_DENIED';
  static const String errorStoragePermissionDenied = 'STORAGE_PERMISSION_DENIED';
  static const String errorMicrophonePermissionDenied = 'MICROPHONE_PERMISSION_DENIED';
}
