/// String constants for vibes feature.
///
/// This class contains all the localized strings used throughout the vibes
/// feature, organized by functional areas for better maintainability.
///
/// Follows the architecture guide's constants management patterns with proper
/// private constructor and static const declarations.
class VibesStrings {
  VibesStrings._(); // Private constructor to prevent instantiation

  // ============================================================================
  // GENERAL VIBES
  // ============================================================================

  /// Vibes title
  static const String vibes = 'Vibes';

  /// Single vibe title
  static const String vibe = 'Vibe';

  /// Loading vibes message
  static const String vibesYukleniyor = 'Vibes yükleniyor...';

  /// No vibes found message
  static const String vibesBulunamadi = 'Vibes bulunamadı';

  /// End of vibes message
  static const String vibesSonu = 'Vibes sonu';

  // ============================================================================
  // VIDEO CONTROLS
  // ============================================================================

  /// Play button
  static const String oynat = 'Oynat';

  /// Pause button
  static const String duraklat = 'Duraklat';

  /// Mute button
  static const String sesKapat = 'Ses Kapat';

  /// Unmute button
  static const String sesAc = 'Ses Aç';

  /// Volume control
  static const String ses = 'Ses';

  /// Speed control
  static const String hiz = 'Hız';

  /// Fullscreen button
  static const String tamEkran = 'Tam Ekran';

  /// Exit fullscreen button
  static const String tamEkrandanCik = 'Tam Ekrandan Çık';

  // ============================================================================
  // VIBE INTERACTIONS
  // ============================================================================

  /// Like button
  static const String begen = 'Beğen';

  /// Unlike button
  static const String begeniKaldir = 'Beğeniyi Kaldır';

  /// Comment button
  static const String yorumYap = 'Yorum Yap';

  /// Share button
  static const String paylas = 'Paylaş';

  /// Save button
  static const String kaydet = 'Kaydet';

  /// Download button
  static const String indir = 'İndir';

  /// Report button
  static const String sikayet = 'Şikayet Et';

  /// Follow button
  static const String takipEt = 'Takip Et';

  /// Following button
  static const String takipEdiliyor = 'Takip Ediliyor';

  // ============================================================================
  // VIBE UPLOAD
  // ============================================================================

  /// Upload vibe title
  static const String vibeYukle = 'Vibe Yükle';

  /// Select media button
  static const String medyaSec = 'Medya Seç';

  /// Take photo button
  static const String fotografCek = 'Fotoğraf Çek';

  /// Record video button
  static const String videoKaydet = 'Video Kaydet';

  /// Upload button
  static const String yukle = 'Yükle';

  /// Cancel upload button
  static const String yuklemeIptal = 'Yüklemeyi İptal Et';

  /// Add to memories button
  static const String anilaraEkle = 'Anılara Ekle';

  /// Memories added message
  static const String anilaraEklendi = 'Anılara eklendi';

  // ============================================================================
  // CAMERA CONTROLS
  // ============================================================================

  /// Switch camera button
  static const String kameraDegistir = 'Kamera Değiştir';

  /// Front camera
  static const String onKamera = 'Ön Kamera';

  /// Back camera
  static const String arkaKamera = 'Arka Kamera';

  /// Flash on
  static const String flasAc = 'Flaş Aç';

  /// Flash off
  static const String flasKapat = 'Flaş Kapat';

  /// Timer
  static const String zamanlayici = 'Zamanlayıcı';

  // ============================================================================
  // GALLERY
  // ============================================================================

  /// Gallery title
  static const String galeri = 'Galeri';

  /// Recent photos
  static const String sonFotograflar = 'Son Fotoğraflar';

  /// Recent videos
  static const String sonVideolar = 'Son Videolar';

  /// Select from gallery
  static const String galeriden = 'Galeriden Seç';

  // ============================================================================
  // NAVIGATION
  // ============================================================================

  /// Previous vibe
  static const String oncekiVibe = 'Önceki Vibe';

  /// Next vibe
  static const String sonrakiVibe = 'Sonraki Vibe';

  /// Go back
  static const String geriDon = 'Geri Dön';

  /// Close
  static const String kapat = 'Kapat';

  // ============================================================================
  // VALIDATION MESSAGES
  // ============================================================================

  /// File too large error
  static const String dosyaCokBuyuk = 'Dosya çok büyük';

  /// Invalid file format error
  static const String gecersizDosyaFormati = 'Geçersiz dosya formatı';

  /// Upload failed error
  static const String yuklemeBasarisiz = 'Yükleme başarısız';

  /// Network error
  static const String agHatasi = 'Ağ bağlantısı hatası';

  /// Permission denied error
  static const String izinReddedildi = 'İzin reddedildi';

  /// Camera not available error
  static const String kameraMusait = 'Kamera müsait değil';

  // ============================================================================
  // SUCCESS MESSAGES
  // ============================================================================

  /// Upload successful
  static const String yuklemeBasarili = 'Yükleme başarılı';

  /// Vibe saved
  static const String vibeKaydedildi = 'Vibe kaydedildi';

  /// Vibe shared
  static const String vibePaylasildi = 'Vibe paylaşıldı';

  /// Downloaded successfully
  static const String indirmeBasarili = 'İndirme başarılı';

  // ============================================================================
  // ERROR MESSAGES
  // ============================================================================

  /// Failed to load vibes
  static const String vibesYuklenemedi = 'Vibes yüklenemedi';

  /// Failed to play video
  static const String videoOynatilmadi = 'Video oynatılamadı';

  /// Failed to save vibe
  static const String vibeKaydedilemedi = 'Vibe kaydedilemedi';

  /// Failed to share vibe
  static const String vibePaylasilamadi = 'Vibe paylaşılamadı';

  /// Failed to download
  static const String indirilemedi = 'İndirilemedi';

  /// Unknown error
  static const String bilinmeyenHata = 'Bilinmeyen bir hata oluştu';

  // ============================================================================
  // LOADING STATES
  // ============================================================================

  /// Loading
  static const String yukleniyor = 'Yükleniyor...';

  /// Processing
  static const String isleniyor = 'İşleniyor...';

  /// Uploading
  static const String yukleniyor2 = 'Yükleniyor...';

  /// Buffering
  static const String arabellekleniyor = 'Arabelleğe alınıyor...';

  /// Preparing
  static const String hazirlaniyor = 'Hazırlanıyor...';
}
