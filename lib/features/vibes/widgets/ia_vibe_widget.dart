import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';
import 'package:ivent_app/features/vibes/models/vibe.dart';
import 'package:ivent_app/features/vibes/widgets/ia_page_controls.dart';
import 'package:ivent_app/features/vibes/widgets/ia_vibe_overlay.dart';
import 'package:ivent_app/features/vibes/widgets/ia_video_controls.dart';
import 'package:ivent_app/features/vibes/widgets/ia_video_player.dart';

/// Main widget for displaying a single vibe (photo or video)
///
/// Handles both image and video content with overlay controls,
/// navigation gestures, and interactive elements like likes and comments.
///
/// Follows the architecture guide's widget naming conventions with "Ia" prefix
/// and proper widget structure patterns. Provides comprehensive vibe display
/// functionality with proper state management and user interaction handling.
class IaVibeWidget extends StatefulWidget {
  /// The vibe content to display
  final Vibe vibe;

  /// Callback for left side tap gesture
  final VoidCallback? onLeftSideTap;

  /// Callback for right side tap gesture
  final VoidCallback? onRightSideTap;

  /// Creates a vibe widget
  ///
  /// [vibe] is the vibe content to display
  /// [onLeftSideTap] is called when the left side is tapped
  /// [onRightSideTap] is called when the right side is tapped
  const IaVibeWidget({
    super.key,
    required this.vibe,
    this.onLeftSideTap,
    this.onRightSideTap,
  });

  @override
  State<IaVibeWidget> createState() => _IaVibeWidgetState();
}

class _IaVibeWidgetState extends State<IaVibeWidget> {
  // Getters
  Vibe get _vibe => widget.vibe;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.black,
      child: Stack(
        children: [
          _buildVibeBody(),
          _buildGradient(),
          if (_vibe.isVideo) _buildVideoControls(),
          _buildPageControls(),
          _buildVibeOverlay(),
        ],
      ),
    );
  }

  // Private methods

  /// Builds the main content (video or image)
  Widget _buildVibeBody() => _vibe.isVideo ? _buildVideo() : _buildImage();

  /// Builds the gradient overlay
  Widget _buildGradient() => const IaRoundedContainer(gradient: AppColors.gradientVibe);

  Widget _buildPageControls() {
    return IaPageControls(
      onLeftSideTap: widget.onLeftSideTap,
      onRightSideTap: widget.onRightSideTap,
    );
  }

  Widget _buildVibeOverlay() => IaVibeOverlay(vibeContent: _vibe.content);

  Widget _buildVideoControls() {
    if (!_vibe.video!.isAvailable) return const SizedBox.shrink();
    return IaVideoControls(videoPlayerController: _vibe.video!.controller);
  }

  Widget _buildVideo() => IaVideoPlayer(video: _vibe.video!);

  Widget _buildImage() {
    return CachedNetworkImage(
      imageUrl: _vibe.content.mediaUrl,
      placeholder: (context, url) => IaLoadingIndicator.white,
      fit: BoxFit.contain,
      width: double.infinity,
      height: double.infinity,
    );
  }
}
