import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';

class IaPageControls extends StatelessWidget {
  final VoidCallback? onLeftSideTap;
  final VoidCallback? onRightSideTap;

  const IaPageControls({
    super.key,
    this.onLeftSideTap,
    this.onRightSideTap,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildLeftSide(),
        _buildRightSide(),
      ],
    );
  }

  Widget _buildLeftSide() {
    return GestureDetector(
      onTap: onLeftSideTap,
      child: Container(
        width: 100,
        height: double.infinity,
        color: AppColors.transparent,
      ),
    );
  }

  Widget _buildRightSide() {
    return GestureDetector(
      onTap: onRightSideTap,
      child: Container(
        width: 100,
        height: double.infinity,
        color: AppColors.transparent,
      ),
    );
  }
}
