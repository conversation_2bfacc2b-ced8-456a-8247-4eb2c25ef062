import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/buttons/shared_buttons.dart';
import 'package:ivent_app/core/widgets/composite/buttons/vibe_buttons.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_basic_info_tile.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';

class IaVibeOverlay extends StatelessWidget {
  final VibeItem vibeContent;

  const IaVibeOverlay({super.key, required this.vibeContent});

  // UI Constants
  static const double _progressBarHeight = 4;
  static const double _progressBarActiveHeight = 5;
  static const double _progressBarRadius = 2;
  static const double _progressBarActiveRadius = 2.5;

  String get _getReadableDate =>
      DateFormat('MMMM yy').format(DateTime.parse(vibeContent.dates[0])).replaceAll(' ', ' \''); // TODO: FIX

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.padding20),
      child: Column(
        children: [
          const SizedBox(height: AppDimensions.padding8),
          _buildProgressIndicator(context),
          const SizedBox(height: AppDimensions.padding8),
          _buildHeader(),
          const Spacer(),
          _buildInteractionActions(),
          const SizedBox(height: AppDimensions.padding12),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width - 40;
    final itemWidth = screenWidth / vibeContent.vibeCount;
    final currentItemIndex = vibeContent.vibeIndex;

    return Stack(
      children: [
        IaRoundedContainer(
          height: _progressBarHeight,
          width: double.infinity,
          color: AppColors.darkGrey.withValues(alpha: 0.5),
          roundness: _progressBarRadius,
        ),
        Positioned(
          left: 0,
          child: IaRoundedContainer(
            height: _progressBarActiveHeight,
            width: itemWidth * (currentItemIndex + 1),
            color: AppColors.white,
            roundness: _progressBarActiveRadius,
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        SharedButtons.backButton(color: AppColors.white),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                vibeContent.iventName,
                maxLines: 1,
                softWrap: false,
                textAlign: TextAlign.right,
                style: AppTextStyles.size20BoldWhite,
              ),
              Text(
                _getReadableDate,
                maxLines: 1,
                softWrap: false,
                textAlign: TextAlign.right,
                style: AppTextStyles.size14BoldWhite,
              ),
            ],
          ),
        ),
        const SizedBox(width: AppDimensions.padding8),
        const IaIconButton(iconPath: AppAssets.moreVertical),
      ],
    );
  }

  Widget _buildInteractionActions() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Expanded(
          child: IaBasicInfoTile.withImageUrl(
            avatarUrl: vibeContent.creatorAvatarUrl,
            avatarSize: 20,
            title: '@${vibeContent.creatorUsername}',
            titleStyle: AppTextStyles.size14Medium.copyWith(color: AppColors.white),
            subtitle: vibeContent.caption,
            subtitleStyle: AppTextStyles.size14Medium.copyWith(color: AppColors.white),
          ),
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            VibeButtons.like(isLiked: false),
            Text(vibeContent.likeCount.toString(), style: AppTextStyles.size14BoldWhite, textAlign: TextAlign.center),
            const SizedBox(height: AppDimensions.padding16),
            VibeButtons.comment(onTap: () {}),
            Text(vibeContent.commentCount.toString(),
                style: AppTextStyles.size14BoldWhite, textAlign: TextAlign.center),
            const SizedBox(height: AppDimensions.padding16),
            VibeButtons.send(onTap: () {}),
          ],
        ),
        const SizedBox(width: AppDimensions.padding8),
      ],
    );
  }
}
