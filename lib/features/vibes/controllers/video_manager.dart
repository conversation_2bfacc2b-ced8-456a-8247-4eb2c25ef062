import 'dart:io';

import 'package:video_player/video_player.dart';

/// Manages video player controllers for efficient memory usage
///
/// Maintains a pool of video controllers organized by vibe folder ID
/// to prevent memory leaks and optimize video playback performance.
/// Automatically disposes old controllers when limits are exceeded.
class VideoManager {
  // Constants for controller limits
  static const int _maxHorizontalControllers = 3; // Max vibe folders
  static const int _maxVerticalControllers = 5; // Max videos per folder

  // Active controller storage organized by vibe folder ID
  final Map<String, List<VideoPlayerController>> _activeControllers = {};

  // Public methods

  /// Gets or creates a video controller for a network URL
  ///
  /// Returns existing controller if available, otherwise creates new one.
  /// Automatically manages controller limits and disposes old controllers.
  Future<VideoPlayerController> getControllerForVideoByUrl(String url, String vibeFolderId) async {
    // Try to find existing controller for this URL
    if (_activeControllers.containsKey(vibeFolderId)) {
      for (final controller in _activeControllers[vibeFolderId]!) {
        if (controller.dataSource == url) {
          return controller;
        }
      }

      if (_activeControllers[vibeFolderId]!.length >= _maxVerticalControllers) {
        final oldest = _activeControllers[vibeFolderId]!.removeAt(0);
        oldest.dispose();
      }

      final newController = VideoPlayerController.networkUrl(Uri.parse(url));
      await newController.initialize();
      _activeControllers[vibeFolderId]!.add(newController);
      return newController;
    } else {
      if (_activeControllers.length >= _maxHorizontalControllers) {
        final oldest = _activeControllers.remove(_activeControllers.keys.first); // Remove oldest vibe folder
        for (final controller in oldest!) {
          controller.dispose();
        }
      }

      _activeControllers[vibeFolderId] = [];
      final newController = VideoPlayerController.networkUrl(Uri.parse(url));
      await newController.initialize();
      _activeControllers[vibeFolderId]!.add(newController);
      return newController;
    }
  }

  /// Gets or creates a video controller for a local file
  ///
  /// Returns existing controller if available, otherwise creates new one.
  /// Automatically manages controller limits and disposes old controllers.
  Future<VideoPlayerController> getControllerForVideoByFile(File file, String vibeFolderId) async {
    // Try to find existing controller for this file
    if (_activeControllers.containsKey(vibeFolderId)) {
      for (final controller in _activeControllers[vibeFolderId]!) {
        if (controller.dataSource == file.path) {
          return controller;
        }
      }

      if (_activeControllers[vibeFolderId]!.length >= _maxVerticalControllers) {
        final oldest = _activeControllers[vibeFolderId]!.removeAt(0);
        oldest.dispose();
      }

      final newController = VideoPlayerController.file(file);
      await newController.initialize();
      _activeControllers[vibeFolderId]!.add(newController);
      return newController;
    } else {
      if (_activeControllers.length >= _maxHorizontalControllers) {
        final oldest = _activeControllers.remove(_activeControllers.keys.first); // Remove oldest vibe folder
        for (final controller in oldest!) {
          controller.dispose();
        }
      }

      _activeControllers[vibeFolderId] = [];
      final newController = VideoPlayerController.file(file);
      await newController.initialize();
      _activeControllers[vibeFolderId]!.add(newController);
      return newController;
    }
  }

  /// Disposes all active video controllers and clears the cache
  ///
  /// Should be called when the video manager is no longer needed
  /// to prevent memory leaks.
  void disposeAll() {
    for (final controllers in _activeControllers.values) {
      for (final controller in controllers) {
        controller.dispose();
      }
    }
    _activeControllers.clear();
  }
}
