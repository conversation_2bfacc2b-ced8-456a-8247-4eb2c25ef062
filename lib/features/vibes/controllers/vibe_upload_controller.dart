import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/vibes/controllers/base_vibes_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_state_manager.dart';

/// Controller for handling vibe upload functionality
///
/// Manages captured media (photos/videos) from camera and handles
/// the upload process to the server with proper media type detection.
class VibeUploadController extends BaseVibesController {
  VibeUploadController(AuthService authService, VibesSharedState state) : super(authService, state);

  // Observable properties
  final _capturedMediaPath = ''.obs;
  final _isVideo = false.obs;
  final _isFrontCamera = false.obs;

  // Getters
  String get capturedMediaPath => _capturedMediaPath.value;
  bool get isVideo => _isVideo.value;
  bool get isFrontCamera => _isFrontCamera.value;

  // Setters
  set capturedMediaPath(String value) => _capturedMediaPath.value = value;
  set isVideo(bool value) => _isVideo.value = value;
  set isFrontCamera(bool value) => _isFrontCamera.value = value;

  /// Uploads the captured media as a vibe to the server
  ///
  /// Automatically detects media type and applies appropriate content type.
  /// Uses hardcoded ivent ID for now - should be made configurable.
  Future<void> uploadVibe() async {
    try {
      await authService.vibesApi.createVibe(
        await http.MultipartFile.fromPath(
          'file',
          capturedMediaPath,
          contentType: isVideo ? MediaType('video', 'mp4') : MediaType('image', 'jpeg'),
        ),
        '05e98cfc-b8a5-4f98-ae4e-f4ccf4d64977', // TODO: Make ivent ID configurable
        VibePrivacyEnum.public,
      );
    } catch (error) {
      handleVibesError(error, 'Vibe yüklenirken bir hata oluştu.');
      rethrow;
    }
  }
}
