import 'package:flutter/foundation.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_state_manager.dart';
import 'package:ivent_app/features/vibes/controllers/video_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Base controller for all Vibes feature controllers
///
/// Provides common functionality and shared resources for all controllers
/// within the Vibes feature. This includes video management, caching,
/// authentication service access, and common error handling patterns.
///
/// Follows the architecture guide's controller patterns with proper BaseController
/// inheritance, initialization order, and comprehensive lifecycle management.
/// All Vibes controllers should extend this base class to ensure
/// consistent behavior and access to shared resources.
abstract class BaseVibesController extends BaseController<VibesSharedState> {
  // Shared resources
  final VideoManager videoManager = VideoManager();
  final CacheManager cacheManager = CacheManager(
    Config(
      'vibeCache',
      stalePeriod: const Duration(hours: 1),
      maxNrOfCacheObjects: 100,
    ),
  );

  // Constructor
  BaseVibesController(AuthService authService, VibesSharedState state) : super(authService, state);

  // Lifecycle methods

  @override
  Future<void> initController() async {
    super.initController();

    try {
      debugPrint('BaseVibesController initialized');

      // Initialize video manager and cache
      await _initializeSharedResources();
    } catch (e) {
      handleError(e);
    }
  }

  @override
  void closeController() {
    try {
      // Clean up video resources first
      cleanupVideoResources();

      debugPrint('BaseVibesController cleaned up');
    } catch (e) {
      debugPrint('Error cleaning up BaseVibesController: $e');
    }

    super.closeController();
  }

  // Common methods

  /// Handles common error scenarios for Vibes operations
  void handleVibesError(dynamic error, [String? customMessage]) {
    final message = customMessage ?? 'Vibes yüklenirken bir hata oluştu.';
    print('Vibes Error: $error - $message');
    // Additional error handling can be added here
  }

  /// Cleans up video resources
  void cleanupVideoResources() {
    videoManager.disposeAll();
  }

  // Private methods

  /// Initializes shared resources for vibes functionality
  Future<void> _initializeSharedResources() async {
    // Initialize cache manager if needed
    // Video manager is already initialized in constructor
    debugPrint('Vibes shared resources initialized');
  }
}
