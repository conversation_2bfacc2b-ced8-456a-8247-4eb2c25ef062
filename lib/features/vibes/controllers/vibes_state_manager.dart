import 'package:get/get.dart';
import 'package:ivent_app/shared/controllers/shared_state.dart';

/// State manager for Vibes feature
///
/// Manages shared reactive state across all Vibes controllers.
/// This includes navigation state, UI state, and shared data that needs
/// to be accessible across multiple controllers within the feature.
///
/// Follows the architecture guide's state management patterns with proper
/// reactive variables and helper methods.
class VibesSharedState extends SharedState {
  // Constants
  final String? vibeId;

  // Reactive state variables
  final _hasError = RxBool(false);
  final _errorMessage = RxString('');
  final _currentVibeIndex = RxInt(0);
  final _isPlaying = RxBool(false);
  final _volume = RxDouble(1.0);
  final _playbackSpeed = RxDouble(1.0);
  final _isBuffering = RxBool(false);
  final _totalVibes = RxInt(0);
  final _lastUpdated = Rx<DateTime?>(null);
  final _viewHistory = <String>[].obs;
  final _favorites = <String>[].obs;

  // Constructor
  VibesSharedState([this.vibeId]);

  // Getters and setters with proper reactive patterns

  /// Error state getter
  bool get hasError => _hasError.value;

  /// Error state setter
  set hasError(bool value) {
    _hasError.value = value;
    updateTimestamp();
  }

  /// Error message getter
  String get errorMessage => _errorMessage.value;

  /// Error message setter
  set errorMessage(String value) {
    _errorMessage.value = value;
    updateTimestamp();
  }

  /// Current vibe index getter
  int get currentVibeIndex => _currentVibeIndex.value;

  /// Current vibe index setter
  set currentVibeIndex(int value) {
    _currentVibeIndex.value = value;
    updateTimestamp();
  }

  /// Playing state getter
  bool get isPlaying => _isPlaying.value;

  /// Playing state setter
  set isPlaying(bool value) {
    _isPlaying.value = value;
    updateTimestamp();
  }

  /// Volume getter
  double get volume => _volume.value;

  /// Volume setter
  set volume(double value) {
    _volume.value = value.clamp(0.0, 1.0);
    updateTimestamp();
  }

  /// Playback speed getter
  double get playbackSpeed => _playbackSpeed.value;

  /// Playback speed setter
  set playbackSpeed(double value) {
    _playbackSpeed.value = value.clamp(0.25, 2.0);
    updateTimestamp();
  }

  /// Buffering state getter
  bool get isBuffering => _isBuffering.value;

  /// Buffering state setter
  set isBuffering(bool value) {
    _isBuffering.value = value;
    updateTimestamp();
  }

  /// Total vibes count getter
  int get totalVibes => _totalVibes.value;

  /// Total vibes count setter
  set totalVibes(int value) {
    _totalVibes.value = value;
    updateTimestamp();
  }

  /// Last updated timestamp getter
  DateTime? get lastUpdated => _lastUpdated.value;

  /// Last updated timestamp setter
  set lastUpdated(DateTime? value) => _lastUpdated.value = value;

  /// View history getter
  List<String> get viewHistory => _viewHistory.toList();

  /// View history setter
  set viewHistory(List<String> value) => _viewHistory.assignAll(value);

  /// Favorites getter
  List<String> get favorites => _favorites.toList();

  /// Favorites setter
  set favorites(List<String> value) => _favorites.assignAll(value);

  // Helper methods

  /// Updates the last updated timestamp to now
  void updateTimestamp() {
    lastUpdated = DateTime.now();
  }

  /// Sets error state with message
  void setError(String message) {
    hasError = true;
    errorMessage = message;
  }

  /// Clears error state
  void clearError() {
    hasError = false;
    errorMessage = '';
  }

  /// Starts playing
  void startPlaying() {
    isPlaying = true;
    isBuffering = false;
  }

  /// Stops playing
  void stopPlaying() {
    isPlaying = false;
    isBuffering = false;
  }

  /// Sets buffering state
  void setBuffering(bool buffering) {
    isBuffering = buffering;
    if (buffering) {
      isPlaying = false;
    }
  }

  /// Moves to next vibe
  void nextVibe() {
    if (currentVibeIndex < totalVibes - 1) {
      currentVibeIndex = currentVibeIndex + 1;
    }
  }

  /// Moves to previous vibe
  void previousVibe() {
    if (currentVibeIndex > 0) {
      currentVibeIndex = currentVibeIndex - 1;
    }
  }

  /// Adds vibe to view history
  void addToViewHistory(String vibeId) {
    if (!_viewHistory.contains(vibeId)) {
      _viewHistory.add(vibeId);
      // Keep only last 50 items
      if (_viewHistory.length > 50) {
        _viewHistory.removeAt(0);
      }
      updateTimestamp();
    }
  }

  /// Adds vibe to favorites
  void addToFavorites(String vibeId) {
    if (!_favorites.contains(vibeId)) {
      _favorites.add(vibeId);
      updateTimestamp();
    }
  }

  /// Removes vibe from favorites
  void removeFromFavorites(String vibeId) {
    if (_favorites.remove(vibeId)) {
      updateTimestamp();
    }
  }

  /// Checks if vibe is in favorites
  bool isFavorite(String vibeId) => _favorites.contains(vibeId);

  /// Checks if vibe has been viewed
  bool hasBeenViewed(String vibeId) => _viewHistory.contains(vibeId);

  /// Resets all state to initial values
  void resetState() {
    hasError = false;
    errorMessage = '';
    currentVibeIndex = 0;
    isPlaying = false;
    volume = 1.0;
    playbackSpeed = 1.0;
    isBuffering = false;
    totalVibes = 0;
    lastUpdated = null;
    _viewHistory.clear();
    _favorites.clear();
  }

  /// Gets current playback progress (0.0 to 1.0)
  double get playbackProgress {
    if (totalVibes == 0) return 0.0;
    return (currentVibeIndex + 1) / totalVibes;
  }

  /// Checks if this is the first vibe
  bool get isFirstVibe => currentVibeIndex == 0;

  /// Checks if this is the last vibe
  bool get isLastVibe => currentVibeIndex >= totalVibes - 1;

  /// Gets a summary of the current state
  Map<String, dynamic> get stateSummary {
    return {
      'vibeId': vibeId,
      'currentVibeIndex': currentVibeIndex,
      'totalVibes': totalVibes,
      'isPlaying': isPlaying,
      'isBuffering': isBuffering,
      'hasError': hasError,
      'errorMessage': errorMessage,
      'volume': volume,
      'playbackSpeed': playbackSpeed,
      'lastUpdated': lastUpdated?.toIso8601String(),
      'viewHistoryCount': _viewHistory.length,
      'favoritesCount': _favorites.length,
      'playbackProgress': playbackProgress,
      'isFirstVibe': isFirstVibe,
      'isLastVibe': isLastVibe,
    };
  }
}
