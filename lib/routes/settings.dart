import 'package:get/get.dart';
import 'package:ivent_app/features/settings/pages/about_page.dart';
import 'package:ivent_app/features/settings/pages/blocked_users_page.dart';
import 'package:ivent_app/features/settings/pages/pdf_viewer_page.dart';
import 'package:ivent_app/features/settings/pages/privacy_settings_page.dart';
import 'package:ivent_app/features/settings/pages/security_settings_page.dart';
import 'package:ivent_app/features/settings/pages/settings_page.dart';
import 'package:ivent_app/features/settings/pages/support_page.dart';

abstract class SettingRoutes {
  SettingRoutes._();

  static const SETTINGS = '/settings';
  static const PRIVACY_SETTINGS = '/settings/privacy';
  static const SECURITY_SETTINGS = '/settings/security';
  static const SUPPORT = '/settings/support';
  static const ABOUT = '/settings/about';
  static const BLOCKED_USERS = '/settings/blocked-users';
  static const PDF_VIEWER = '/settings/pdf-viewer';
}

final settingsPages = [
  GetPage(
    name: SettingRoutes.SETTINGS,
    page: () => const SettingsPage(),
  ),
  GetPage(
    name: SettingRoutes.PRIVACY_SETTINGS,
    page: () => const PrivacySettingsPage(),
  ),
  GetPage(
    name: SettingRoutes.SECURITY_SETTINGS,
    page: () => const SecuritySettingsPage(),
  ),
  GetPage(
    name: SettingRoutes.SUPPORT,
    page: () => const SupportPage(),
  ),
  GetPage(
    name: SettingRoutes.ABOUT,
    page: () => const AboutPage(),
  ),
  GetPage(
    name: SettingRoutes.BLOCKED_USERS,
    page: () => const BlockedUsersPage(),
  ),
  GetPage(
    name: SettingRoutes.PDF_VIEWER,
    page: () => const PdfViewerPage(),
  ),
];
