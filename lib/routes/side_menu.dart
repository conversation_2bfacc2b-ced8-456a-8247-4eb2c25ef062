import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/profile_completion_controller.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/profile_side_menu_controller.dart';
import 'package:ivent_app/features/profile/pages/profile_completion_page.dart';
import 'package:ivent_app/features/profile/pages/profile_level_steps_page.dart';

abstract class SideMenuRoutes {
  SideMenuRoutes._();

  static const LEVEL_STEPS = '/level_steps';
  static const PROFILE_COMPLETION = '/profile_completion';
}

class SideMenuBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<ProfileSideMenuController>()) return;
    final authService = Get.find<AuthService>();

    Get.lazyPut(() => ProfileSideMenuController(authService, Get.find<ProfileSharedState>()), fenix: true);

    // ProfileCompletionController requires ProfileSharedState
    Get.lazyPut(() => ProfileSharedState(authService.sessionUser!.sessionId), fenix: true);
    Get.lazyPut(() => ProfileCompletionController(authService, Get.find<ProfileSharedState>()), fenix: true);
  }
}

final sideMenuPages = [
  GetPage(
    name: SideMenuRoutes.LEVEL_STEPS,
    page: () => const ProfileLevelStepsPage(),
    binding: SideMenuBindings(),
  ),
  GetPage(
    name: SideMenuRoutes.PROFILE_COMPLETION,
    page: () => const ProfileCompletionPage(),
    binding: SideMenuBindings(),
  ),
];
