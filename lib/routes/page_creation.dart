import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/pages/controllers/page_creation_controller.dart';
import 'package:ivent_app/features/pages/controllers/page_creation_state_manager.dart';
import 'package:ivent_app/features/pages/pages/page_creation_step1.dart';
import 'package:ivent_app/features/pages/pages/page_creation_step2.dart';
import 'package:ivent_app/features/pages/pages/page_creation_step3.dart';
import 'package:ivent_app/features/pages/pages/page_creation_step4.dart';

abstract class PageCreationRoutes {
  PageCreationRoutes._();

  static const STEP1 = '/page_creation_step1';
  static const STEP2 = '/page_creation_step2';
  static const STEP3 = '/page_creation_step3';
  static const STEP4 = '/page_creation_step4';
}

class PageCreationBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<PageCreationController>()) return;
    Get.lazyPut(() => PageCreationController(Get.find<AuthService>(), PageCreationSharedState()), fenix: true);
  }
}

final List<GetPage<dynamic>> pageCreationPages = [
  GetPage(
    name: PageCreationRoutes.STEP1,
    page: () => const PageCreationStep1(),
    binding: PageCreationBindings(),
  ),
  GetPage(
    name: PageCreationRoutes.STEP2,
    page: () => const PageCreationStep2(),
    binding: PageCreationBindings(),
  ),
  GetPage(
    name: PageCreationRoutes.STEP3,
    page: () => const PageCreationStep3(),
    binding: PageCreationBindings(),
  ),
  GetPage(
    name: PageCreationRoutes.STEP4,
    page: () => const PageCreationStep4(),
    binding: PageCreationBindings(),
  ),
];
