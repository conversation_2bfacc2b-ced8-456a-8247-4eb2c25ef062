import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/sub_controllers/creator_request_controller.dart';
import 'package:ivent_app/features/profile/pages/creator_request/creator_request_status.dart';
import 'package:ivent_app/features/profile/pages/creator_request/creator_request_step1.dart';
import 'package:ivent_app/features/profile/pages/creator_request/creator_request_step2.dart';
import 'package:ivent_app/features/profile/pages/creator_request/creator_request_step3.dart';

abstract class CreatorRequestRoutes {
  CreatorRequestRoutes._();

  static const CREATOR_REQUEST_STEP1 = '/creator_request_step1';
  static const CREATOR_REQUEST_STEP2 = '/creator_request_step2';
  static const CREATOR_REQUEST_STEP3 = '/creator_request_step3';
  static const CREATOR_REQUEST_STATUS = '/creator_request_status';
}

// Binding class for Creator Request Controller
class CreatorRequestBinding extends Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<CreatorRequestController>()) return;
    Get.lazyPut<CreatorRequestController>(
      () => CreatorRequestController(Get.find<AuthService>(), Get.find<ProfileSharedState>()),
    );
  }
}

final creatorRequestRoutes = [
  GetPage(
    name: CreatorRequestRoutes.CREATOR_REQUEST_STEP1,
    page: () => const CreatorRequestStep1(),
    binding: CreatorRequestBinding(),
  ),
  GetPage(
    name: CreatorRequestRoutes.CREATOR_REQUEST_STEP2,
    page: () => const CreatorRequestStep2(),
    binding: CreatorRequestBinding(),
  ),
  GetPage(
    name: CreatorRequestRoutes.CREATOR_REQUEST_STEP3,
    page: () => const CreatorRequestStep3(),
    binding: CreatorRequestBinding(),
  ),
  GetPage(
    name: CreatorRequestRoutes.CREATOR_REQUEST_STATUS,
    page: () => const CreatorRequestStatus(),
    binding: CreatorRequestBinding(),
  ),
];
