import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/utils/api_client_interceptor.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';

class AuthService extends GetxService {
  late final ApiClient apiClient;
  late final AuthApi authApi;
  late final CommentsApi commentsApi;
  late final GroupsApi groupsApi;
  late final GroupMembershipsApi groupMembershipsApi;
  late final HobbiesApi hobbiesApi;
  late final HomeApi homeApi;
  late final IventsApi iventsApi;
  late final IventCollabsApi iventCollabsApi;
  late final LocationsApi locationsApi;
  late final MapboxApi mapboxApi;
  late final MemoriesApi memoriesApi;
  late final NotificationsApi notificationsApi;
  late final PagesApi pagesApi;
  late final PageBlacklistsApi pageBlacklistsApi;
  late final PageMembershipsApi pageMembershipsApi;
  late final SquadMembershipsApi squadMembershipsApi;
  late final UniversitiesApi universitiesApi;
  late final UsersApi usersApi;
  late final UserRelationshipsApi userRelationshipsApi;
  late final VibesApi vibesApi;

  final basePath = dotenv.env['BASE_PATH']!;

  final _sessionUser = Rxn<SessionUser>();
  final _hasUpcomingEventToday = false.obs;

  SessionUser? get sessionUser => _sessionUser.value;
  bool get hasUpcomingEventToday => _hasUpcomingEventToday.value;
  bool get isLoggedIn => _sessionUser.value != null;

  Future<void> login(SessionUser userData) async {
    _sessionUser.value = userData;
    SharedPrefs.setUserToCache(userData);
    apiClient.defaultHeaderMap['Authorization'] = 'Bearer ${userData.token}';
  }

  Future<void> logout() async {
    debugPrint('🔄 [AuthService] Starting logout process...');

    // Clear session data
    _sessionUser.value = null;

    // Clear cache
    SharedPrefs.deleteUserFromCache();

    // Remove authorization header
    apiClient.defaultHeaderMap.remove('Authorization');

    // Clear all GetX controllers to prevent memory leaks and null reference errors
    try {
      Get.reset();
      debugPrint('✅ [AuthService] All controllers cleared');
    } catch (e) {
      debugPrint('⚠️ [AuthService] Error clearing controllers: $e');
    }

    // Show success message and let user restart manually
    Get.snackbar(
      'Başarılı',
      'Çıkış yapıldı. Lütfen uygulamayı yeniden başlatın.',
      backgroundColor: AppColors.success,
      colorText: AppColors.white,
      duration: const Duration(seconds: 5),
    );

    debugPrint('✅ [AuthService] Logout completed successfully');
  }

  Future<void> logoutWithContext(BuildContext context) async {
    debugPrint('🔄 [AuthService] Starting logout process with context...');

    // Clear session data
    _sessionUser.value = null;

    // Clear cache
    SharedPrefs.deleteUserFromCache();

    // Remove authorization header
    apiClient.defaultHeaderMap.remove('Authorization');

    // Clear all GetX controllers to prevent memory leaks and null reference errors
    try {
      Get.reset();
      debugPrint('✅ [AuthService] All controllers cleared');
    } catch (e) {
      debugPrint('⚠️ [AuthService] Error clearing controllers: $e');
    }

    // Show success message
    Get.snackbar(
      'Başarılı',
      'Çıkış yapıldı. Lütfen uygulamayı yeniden başlatın.',
      backgroundColor: AppColors.success,
      colorText: AppColors.white,
      duration: const Duration(seconds: 5),
    );

    debugPrint('✅ [AuthService] Logout with context completed successfully');
  }

  Future<void> deleteAccount() async {
    if (!isLoggedIn) {
      debugPrint('❌ [AuthService] Cannot delete account: User not logged in');
      Get.snackbar(
        'Hata',
        'Hesap silme işlemi için oturum açmanız gerekiyor.',
        backgroundColor: AppColors.error,
        colorText: AppColors.white,
      );
      return;
    }

    try {
      debugPrint('🔄 [AuthService] Starting account deletion process...');

      // Show loading indicator
      Get.dialog(
        AlertDialog(
          backgroundColor: AppColors.background,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(color: AppColors.primary),
              const SizedBox(height: AppDimensions.padding16),
              Text(
                'Hesap siliniyor...',
                style: AppTextStyles.size14Regular,
              ),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      // Call delete API
      await usersApi.deleteByUserId(_sessionUser.value!.sessionId);

      debugPrint('✅ [AuthService] Account deleted successfully');

      // Clear cache immediately
      SharedPrefs.deleteUserFromCache();

      // Close loading dialog
      Get.back();

      // Show brief success message BEFORE clearing data
      Get.snackbar(
        'Başarılı',
        'Hesabınız silindi. Uygulama yeniden başlatılıyor...',
        backgroundColor: AppColors.success,
        colorText: AppColors.white,
        duration: const Duration(seconds: 2),
      );

      // Wait for snackbar to show
      await Future.delayed(const Duration(seconds: 2));

      // Restart the entire app with multiple methods
      await _performAppRestart();

      debugPrint('✅ [AuthService] Account deletion completed successfully');
    } catch (e) {
      debugPrint('❌ [AuthService] Account deletion failed: $e');

      // Close loading dialog
      try {
        Get.back();
      } catch (dialogError) {
        debugPrint('⚠️ [AuthService] Error closing dialog: $dialogError');
      }

      Get.snackbar(
        'Hata',
        'Hesap silme işlemi başarısız oldu. Lütfen tekrar deneyin.',
        backgroundColor: AppColors.error,
        colorText: AppColors.white,
      );
    }
  }

  Future<void> _clearAllData() async {
    debugPrint('🔄 [AuthService] Clearing all data before app restart...');

    // Clear session data
    _sessionUser.value = null;

    // Clear cache
    SharedPrefs.deleteUserFromCache();

    // Remove authorization header
    apiClient.defaultHeaderMap.remove('Authorization');

    // Clear all GetX controllers and routes
    try {
      Get.reset();
      debugPrint('✅ [AuthService] All controllers and routes cleared');
    } catch (e) {
      debugPrint('⚠️ [AuthService] Error clearing controllers: $e');
    }

    debugPrint('✅ [AuthService] All data cleared successfully');
  }

  Future<void> deleteAccountWithContext(BuildContext context) async {
    if (!isLoggedIn) {
      debugPrint('❌ [AuthService] Cannot delete account: User not logged in');
      Get.snackbar(
        'Hata',
        'Hesap silme işlemi için oturum açmanız gerekiyor.',
        backgroundColor: AppColors.error,
        colorText: AppColors.white,
      );
      return;
    }

    try {
      debugPrint('🔄 [AuthService] Starting account deletion process with context...');

      // Show loading indicator
      Get.dialog(
        AlertDialog(
          backgroundColor: AppColors.background,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(color: AppColors.primary),
              const SizedBox(height: AppDimensions.padding16),
              Text(
                'Hesap siliniyor...',
                style: AppTextStyles.size14Regular,
              ),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      // Call delete API
      await usersApi.deleteByUserId(_sessionUser.value!.sessionId);

      debugPrint('✅ [AuthService] Account deleted successfully');

      // Clear cache immediately
      SharedPrefs.deleteUserFromCache();

      // Close loading dialog
      Get.back();

      // Show brief success message BEFORE clearing data
      Get.snackbar(
        'Başarılı',
        'Hesabınız silindi. Uygulama yeniden başlatılıyor...',
        backgroundColor: AppColors.success,
        colorText: AppColors.white,
        duration: const Duration(seconds: 2),
      );

      // Wait for snackbar to show
      await Future.delayed(const Duration(seconds: 2));

      // Restart the entire app with context and multiple methods
      await _performAppRestartWithContext(context);

      debugPrint('✅ [AuthService] Account deletion with context completed successfully');
    } catch (e) {
      debugPrint('❌ [AuthService] Account deletion failed: $e');

      // Close loading dialog
      try {
        Get.back();
      } catch (dialogError) {
        debugPrint('⚠️ [AuthService] Error closing dialog: $dialogError');
      }

      Get.snackbar(
        'Hata',
        'Hesap silme işlemi başarısız oldu. Lütfen tekrar deneyin.',
        backgroundColor: AppColors.error,
        colorText: AppColors.white,
      );
    }
  }

  Future<void> _performAppRestart() async {
    debugPrint('🔄 [AuthService] Attempting to restart app with multiple methods...');

    // Clear all data before restart
    await _clearAllData();

    // Method 1: Try Phoenix.rebirth() with GetX context
    try {
      final context = Get.context;
      if (context != null && context.mounted) {
        debugPrint('🔄 [AuthService] Trying Phoenix.rebirth() with GetX context...');
        Phoenix.rebirth(context);
        debugPrint('✅ [AuthService] Phoenix.rebirth() called successfully');
        return;
      }
    } catch (e) {
      debugPrint('❌ [AuthService] Phoenix.rebirth() failed: $e');
    }

    // Method 2: Try SystemNavigator.pop() to close app
    try {
      debugPrint('🔄 [AuthService] Trying SystemNavigator.pop()...');
      if (Platform.isAndroid) {
        SystemNavigator.pop();
        return;
      }
    } catch (e) {
      debugPrint('❌ [AuthService] SystemNavigator.pop() failed: $e');
    }

    // Method 3: Try exit() for complete app termination
    try {
      debugPrint('🔄 [AuthService] Trying exit()...');
      exit(0);
    } catch (e) {
      debugPrint('❌ [AuthService] exit() failed: $e');
    }

    // Method 4: Fallback - show manual restart message
    _showManualRestartMessage();
  }

  Future<void> _performAppRestartWithContext(BuildContext context) async {
    debugPrint('🔄 [AuthService] Attempting to restart app with context and multiple methods...');

    // Clear all data before restart
    await _clearAllData();

    // Method 1: Try Phoenix.rebirth() with provided context
    try {
      if (context.mounted) {
        debugPrint('🔄 [AuthService] Trying Phoenix.rebirth() with provided context...');
        Phoenix.rebirth(context);
        debugPrint('✅ [AuthService] Phoenix.rebirth() with context called successfully');
        return;
      }
    } catch (e) {
      debugPrint('❌ [AuthService] Phoenix.rebirth() with context failed: $e');
    }

    // Method 2: Try Phoenix.rebirth() with GetX context
    try {
      final getxContext = Get.context;
      if (getxContext != null && getxContext.mounted) {
        debugPrint('🔄 [AuthService] Trying Phoenix.rebirth() with GetX context...');
        Phoenix.rebirth(getxContext);
        debugPrint('✅ [AuthService] Phoenix.rebirth() with GetX context called successfully');
        return;
      }
    } catch (e) {
      debugPrint('❌ [AuthService] Phoenix.rebirth() with GetX context failed: $e');
    }

    // Method 3: Try SystemNavigator.pop() to close app
    try {
      debugPrint('🔄 [AuthService] Trying SystemNavigator.pop()...');
      if (Platform.isAndroid) {
        SystemNavigator.pop();
        return;
      }
    } catch (e) {
      debugPrint('❌ [AuthService] SystemNavigator.pop() failed: $e');
    }

    // Method 4: Try exit() for complete app termination
    try {
      debugPrint('🔄 [AuthService] Trying exit()...');
      exit(0);
    } catch (e) {
      debugPrint('❌ [AuthService] exit() failed: $e');
    }

    // Method 5: Fallback - show manual restart message
    _showManualRestartMessage();
  }

  void _showManualRestartMessage() {
    debugPrint('⚠️ [AuthService] All restart methods failed, showing manual restart message');

    // Try to show message, but don't fail if GetX is not available
    try {
      Get.snackbar(
        'Hesap Silindi',
        'Lütfen uygulamayı manuel olarak kapatıp yeniden açın.',
        backgroundColor: AppColors.success,
        colorText: AppColors.white,
        duration: const Duration(seconds: 10),
        isDismissible: false,
      );
    } catch (e) {
      debugPrint('❌ [AuthService] Cannot show snackbar: $e');
      // If we can't show snackbar, at least log the message
      debugPrint('🔔 [AuthService] USER MESSAGE: Hesap silindi. Lütfen uygulamayı manuel olarak kapatıp yeniden açın.');
    }
  }

  Future<AuthService> init() async {
    _initializeApiClients();

    debugPrint('🔄 [AuthService] Initializing...');
    final sessionUser = await SharedPrefs.getUserFromCache();
    await checkUpcomingEvents();

    if (sessionUser != null) {
      _sessionUser.value = sessionUser;
      apiClient.defaultHeaderMap['Authorization'] = 'Bearer ${sessionUser.token}';
      debugPrint(
          '✅ [AuthService] User loaded from cache: ${sessionUser.sessionFullname} (${sessionUser.sessionUsername})');
    } else {
      debugPrint('⚠️ [AuthService] No user found in cache');
    }

    return this;
  }

  void _initializeApiClients() {
    apiClient = ApiClient(basePath: basePath);

    // Setup DIO pretty logger for iOS debug mode
    apiClient.setupDioClient(baseUrl: basePath);
    debugPrint('🔧 [AuthService] API Client initialized with DIO pretty logger');

    // Also configure the global defaultApiClient to use the same base path and DIO
    defaultApiClient = ApiClient(basePath: basePath);
    defaultApiClient.setupDioClient(baseUrl: basePath);
    debugPrint('🔧 [AuthService] Default API Client configured with DIO');

    authApi = AuthApi(apiClient);
    commentsApi = CommentsApi(apiClient);
    groupsApi = GroupsApi(apiClient);
    groupMembershipsApi = GroupMembershipsApi(apiClient);
    hobbiesApi = HobbiesApi(apiClient);
    homeApi = HomeApi(apiClient);
    iventsApi = IventsApi(apiClient);
    iventCollabsApi = IventCollabsApi(apiClient);
    locationsApi = LocationsApi(apiClient);
    mapboxApi = MapboxApi(apiClient);
    memoriesApi = MemoriesApi(apiClient);
    notificationsApi = NotificationsApi(apiClient);
    pagesApi = PagesApi(apiClient);
    pageBlacklistsApi = PageBlacklistsApi(apiClient);
    pageMembershipsApi = PageMembershipsApi(apiClient);
    squadMembershipsApi = SquadMembershipsApi(apiClient);
    universitiesApi = UniversitiesApi(apiClient);
    usersApi = UsersApi(apiClient);
    userRelationshipsApi = UserRelationshipsApi(apiClient);
    vibesApi = VibesApi(apiClient);
  }

  bool isTokenExpired() {
    if (_sessionUser.value == null || _sessionUser.value?.token == null) {
      return true;
    }
    try {
      final token = _sessionUser.value!.token;
      final expiration = _getTokenExpiration(token);
      return DateTime.now().isAfter(expiration);
    } catch (e) {
      debugPrint('❌ [AuthService] Error checking token expiration: $e');
      return true; // Consider expired on error
    }
  }

  DateTime _getTokenExpiration(String token) {
    // If using JWT:
    final parts = token.split('.');
    if (parts.length != 3) {
      throw Exception('Invalid token format');
    }

    final payload = parts[1];
    final normalized = base64Url.normalize(payload);
    final decoded = utf8.decode(base64Url.decode(normalized));
    final Map<String, dynamic> data = json.decode(decoded);

    // JWT standard expiration claim is 'exp' in seconds since epoch
    if (data.containsKey('exp')) {
      return DateTime.fromMillisecondsSinceEpoch(data['exp'] * 1000);
    }

    throw Exception('Token does not contain expiration');
  }

  Future<void> checkUpcomingEvents() async {}
}
