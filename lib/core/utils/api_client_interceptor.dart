import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:ivent_app/api/api.dart';

/// DIO-based HTTP client wrapper for OpenAPI generated ApiClient
class DioHttpClientWrapper extends http.BaseClient {
  static Dio? _dio;
  static bool _isInitialized = false;

  /// Initialize DIO with pretty logger for iOS debug mode
  static void setupDio({String? baseUrl}) {
    if (_isInitialized) return;

    _dio = Dio(BaseOptions(
      baseUrl: baseUrl ?? 'http://localhost',
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add pretty logger only for iOS debug mode
    if (kDebugMode && Platform.isIOS) {
      _dio!.interceptors.add(
        PrettyDioLogger(
          requestHeader: true,
          requestBody: true,
          responseHeader: true,
          responseBody: true,
          error: true,
          compact: false,
          maxWidth: 120,
          enabled: true,
          logPrint: (object) {
            // Custom log print to ensure body content is visible
            debugPrint(object.toString());
          },
        ),
      );
      debugPrint('🚀 [DIO] Pretty logger enabled for iOS debug mode');
    }

    _isInitialized = true;
    debugPrint('🔧 [DIO] HTTP Client configured with base URL: ${_dio!.options.baseUrl}');
  }

  /// Get DIO instance
  static Dio get instance {
    if (!_isInitialized || _dio == null) {
      setupDio();
    }
    return _dio!;
  }

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    try {
      // Prepare request data
      String? requestData;
      if (request is http.Request && request.body.isNotEmpty) {
        requestData = request.body;
      }
      
      debugPrint('📡 [DIO] ${request.method} ${request.url}');
      if (requestData != null) {
        debugPrint('📦 [DIO] Request Body: $requestData');
      }
      
      // Make DIO request with JSON response type for better logging
      final response = await _dio!.request(
        request.url.toString(),
        data: requestData,
        options: Options(
          method: request.method,
          headers: request.headers,
          responseType: ResponseType.plain, // Changed to plain for better body visibility
        ),
      );

      debugPrint('✅ [DIO] Response Status: ${response.statusCode}');
      debugPrint('📋 [DIO] Response Body: ${response.data}');

      // Convert response data to bytes for StreamedResponse
      final responseBody = response.data?.toString() ?? '';
      final responseBytes = utf8.encode(responseBody);
      
      // Convert DIO response to http.StreamedResponse
      return http.StreamedResponse(
        Stream.fromIterable([responseBytes]),
        response.statusCode!,
        contentLength: responseBytes.length,
        request: request,
        headers: _convertHeaders(response.headers.map),
        isRedirect: response.isRedirect,
        persistentConnection: true,
        reasonPhrase: response.statusMessage,
      );
    } on DioException catch (e) {
      debugPrint('❌ [DIO] Request failed: ${e.message}');
      debugPrint('❌ [DIO] Error Type: ${e.type}');
      
      if (e.response != null) {
        debugPrint('❌ [DIO] Error Status: ${e.response!.statusCode}');
        debugPrint('❌ [DIO] Error Body: ${e.response!.data}');
        
        // Return error response with body
        final errorBody = e.response!.data?.toString() ?? '';
        final errorBytes = utf8.encode(errorBody);
        
        return http.StreamedResponse(
          Stream.fromIterable([errorBytes]),
          e.response!.statusCode!,
          contentLength: errorBytes.length,
          request: request,
          headers: _convertHeaders(e.response!.headers.map),
          reasonPhrase: e.response!.statusMessage,
        );
      } else {
        // Network error
        throw http.ClientException(e.message ?? 'Network error', request.url);
      }
    } catch (e) {
      debugPrint('❌ [DIO] Unexpected error: $e');
      throw http.ClientException('Unexpected error: $e', request.url);
    }
  }

  /// Convert DIO headers format to HTTP client format
  Map<String, String> _convertHeaders(Map<String, List<String>> dioHeaders) {
    return dioHeaders.map((key, value) => MapEntry(key, value.join(', ')));
  }
}

/// Extension to easily setup DIO for ApiClient
extension ApiClientDioExtension on ApiClient {
  /// Setup this ApiClient to use DIO with pretty logger
  void setupDioClient({String? baseUrl}) {
    DioHttpClientWrapper.setupDio(baseUrl: baseUrl);
    client = DioHttpClientWrapper();
    debugPrint('🔄 [DIO] ApiClient configured to use DIO');
  }
} 