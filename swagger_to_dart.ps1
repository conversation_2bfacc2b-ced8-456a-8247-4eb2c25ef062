# Remove existing generated API folder if it exists
Remove-Item -Path ".\generated_api" -Recurse -ErrorAction SilentlyContinue

# Remove existing API folder in the lib directory if it exists
Remove-Item -Path ".\lib\api" -Recurse -ErrorAction SilentlyContinue

# Generate Dart API client code using OpenAPI Generator CLI
openapi-generator-cli generate -i ".\swagger_to_dart\api-json.json" -g dart -o ".\generated_api"

# Move the generated library files to the lib\api folder
Move-Item -Path ".\generated_api\lib" -Destination ".\lib\api\"

# Move the generated documentation files to the lib\api\doc folder
Move-Item -Path ".\generated_api\doc" -Destination ".\lib\api\doc\"

# Remove the temporary generated API folder
Remove-Item -Path ".\generated_api" -Recurse

# Rename methods in the generated API code to match naming conventions
Get-ChildItem ".\lib\api\api\" -Filter "*.dart" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    $newContent = [regex]::Replace($content, '[a-zA-Z]+?Controller(.)', {
            param($match)
            $match.Groups[1].Value.ToLower()
        })
    $newContent | Set-Content $_.FullName
}

# Rename methods in the generated API code documentation to match naming conventions
Get-ChildItem ".\lib\api\doc\" -Filter "*.md" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    $newContent = [regex]::Replace($content, '[a-zA-Z]+?Controller(.)', {
            param($match)
            $match.Groups[1].Value.ToLower()
        })
    $newContent | Set-Content $_.FullName
}
