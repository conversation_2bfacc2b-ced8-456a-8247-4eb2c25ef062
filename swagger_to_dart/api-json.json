{"openapi": "3.0.0", "paths": {"/auth/logout": {"post": {"operationId": "AuthController_logout", "summary": "<PERSON><PERSON><PERSON><PERSON> yapar", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["auth"]}}, "/auth/validate": {"post": {"operationId": "AuthController_validate", "summary": "Telefon numarasına gönderilmiş onay kodunu doğrular ve telefon numarası yeniyse onu kayıt olma say<PERSON>na, değ<PERSON>e giriş sayfasına yönlendirir", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateReturn"}}}}}, "tags": ["auth"]}}, "/auth/send-verification-code": {"post": {"operationId": "AuthController_sendVerificationCode", "summary": "Telefon numarasına onay kodu gönderir", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendVerificationCodeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["auth"]}}, "/comments/create": {"post": {"operationId": "CommentsController_createComment", "summary": "<PERSON><PERSON>ur", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCommentDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCommentReturn"}}}}}, "tags": ["comments"]}}, "/comments/{id}/delete": {"delete": {"operationId": "CommentsController_deleteCommentByCommentId", "summary": "<PERSON><PERSON><PERSON> ile yorumu siler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["comments"]}}, "/groupMemberships/{groupId}/administration/add": {"post": {"operationId": "GroupMembershipsController_addModeratorByGroupId", "summary": "Grubun IDsi ile arkadaş grubundaki birisinin hesabını yönetici yapar", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddModeratorByGroupIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["groupMemberships"]}}, "/groupMemberships/{groupId}/administration/remove": {"post": {"operationId": "GroupMembershipsController_removeModeratorByGroupId", "summary": "Grubun IDsi ile arkadaş grubundaki birisini yöneticilerden çıkartır", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveModeratorByGroupIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["groupMemberships"]}}, "/groupMemberships/{groupId}/administration/transfer": {"post": {"operationId": "GroupMembershipsController_transferAdministrationByGroupId", "summary": "Grubun IDsi ile arkadaş grubundaki birisine yöneticiliği de<PERSON>der", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferAdministrationByGroupIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["groupMemberships"]}}, "/groupMemberships/search/{groupId}": {"get": {"operationId": "GroupMembershipsController_searchInvitableUsersByGroupId", "summary": "Arkadaş grubu oluşturulurken eklenebilecek hesapları listeler", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchUsersForGroupCreationReturn"}}}}}, "tags": ["groupMemberships"]}}, "/groupMemberships/search": {"get": {"operationId": "GroupMembershipsController_searchUsersForGroupCreation", "summary": "Arkadaş grubu oluşturulurken eklenebilecek hesapları listeler", "parameters": [{"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchUsersForGroupCreationReturn"}}}}}, "tags": ["groupMemberships"]}}, "/groupMemberships/{groupId}/search": {"get": {"operationId": "GroupMembershipsController_searchGroupMembersByGroupId", "summary": "Arkadaş grubundaki üyeleri listeler", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchGroupMembersByGroupIdReturn"}}}}}, "tags": ["groupMemberships"]}}, "/groupMemberships/{groupId}/leave": {"post": {"operationId": "GroupMembershipsController_leaveGroupByGroupId", "summary": "Grubun IDsi ile arkadaş grubundan ayrılınır", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["groupMemberships"]}}, "/groupMemberships/{groupId}/invite": {"post": {"operationId": "GroupMembershipsController_inviteMembersByGroupId", "summary": "Grubun IDsi ile bir hesaba davet gönderilir", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InviteMembersByGroupIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["groupMemberships"]}}, "/groupMemberships/{groupId}/remove": {"post": {"operationId": "GroupMembershipsController_removeMemberByGroupId", "summary": "Grubun IDsi ile bir hesap gruptan <PERSON>ı<PERSON>", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveMemberByGroupIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["groupMemberships"]}}, "/groups/create": {"post": {"operationId": "GroupsController_createGroup", "summary": "Arkadaş grubu oluşturur", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGroupDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGroupReturn"}}}}}, "tags": ["groups"]}}, "/groups/{id}/delete": {"delete": {"operationId": "GroupsController_deleteGroupByGroupId", "summary": "Grubun IDsi ile arkadaş grubunu siler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["groups"]}}, "/groups/{id}": {"get": {"operationId": "GroupsController_getGroupByGroupId", "summary": "Grubun IDsi ile arkadaş grubundaki hesapları listeler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetGroupByGroupIdReturn"}}}}}, "tags": ["groups"]}}, "/hobbies/search": {"get": {"operationId": "HobbiesController_searchHobbies", "summary": "Seçilebilecek hobileri listeler", "parameters": [{"name": "type", "required": true, "in": "query", "description": "Type of search to perform - either for the profile or for the default search", "schema": {"$ref": "#/components/schemas/HobbiesSearchOriginEnum"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchHobbiesReturn"}}}}}, "tags": ["hobbies"]}}, "/hobbies/add": {"post": {"operationId": "HobbiesController_addHobbiesByHobbyId", "summary": "<PERSON><PERSON> ho<PERSON> e<PERSON>r", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddHobbiesByHobbyIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["hobbies"]}}, "/feed": {"get": {"operationId": "HomeController_feed", "summary": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> anda feedi listelerken sadece dummy verileri döndürüyoruz. İleride parametre ile çalışacak.", "parameters": [{"name": "dateType", "required": true, "in": "query", "description": "Date range to filter the feed", "schema": {"$ref": "#/components/schemas/FeedDateEnum"}}, {"name": "categories", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "location<PERSON>oeff", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "longitude", "required": false, "in": "query", "schema": {"example": -73.9712, "type": "double"}}, {"name": "latitude", "required": false, "in": "query", "schema": {"example": 40.7766, "type": "double"}}, {"name": "endDate", "required": false, "in": "query", "description": "End date for the feed", "schema": {"example": "2021-12-31"}}, {"name": "startDate", "required": false, "in": "query", "description": "Start date for the feed", "schema": {"example": "2021-12-31"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeedReturn"}}}}}, "tags": ["home"]}}, "/map": {"get": {"operationId": "HomeController_map", "summary": "Mapi listeler", "description": "<PERSON><PERSON> anda mapi listelerken sadece dummy verileri döndürüyoruz. İleride parametre ile çalışacak.", "parameters": [{"name": "startDate", "required": true, "in": "query", "description": "Start date for the ivents on map", "schema": {"example": "2021-12-31", "type": "string"}}, {"name": "endDate", "required": true, "in": "query", "description": "End date for the ivents on map", "schema": {"example": "2021-12-31", "type": "string"}}, {"name": "latStart", "required": true, "in": "query", "schema": {"example": 40.7766, "type": "double"}}, {"name": "latEnd", "required": true, "in": "query", "schema": {"example": 40.7766, "type": "double"}}, {"name": "lngStart", "required": true, "in": "query", "schema": {"example": -73.9712, "type": "double"}}, {"name": "lngEnd", "required": true, "in": "query", "schema": {"example": -73.9712, "type": "double"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MapReturn"}}}}}, "tags": ["home"]}}, "/searchIvent": {"get": {"operationId": "HomeController_searchIvent", "summary": "<PERSON>y<PERSON><PERSON><PERSON> arama yapar", "parameters": [{"name": "q", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchIventReturn"}}}}}, "tags": ["home"]}}, "/searchAccount": {"get": {"operationId": "HomeController_searchAccount", "summary": "<PERSON>y<PERSON><PERSON><PERSON> arama yapar", "parameters": [{"name": "q", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchAccountReturn"}}}}}, "tags": ["home"]}}, "/iventCollabs/search": {"get": {"operationId": "IventCollabsController_searchCollabsForIventCreation", "summary": "Ivent oluştururken paydaş olabilecek hesapları listeler", "parameters": [{"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchCollabsForIventCreationReturn"}}}}}, "tags": ["iventCollabs"]}}, "/iventCollabs/{iventId}/search": {"get": {"operationId": "IventCollabsController_searchCollabs", "summary": "Ivent'te bulunan paydaş hesapları listeler", "parameters": [{"name": "iventId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchCollabsReturn"}}}}}, "tags": ["iventCollabs"]}}, "/iventCollabs/{iventId}/leave": {"post": {"operationId": "IventCollabsController_leaveCollabrationByIventId", "summary": "Ivent IDsi ile paydaş hesaplardan ayrılınır", "parameters": [{"name": "iventId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["iventCollabs"]}}, "/iventCollabs/{iventId}/remove": {"post": {"operationId": "IventCollabsController_removeCollabByIventId", "summary": "Ivent IDsi ile bir hesabı paydaş hesaplardan çıkartır", "parameters": [{"name": "iventId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveCollabByIventIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["iventCollabs"]}}, "/ivents/create": {"post": {"operationId": "IventsController_createIvent", "summary": "<PERSON><PERSON>", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIventDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIventReturn"}}}}}, "tags": ["ivents"]}}, "/ivents/{id}/delete": {"delete": {"operationId": "IventsController_deleteIventByIventId", "summary": "Ivent IDsi ile iventi siler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["ivents"]}}, "/ivents/latest": {"get": {"operationId": "IventsController_getLatestIvents", "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> katıldı<PERSON> son i<PERSON><PERSON><PERSON>r", "parameters": [{"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLatestIventsReturn"}}}}}, "tags": ["ivents"]}}, "/ivents/suggestedImages": {"get": {"operationId": "IventsController_getSuggestedImages", "summary": "Ivent o<PERSON>şturulurken resim önerilerini listeler", "parameters": [{"name": "criterias", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSuggestedImagesReturn"}}}}}, "tags": ["ivents"]}}, "/ivents/{id}": {"get": {"operationId": "IventsController_getIventPageByIventId", "summary": "Ivent IDsi ile iventin sayfa bilgilerini listeler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetIventPageByIventIdReturn"}}}}}, "tags": ["ivents"]}}, "/ivents/banner": {"post": {"operationId": "IventsController_getBannerByIventId", "summary": "Ivent IDsi ile iventin küçük kart bilgilerini listeler", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetBannerByIventIdDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetBannerByIventIdReturn"}}}}}, "tags": ["ivents"]}}, "/ivents/{id}/update/date": {"put": {"operationId": "IventsController_updateDateByIventId", "summary": "Ivent <PERSON>si ile iventin tarih bil<PERSON><PERSON> g<PERSON>", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDateByIventIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["ivents"]}}, "/ivents/{id}/update/details": {"put": {"operationId": "IventsController_updateDetailsByIventId", "summary": "Ivent IDsi ile iventin de<PERSON>ü<PERSON>ller", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDetailsByIventIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["ivents"]}}, "/ivents/{id}/update/location": {"put": {"operationId": "IventsController_updateLocationByIventId", "summary": "Ivent <PERSON>si ile iventin konumunu g<PERSON>", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLocationByIventIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["ivents"]}}, "/ivents/{id}/favorite": {"post": {"operationId": "IventsController_favoriteIventByIventId", "summary": "Ivent IDsi ile iventi favorilere ekler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["ivents"]}}, "/ivents/{id}/unfavorite": {"post": {"operationId": "IventsController_unfavoriteIventByIventId", "summary": "Ivent IDsi ile iventi favorilerden çıkarır", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["ivents"]}}, "/ivents/upcoming": {"post": {"operationId": "IventsController_getUpcomingIvent", "summary": "Kullanıcının katılacağı en yakın iventi listeler", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["ivents"]}}, "/locations/search": {"get": {"operationId": "LocationsController_searchLocations", "summary": "Haritadan gözüken bölgedeki konumları listeler (hem GoogleMapsAPI hem de kendi konumlarımız olabilir)", "parameters": [{"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLocationsReturn"}}}}}, "tags": ["locations"]}}, "/locations/latest": {"get": {"operationId": "LocationsController_getLatestLocations", "summary": "<PERSON> seç<PERSON>n konum<PERSON> listeler", "parameters": [{"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLatestLocationsReturn"}}}}}, "tags": ["locations"]}}, "/mapbox/suggest": {"get": {"operationId": "MapboxController_searchBoxSuggest", "summary": "Search for place suggestions", "description": "Returns a list of place suggestions based on the search query", "parameters": [{"name": "q", "required": true, "in": "query", "description": "Search query string", "schema": {"example": "coffee shop", "type": "string"}}, {"name": "sessionToken", "required": true, "in": "query", "description": "Session token for grouping requests", "schema": {"example": "session_123", "type": "string"}}, {"name": "language", "required": false, "in": "query", "description": "Language code (ISO 639-1)", "schema": {"example": "en", "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Maximum number of results", "schema": {"example": 10, "type": "integer"}}, {"name": "proximity", "required": false, "in": "query", "description": "Proximity bias as longitude,latitude", "schema": {"example": "28.9784,41.0082", "type": "string"}}, {"name": "bbox", "required": false, "in": "query", "description": "Bounding box as minX,minY,maxX,maxY", "schema": {"example": "28.9,40.9,29.1,41.1", "type": "string"}}, {"name": "country", "required": false, "in": "query", "description": "Country code filter (ISO 3166-1 alpha-2)", "schema": {"example": "TR", "type": "string"}}, {"name": "types", "required": false, "in": "query", "description": "Feature types to include", "schema": {"example": "poi", "type": "string"}}, {"name": "poiCategory", "required": false, "in": "query", "description": "POI category filter", "schema": {"example": "food_and_drink", "type": "string"}}, {"name": "poiCategoryExclusions", "required": false, "in": "query", "description": "POI categories to exclude", "schema": {"example": "gas_station", "type": "string"}}, {"name": "etaType", "required": false, "in": "query", "description": "ETA calculation type", "schema": {"example": "navigation", "type": "string"}}, {"name": "navigationProfile", "required": false, "in": "query", "description": "Navigation profile for ETA", "schema": {"example": "driving", "type": "string"}}, {"name": "origin", "required": false, "in": "query", "description": "Origin point for ETA as longitude,latitude", "schema": {"example": "28.9784,41.0082", "type": "string"}}], "responses": {"200": {"description": "List of place suggestions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchBoxSuggestReturn"}}}}}, "tags": ["mapbox"]}}, "/mapbox/retrieve/{id}": {"get": {"operationId": "MapboxController_searchBoxRetrieve", "summary": "Retrieve detailed information about a place", "description": "Returns detailed information about a specific place using its Mapbox ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "sessionToken", "required": true, "in": "query", "description": "Session token for grouping requests", "schema": {"example": "session_123", "type": "string"}}, {"name": "language", "required": false, "in": "query", "description": "Language code (ISO 639-1)", "schema": {"example": "en", "type": "string"}}, {"name": "etaType", "required": false, "in": "query", "description": "ETA calculation type", "schema": {"example": "navigation", "type": "string"}}, {"name": "navigationProfile", "required": false, "in": "query", "description": "Navigation profile for ETA", "schema": {"example": "driving", "type": "string"}}, {"name": "origin", "required": false, "in": "query", "description": "Origin point for ETA as longitude,latitude", "schema": {"example": "28.9784,41.0082", "type": "string"}}], "responses": {"200": {"description": "Detailed place information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchBoxRetrieveReturn"}}}}}, "tags": ["mapbox"]}}, "/mapbox/forward": {"get": {"operationId": "MapboxController_searchBoxForward", "summary": "Forward geocoding search", "description": "Search for places using forward geocoding", "parameters": [{"name": "q", "required": true, "in": "query", "description": "Search query string", "schema": {"example": "coffee shop", "type": "string"}}, {"name": "language", "required": false, "in": "query", "description": "Language code (ISO 639-1)", "schema": {"example": "en", "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Maximum number of results", "schema": {"example": 10, "type": "integer"}}, {"name": "proximity", "required": false, "in": "query", "description": "Proximity bias as longitude,latitude", "schema": {"example": "28.9784,41.0082", "type": "string"}}, {"name": "bbox", "required": false, "in": "query", "description": "Bounding box as minX,minY,maxX,maxY", "schema": {"example": "28.9,40.9,29.1,41.1", "type": "string"}}, {"name": "country", "required": false, "in": "query", "description": "Country code filter (ISO 3166-1 alpha-2)", "schema": {"example": "TR", "type": "string"}}, {"name": "types", "required": false, "in": "query", "description": "Feature types to include", "schema": {"example": "poi", "type": "string"}}, {"name": "poiCategory", "required": false, "in": "query", "description": "POI category filter", "schema": {"example": "food_and_drink", "type": "string"}}, {"name": "poiCategoryExclusions", "required": false, "in": "query", "description": "POI categories to exclude", "schema": {"example": "gas_station", "type": "string"}}, {"name": "autoComplete", "required": false, "in": "query", "description": "Auto-complete setting", "schema": {"example": "true", "type": "string"}}, {"name": "etaType", "required": false, "in": "query", "description": "ETA calculation type", "schema": {"example": "navigation", "type": "string"}}, {"name": "navigationProfile", "required": false, "in": "query", "description": "Navigation profile for ETA", "schema": {"example": "driving", "type": "string"}}, {"name": "origin", "required": false, "in": "query", "description": "Origin point for ETA as longitude,latitude", "schema": {"example": "28.9784,41.0082", "type": "string"}}], "responses": {"200": {"description": "Forward geocoding results", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchBoxForwardReturn"}}}}}, "tags": ["mapbox"]}}, "/mapbox/category/{canonicalCategoryId}": {"get": {"operationId": "MapboxController_searchBoxCategory", "summary": "Search places by category", "description": "Search for places within a specific category", "parameters": [{"name": "canonicalCategoryId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "language", "required": false, "in": "query", "description": "Language code (ISO 639-1)", "schema": {"example": "en", "type": "string"}}, {"name": "proximity", "required": false, "in": "query", "description": "Proximity bias as longitude,latitude", "schema": {"example": "28.9784,41.0082", "type": "string"}}, {"name": "bbox", "required": false, "in": "query", "description": "Bounding box as minX,minY,maxX,maxY", "schema": {"example": "28.9,40.9,29.1,41.1", "type": "string"}}, {"name": "country", "required": false, "in": "query", "description": "Country code filter (ISO 3166-1 alpha-2)", "schema": {"example": "TR", "type": "string"}}, {"name": "types", "required": false, "in": "query", "description": "Feature types to include", "schema": {"example": "poi", "type": "string"}}, {"name": "poiCategoryExclusions", "required": false, "in": "query", "description": "POI categories to exclude", "schema": {"example": "gas_station", "type": "string"}}, {"name": "sarType", "required": false, "in": "query", "description": "Search along route type", "schema": {"example": "isochrone", "type": "string"}}, {"name": "route", "required": false, "in": "query", "description": "Route geometry for search along route", "schema": {"type": "string"}}, {"name": "routeGeometry", "required": false, "in": "query", "description": "Route geometry format", "schema": {"example": "polyline", "type": "string"}}, {"name": "timeDeviation", "required": false, "in": "query", "description": "Time deviation in minutes", "schema": {"example": 15, "type": "integer"}}], "responses": {"200": {"description": "Category search results", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchBoxCategoryReturn"}}}}}, "tags": ["mapbox"]}}, "/mapbox/categories": {"get": {"operationId": "MapboxController_searchBoxCategoryList", "summary": "List available categories", "description": "Returns a list of all available POI categories", "parameters": [{"name": "language", "required": false, "in": "query", "description": "Language code (ISO 639-1)", "schema": {"example": "en", "type": "string"}}], "responses": {"200": {"description": "List of available categories", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchBoxCategoryListReturn"}}}}}, "tags": ["mapbox"]}}, "/mapbox/reverse": {"get": {"operationId": "MapboxController_searchBoxReverse", "summary": "Reverse geocoding search", "description": "Find places near a specific coordinate", "parameters": [{"name": "longitude", "required": true, "in": "query", "description": "Longitude coordinate", "schema": {"example": 28.9784, "type": "double"}}, {"name": "latitude", "required": true, "in": "query", "description": "Latitude coordinate", "schema": {"example": 41.0082, "type": "double"}}, {"name": "language", "required": false, "in": "query", "description": "Language code (ISO 639-1)", "schema": {"example": "en", "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Maximum number of results", "schema": {"example": 10, "type": "integer"}}, {"name": "country", "required": false, "in": "query", "description": "Country code filter (ISO 3166-1 alpha-2)", "schema": {"example": "TR", "type": "string"}}, {"name": "types", "required": false, "in": "query", "description": "Feature types to include", "schema": {"example": "poi", "type": "string"}}], "responses": {"200": {"description": "Reverse geocoding results", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchBoxReverseReturn"}}}}}, "tags": ["mapbox"]}}, "/memories/create": {"post": {"operationId": "MemoriesController_createMemory", "summary": "Memory oluşturur", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMemoryDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMemoryReturn"}}}}}, "tags": ["memories"]}}, "/memories/{id}/delete": {"delete": {"operationId": "MemoriesController_deleteMemoryByMemoryId", "summary": "Memory IDsi ile memory siler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["memories"]}}, "/memories/{id}": {"get": {"operationId": "MemoriesController_getMemoryByMemoryId", "summary": "ID ile memory getirir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "origin", "required": true, "in": "query", "description": "Origin of the memory", "schema": {"$ref": "#/components/schemas/MemoryOriginEnum"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetMemoryByMemoryIdReturn"}}}}}, "tags": ["memories"]}}, "/notifications": {"get": {"operationId": "NotificationsController_getNotifications", "summary": "Bildirimleri listeler", "parameters": [{"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetNotificationsReturn"}}}}}, "tags": ["notifications"]}}, "/notifications/{id}/reply": {"post": {"operationId": "NotificationsController_replyInvitationBySquadId", "summary": "Squad IDsi ile seçilen davete \"accept\" ya da \"reject\" yanıtı verilir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "reply", "required": true, "in": "query", "description": "Reply type for the notification", "schema": {"$ref": "#/components/schemas/NotificationReplyTypeEnum"}}], "responses": {"200": {"description": ""}}, "tags": ["notifications"]}}, "/pageBlacklists/{pageId}/blocklist": {"get": {"operationId": "PageBlacklistsController_searchPageBlocklistByPageId", "summary": "Sayfa IDsi ile engellenenleri listeler", "parameters": [{"name": "pageId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchPageBlocklistByPageIdReturn"}}}}}, "tags": ["pageBlacklists"]}}, "/pageBlacklists/{pageId}/block": {"post": {"operationId": "PageBlacklistsController_blockUserByPageId", "summary": "Sayfa IDsi ile bir hesab<PERSON> engeller", "parameters": [{"name": "pageId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockUserByPageIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["pageBlacklists"]}}, "/pageBlacklists/{pageId}/unblock": {"post": {"operationId": "PageBlacklistsController_unblockUserByPageId", "summary": "Sayfa IDsi ile bir hesabın engelini kaldırır", "parameters": [{"name": "pageId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnblockUserByPageIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["pageBlacklists"]}}, "/pageMemberships/administration/search": {"get": {"operationId": "PageMembershipsController_searchModeratorsForPageCreation", "summary": "Sayfa oluştururken uygun yardımcı adminler listelenir", "parameters": [{"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchModeratorsForPageCreationReturn"}}}}}, "tags": ["pageMemberships"]}}, "/pageMemberships/{pageId}/administration/search": {"get": {"operationId": "PageMembershipsController_searchModeratorsToAddByPageId", "summary": "Var olan sayfaya eklenebilecek uygun yardımcı adminler listelenir", "parameters": [{"name": "pageId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchModeratorsForPageCreationReturn"}}}}}, "tags": ["pageMemberships"]}}, "/pageMemberships/{pageId}/administration/leave": {"post": {"operationId": "PageMembershipsController_leavePageModerationByPageId", "summary": "Sayfa IDsi ile adminlikten ayrılınır", "parameters": [{"name": "pageId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["pageMemberships"]}}, "/pageMemberships/{pageId}/administration/remove": {"post": {"operationId": "PageMembershipsController_removePageModeratorByPageId", "summary": "Sayfa IDsi ile yardımcı admin ç<PERSON>ılır", "parameters": [{"name": "pageId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemovePageModeratorByPageIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["pageMemberships"]}}, "/pageMemberships/{pageId}/administration/transfer": {"post": {"operationId": "PageMembershipsController_transferPageAdministrationByPageId", "summary": "Sayfa IDsi ile adminlik devre<PERSON>lir", "parameters": [{"name": "pageId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferPageAdministrationByPageIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["pageMemberships"]}}, "/pageMemberships/{pageId}/members": {"get": {"operationId": "PageMembershipsController_searchPageMembersByPageId", "summary": "Sayfa IDsi ile üyeler listelenir", "parameters": [{"name": "pageId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchPageMembersByPageIdReturn"}}}}}, "tags": ["pageMemberships"]}}, "/pageMemberships/{pageId}/search": {"get": {"operationId": "PageMembershipsController_searchUsersToAddByPageId", "summary": "Sayfa IDsi ile eklenebilecek hesaplar listelenir", "parameters": [{"name": "pageId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchUsersToAddByPageIdReturn"}}}}}, "tags": ["pageMemberships"]}}, "/pageMemberships/{pageId}/administration": {"get": {"operationId": "PageMembershipsController_searchAdministrationByPageId", "summary": "Sayfa IDsi ile yetkilileri listeler", "parameters": [{"name": "pageId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchAdministrationByPageIdReturn"}}}}}, "tags": ["pageMemberships"]}}, "/pageMemberships/{pageId}/join": {"post": {"operationId": "PageMembershipsController_joinPageMembershipByPageId", "summary": "Sayfa IDsi ile üye olunur", "parameters": [{"name": "pageId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["pageMemberships"]}}, "/pageMemberships/{pageId}/leave": {"post": {"operationId": "PageMembershipsController_leavePageMembershipByPageId", "summary": "Sayfa IDsi ile üyelikten çıkılır", "parameters": [{"name": "pageId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["pageMemberships"]}}, "/pageMemberships/{pageId}/add": {"post": {"operationId": "PageMembershipsController_addPageMembersByPageId", "summary": "Sayfa IDsi ile üye eklenir", "parameters": [{"name": "pageId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddPageMembersByPageIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["pageMemberships"]}}, "/pageMemberships/{pageId}/remove": {"post": {"operationId": "PageMembershipsController_removePageMemberByPageId", "summary": "Sayfa IDsi ile üyelikten çıkartılır", "parameters": [{"name": "pageId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemovePageMemberByPageIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["pageMemberships"]}}, "/pages/create": {"post": {"operationId": "PagesController_createPage", "summary": "<PERSON><PERSON> o<PERSON>şturur", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePageDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePageReturn"}}}}}, "tags": ["pages"]}}, "/pages/{id}/delete": {"delete": {"operationId": "PagesController_deletePageByPageId", "summary": "Sayfa IDsi ile sayfayı siler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["pages"]}}, "/pages/{id}": {"get": {"operationId": "PagesController_getPageByPageId", "summary": "Sayfa IDsi ile sayfanın bilgilerini listeler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPageByPageIdReturn"}}}}}, "tags": ["pages"]}}, "/pages/{id}/ivents": {"get": {"operationId": "PagesController_getIventsCreatedByPageId", "summary": "Sayfa IDsi ile say<PERSON>ın iventlerini listeler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetIventsCreatedByPageIdReturn"}}}}}, "tags": ["pages"]}}, "/pages/{id}/vibes": {"get": {"operationId": "PagesController_getVibesByPageId", "summary": "Sayfa IDsi ile sayfanın vibelarını listeler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetVibeFoldersByPageIdReturn"}}}}}, "tags": ["pages"]}}, "/pages/{id}/details": {"get": {"operationId": "PagesController_getDetailsByPageId", "summary": "Sayfa IDsi ile say<PERSON>ın de<PERSON>ylarını listeler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPageDetailsByPageIdReturn"}}}}}, "tags": ["pages"]}}, "/pages/{id}/update/description": {"put": {"operationId": "PagesController_updateDescriptionByPageId", "summary": "Sayfa IDsi ile say<PERSON>ın açıklamasını günceller", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDescriptionByPageIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["pages"]}}, "/pages/{id}/update/links": {"put": {"operationId": "PagesController_updateLinksByPageId", "summary": "Sayfa <PERSON>si ile say<PERSON><PERSON>n link<PERSON>", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLinksByPageIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["pages"]}}, "/pages/{id}/update/location": {"put": {"operationId": "PagesController_updateLocationByPageId", "summary": "<PERSON><PERSON> ile say<PERSON>ın konum<PERSON>u g<PERSON>", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLocationByPageIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["pages"]}}, "/pages/{id}/block": {"post": {"operationId": "PagesController_blockPageByPageId", "summary": "Say<PERSON> ile sayfa engellenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["pages"]}}, "/pages/{id}/unblock": {"post": {"operationId": "PagesController_unblockPageByPageId", "summary": "Sayfa IDsi ile sayfa engeli kaldırılır", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["pages"]}}, "/pages/{id}/subscribe": {"post": {"operationId": "PagesController_subscribeByPageId", "summary": "Sayfa IDsi ile etkinlik bildirimlerini açar", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["pages"]}}, "/pages/{id}/unsubscribe": {"post": {"operationId": "PagesController_unsubscribeByPageId", "summary": "Sayfa IDsi ile etkinlik bildirimlerini kapatır", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["pages"]}}, "/pages/{id}/followers": {"get": {"operationId": "PagesController_searchFollowersByPageId", "summary": "Sayfa IDsi ile takipçileri listeler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchFollowersByPageIdReturn"}}}}}, "tags": ["pages"]}}, "/pages/{id}/follow": {"post": {"operationId": "PagesController_followByPageId", "summary": "Sayfa IDsi ile takip edilir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["pages"]}}, "/pages/{id}/unfollow": {"post": {"operationId": "PagesController_unfollowByPageId", "summary": "Sayfa IDsi ile takipten çıkılır", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["pages"]}}, "/pages/{id}/followers/remove": {"post": {"operationId": "PagesController_removeFollowerByPageId", "summary": "Sayfa IDsi ile takipçi çıkartılır", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveFollowerByPageIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["pages"]}}, "/squadMemberships/{iventId}/search": {"get": {"operationId": "SquadMembershipsController_searchInvitableUsersByIventId", "summary": "Ivente katılırken davet edilebilecek hesapları listeler", "parameters": [{"name": "iventId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "type", "required": true, "in": "query", "description": "Type of friends to list - either groups or users", "schema": {"$ref": "#/components/schemas/FriendListingTypeEnum"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchInvitableUsersByIventIdReturn"}}}}}, "tags": ["squadMemberships"]}}, "/squadMemberships/{iventId}": {"get": {"operationId": "SquadMembershipsController_searchParticipantsByIventId", "summary": "Ivent IDsi ile (eğer iventte yetkiliyse): ivente katılan bütün hesapları, (eğer ivente katılmayan bir user ise): ivente katılan ya da favorilemiş arkadaşları, (eğer ivente katılan bir user ise): ivente birlikte katıldığı arkadaşları listeler", "parameters": [{"name": "iventId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchParticipantsByIventIdReturn"}}}}}, "tags": ["squadMemberships"]}}, "/squadMemberships/{iventId}/join": {"post": {"operationId": "SquadMembershipsController_joinIventAndCreateSquadByIventId", "summary": "Ivent IDsi ile seçilen hesaplarla birlikte ivente katılır (seçili hesaplara davet gönderilir)", "parameters": [{"name": "iventId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JoinIventAndCreateSquadByIventIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["squadMemberships"]}}, "/squadMemberships/{iventId}/leave": {"post": {"operationId": "SquadMembershipsController_leaveSquadByIventId", "summary": "Ivent IDsi ile etkinlikten ayrılınır", "parameters": [{"name": "iventId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["squadMemberships"]}}, "/squadMemberships/{iventId}/invite": {"post": {"operationId": "SquadMembershipsController_inviteFriendsByIventId", "summary": "Ivent <PERSON>si ile seçilen hesaplar davet edilir", "parameters": [{"name": "iventId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InviteFriendsByIventIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["squadMemberships"]}}, "/universities/search": {"post": {"operationId": "UniversitiesController_searchUniversities", "summary": "Ivent oluşturulurken üniversiteleri listeler", "parameters": [{"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchUniversitiesReturn"}}}}}, "tags": ["universities"]}}, "/userRelationships/blocklist": {"get": {"operationId": "UserRelationshipsController_getUserBlocklist", "summary": "Engellenenleri <PERSON>eler", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUserBlocklistReturn"}}}}}, "tags": ["userRelationships"]}}, "/userRelationships/{userId}/block": {"post": {"operationId": "UserRelationshipsController_blockUserByUserId", "summary": "<PERSON><PERSON><PERSON> ile hesap engellenir", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["userRelationships"]}}, "/userRelationships/{userId}/unblock": {"post": {"operationId": "UserRelationshipsController_unblockUserByUserId", "summary": "<PERSON><PERSON><PERSON> ile hesap engeli kaldırılır", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["userRelationships"]}}, "/userRelationships/{userId}/friends": {"get": {"operationId": "UserRelationshipsController_searchFriendsByUserId", "summary": "Hesap <PERSON>si ile arkadaşlar listelenir", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "type", "required": true, "in": "query", "description": "Type of friends to list - either groups or users", "schema": {"$ref": "#/components/schemas/FriendListingTypeEnum"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchFriendsByUserIdReturn"}}}}}, "tags": ["userRelationships"]}}, "/userRelationships/{userId}/invite": {"post": {"operationId": "UserRelationshipsController_inviteFriendByUserId", "summary": "Hesap <PERSON> ile arkadaş daveti gönderilir", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["userRelationships"]}}, "/userRelationships/{userId}/remove": {"post": {"operationId": "UserRelationshipsController_removeFriendByUserId", "summary": "Hesap <PERSON>si ile arkadaşlıktan çıkılır", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["userRelationships"]}}, "/users/register": {"post": {"operationId": "UsersController_register", "summary": "<PERSON><PERSON><PERSON> giri<PERSON>", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterReturn"}}}}}, "tags": ["users"]}}, "/users/{id}/delete": {"delete": {"operationId": "UsersController_deleteByUserId", "summary": "<PERSON><PERSON><PERSON> ile hesap si<PERSON>r", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/{id}": {"get": {"operationId": "UsersController_getByUserId", "summary": "<PERSON><PERSON><PERSON> ile hesap bilgileri listelenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUserByUserIdReturn"}}}}}, "tags": ["users"]}}, "/users/{id}/contacts": {"post": {"operationId": "UsersController_getContactsByUserId", "summary": "Hesap <PERSON>si ile rehber bağlantıları listelenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetContactsByUserIdDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetContactsByUserIdReturn"}}}}}, "tags": ["users"]}}, "/users/{id}/favorites": {"get": {"operationId": "UsersController_getFavoritesByUserId", "summary": "<PERSON>sap <PERSON> ile hesabın favoriledikleri listelenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetFavoritesByUserIdReturn"}}}}}, "tags": ["users"]}}, "/users/{id}/followings": {"get": {"operationId": "UsersController_getFollowingsByUserId", "summary": "<PERSON><PERSON><PERSON> ile hesabın takip ettikleri listelenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetFollowingsByUserIdReturn"}}}}}, "tags": ["users"]}}, "/users/{id}/ivents": {"get": {"operationId": "UsersController_getIventsByUserId", "summary": "Hesap <PERSON>si ile hesabın katıldığı ya da oluşturduğu iventler listelenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "type", "required": true, "in": "query", "description": "Type of ivents to list - either joined or created by the user", "schema": {"$ref": "#/components/schemas/IventListingTypeEnum"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetIventsByUserIdReturn"}}}}}, "tags": ["users"]}}, "/users/{id}/level": {"get": {"operationId": "UsersController_getLevelByUserId", "summary": "<PERSON>sap <PERSON><PERSON> ile hesabın aşaması gösterilir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLevelByUserIdReturn"}}}}}, "tags": ["users"]}}, "/users/{id}/pages": {"get": {"operationId": "UsersController_getPagesByUserId", "summary": "<PERSON><PERSON><PERSON> <PERSON><PERSON> ile hesaba ait sayfa<PERSON> gösterilir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPagesByUserIdReturn"}}}}}, "tags": ["users"]}}, "/users/{id}/memories": {"get": {"operationId": "UsersController_getMemoryFoldersByUserId", "summary": "<PERSON><PERSON><PERSON> ile hesabın memoriesleri listelenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetMemoryFoldersByUserIdReturn"}}}}}, "tags": ["users"]}}, "/users/{id}/vibes": {"get": {"operationId": "UsersController_getVibeFoldersByUserId", "summary": "Hesap <PERSON>si ile hesabın vibeları listelenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetVibeFoldersByUserIdReturn"}}}}}, "tags": ["users"]}}, "/users/{id}/update": {"put": {"operationId": "UsersController_updateByUserId", "summary": "<PERSON>sap <PERSON><PERSON> ile hesabın detayları güncellenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateByUserIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/{id}/update/email": {"put": {"operationId": "UsersController_updateEmailByUserId", "summary": "Hesap <PERSON> ile emaili güncellenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEmailByUserIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/{id}/update/grad": {"put": {"operationId": "UsersController_updateGradByUserId", "summary": "Hesap <PERSON>si ile mezuniyet durumu güncellenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGradByUserIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/{id}/update/notifications": {"put": {"operationId": "UsersController_updateNotificationsByUserId", "summary": "Hesap <PERSON><PERSON> ile bildirim ayarları güncellenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNotificationsByUserIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/{id}/update/phone-number": {"put": {"operationId": "UsersController_updatePhoneNumberByUserId", "summary": "Hesap <PERSON>si ile telefon numarası güncellenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePhoneNumberByUserIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/{id}/update/privacy": {"put": {"operationId": "UsersController_updatePrivacyByUserId", "summary": "Hesap <PERSON>si ile gizlilik ayarları güncellenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/{id}/followers": {"get": {"operationId": "UsersController_getFollowersByUserId", "summary": "Hesap <PERSON>si ile takipçileri listeler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetFollowersByUserIdReturn"}}}}}, "tags": ["users"]}}, "/users/{id}/followers/friends": {"get": {"operationId": "UsersController_getFollowerFriendsByUserId", "summary": "Hesap IDsi ile takipçilerden arkadaş olunanları listeler", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "q", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetFollowerFriendsByUserIdReturn"}}}}}, "tags": ["users"]}}, "/users/{id}/follow": {"post": {"operationId": "UsersController_followByUserId", "summary": "<PERSON><PERSON><PERSON> ile hesap takip edilir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/{id}/unfollow": {"post": {"operationId": "UsersController_unfollowByUserId", "summary": "Hesap <PERSON>si ile hesabın takibinden çıkılır", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/{id}/followers/remove": {"post": {"operationId": "UsersController_removeFollowerByUserId", "summary": "Hesap <PERSON> ile hesabın takipçilerinden kaldırılır", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveFollowerByUserIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/{id}/subscribe": {"post": {"operationId": "UsersController_subscribeByUserId", "summary": "<PERSON><PERSON><PERSON> ile hesap bildirimleri açılır", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/{id}/unsubscribe": {"post": {"operationId": "UsersController_unsubscribeByUserId", "summary": "<PERSON><PERSON><PERSON> ile hesap bildiri<PERSON>i ka<PERSON>tılır", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/emailVerification": {"post": {"operationId": "UsersController_validateEmail", "summary": "Email doğrulaması için gönderilen kod doğrulanır", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/send-verification-email": {"post": {"operationId": "UsersController_sendVerificationEmail", "summary": "Email doğrulaması için kod gönderilir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/{id}/send-creator-request": {"post": {"operationId": "UsersController_sendCreatorRequestForm", "summary": "Creator ba<PERSON><PERSON><PERSON> formu g<PERSON>", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/{id}/banner": {"get": {"operationId": "UsersController_getUserBannerByUserId", "summary": "<PERSON><PERSON><PERSON> ile hesabın avatar URL'si ve ismi listelenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUserBannerByUserIdReturn"}}}}}, "tags": ["users"]}}, "/vibes/create": {"post": {"operationId": "VibesController_createVibe", "summary": "Vibe oluşturulur", "parameters": [], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/CreateVibeDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateVibeReturn"}}}}}, "tags": ["vibes"]}}, "/vibes/{id}/delete": {"delete": {"operationId": "VibesController_deleteByVibeId", "summary": "Vibe IDsi ile vibe silinir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["vibes"]}}, "/vibes": {"get": {"operationId": "VibesController_getVibes", "summary": "Vibe sekmesi listelenir", "parameters": [{"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetVibesReturn"}}}}}, "tags": ["vibes"]}}, "/vibes/{id}": {"get": {"operationId": "VibesController_getByVibeId", "summary": "Vibe IDsi ile vibe getirilir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetVibeByVibeIdReturn"}}}}}, "tags": ["vibes"]}}, "/vibes/{id}/comments": {"get": {"operationId": "VibesController_getCommentsByVibeId", "summary": "Vibe IDsi ile vibe yorumları listelenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "integer"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCommentsByVibeIdReturn"}}}}}, "tags": ["vibes"]}}, "/vibes/{id}/update": {"put": {"operationId": "VibesController_updateByVibeId", "summary": "Vibe IDsi ile vibe bilgileri güncellenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateByVibeIdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["vibes"]}}, "/vibes/{id}/likes": {"get": {"operationId": "VibesController_getLikesByVibeId", "summary": "Vibe IDsi ile vibe beğenileri listelenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLikesByVibeIdReturn"}}}}}, "tags": ["vibes"]}}, "/vibes/{id}/likes/like": {"post": {"operationId": "VibesController_likeByVibeId", "summary": "Vibe IDsi ile vibe beğenilir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["vibes"]}}, "/vibes/{id}/likes/unlike": {"post": {"operationId": "VibesController_unlikeByVibeId", "summary": "Vibe IDsi ile vibe beğenisi kaldırılır", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["vibes"]}}, "/vibes/{id}/hide": {"post": {"operationId": "VibesController_hideByVibeId", "summary": "Vibe IDsi ile vibe gizlenir", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["vibes"]}}, "/vibes/{id}/show": {"post": {"operationId": "VibesController_showByVibeId", "summary": "Vibe IDsi ile vibe gösterime girer", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["vibes"]}}, "/health": {"get": {"operationId": "AppController_getHealth", "summary": "Health check", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["health"]}}}, "info": {"title": "Ivent API Documentation", "description": "You can download the API documentation in JSON format from http://localhost:3000/api-json", "version": "0.0.1", "contact": {}}, "tags": [], "servers": [], "components": {"securitySchemes": {"JWT-auth": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"ValidateDto": {"type": "object", "properties": {"validationCode": {"type": "string", "example": "123456", "description": "Six-digit verification code sent to the phone number", "minLength": 6, "maxLength": 6}, "phoneNumber": {"type": "string", "example": "+90(500)4003020", "description": "Phone number in international format with country code"}}, "required": ["validationCode", "phoneNumber"]}, "UserRoleEnum": {"type": "string", "description": "User level (role) information", "enum": ["level_0", "level_1", "level_2", "level_3", "level_4", "level_5", "level_6", "creator"]}, "AuthEnum": {"type": "string", "description": "Authentication result type", "enum": ["login", "register"]}, "ValidateReturn": {"type": "object", "properties": {"token": {"type": "string", "nullable": true, "description": "JWT authentication token for authenticated users", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "userId": {"type": "string", "nullable": true, "description": "Unique identifier of the authenticated user", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "role": {"example": "level_0", "nullable": true, "$ref": "#/components/schemas/UserRoleEnum"}, "username": {"type": "string", "nullable": true, "description": "Username of the authenticated user", "example": "john_doe"}, "fullname": {"type": "string", "nullable": true, "description": "Full name of the authenticated user", "example": "<PERSON>"}, "avatarUrl": {"type": "string", "nullable": true, "description": "URL to the user's avatar image", "example": "https://example.com/avatar.jpg", "format": "url"}, "type": {"example": "login", "$ref": "#/components/schemas/AuthEnum"}}, "required": ["type"]}, "SendVerificationCodeDto": {"type": "object", "properties": {"phoneNumber": {"type": "string", "example": "+90(500)4003020", "description": "Phone number in international format with country code"}}, "required": ["phoneNumber"]}, "CreateCommentDto": {"type": "object", "properties": {"comment": {"type": "string", "description": "The comment text content", "example": "This is a great post!", "minLength": 1, "maxLength": 1000}, "vibeId": {"type": "string", "description": "UUID of the vibe being commented on", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "iventName": {"type": "string", "description": "An ivent name can only contain letters, numbers, underscores, and hyphens.", "example": "My Awesome Ivent", "minLength": 3, "maxLength": 200}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "Optional thumbnail URL for the comment", "example": "https://example.com/thumbnail.jpg", "format": "url"}, "creatorId": {"type": "string", "description": "UUID of the user creating the comment", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["comment", "vibeId", "iventName", "creatorId"]}, "CreateCommentReturn": {"type": "object", "properties": {"commentId": {"type": "string", "description": "UUID of the newly created comment", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["commentId"]}, "AddModeratorByGroupIdDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the user to be promoted to moderator", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["userId"]}, "RemoveModeratorByGroupIdDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the moderator to be demoted to regular member", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["userId"]}, "TransferAdministrationByGroupIdDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the user to transfer group administration to", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["userId"]}, "UserListItem": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the user", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "username": {"type": "string", "description": "Username of the user", "example": "john_doe"}, "avatarUrl": {"type": "string", "nullable": true, "description": "URL to the user's avatar image", "example": "https://example.com/avatar.jpg", "format": "url"}, "university": {"type": "string", "nullable": true, "description": "Name of the user's university", "example": "Boğaziçi University"}}, "required": ["userId", "username"]}, "SearchUsersForGroupCreationReturn": {"type": "object", "properties": {"users": {"description": "List of users available for group creation", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItem"}}, "userCount": {"type": "integer", "description": "Total number of users available for group creation", "example": 15, "minimum": 0}}, "required": ["users", "userCount"]}, "UserRelationshipStatusEnum": {"type": "string", "description": "Status of the relationship between the current user and the user in the list", "enum": ["pending", "accepted", "blocked"]}, "GroupMembershipStatusEnum": {"type": "string", "description": "Status of the group membership of the user", "enum": ["admin", "moderator", "pending", "accepted"]}, "UserListItemWithGroupRole": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the user", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "username": {"type": "string", "description": "Username of the user", "example": "john_doe"}, "avatarUrl": {"type": "string", "nullable": true, "description": "URL to the user's avatar image", "example": "https://example.com/avatar.jpg", "format": "url"}, "university": {"type": "string", "nullable": true, "description": "Name of the user's university", "example": "Boğaziçi University"}, "relationshipStatus": {"example": "accepted", "$ref": "#/components/schemas/UserRelationshipStatusEnum"}, "groupMembershipStatus": {"example": "admin", "$ref": "#/components/schemas/GroupMembershipStatusEnum"}}, "required": ["userId", "username", "relationshipStatus", "groupMembershipStatus"]}, "SearchGroupMembersByGroupIdReturn": {"type": "object", "properties": {"members": {"description": "List of group members with their roles and friendship status", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItemWithGroupRole"}}, "memberCount": {"type": "integer", "description": "Total number of group members", "example": 25, "minimum": 0}}, "required": ["members", "memberCount"]}, "InviteMembersByGroupIdDto": {"type": "object", "properties": {"userIds": {"description": "Array of user UUIDs to invite to the group", "example": ["123e4567-e89b-12d3-a456-************", "987fcdeb-51a2-43d1-9f12-123456789abc"], "type": "array", "items": {"type": "string", "format": "uuid"}}}, "required": ["userIds"]}, "RemoveMemberByGroupIdDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the user to be removed from the group", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["userId"]}, "CreateGroupDto": {"type": "object", "properties": {"groupName": {"type": "string", "description": "Group name can only contain letters, numbers, underscores, hyphens, and periods", "example": "My Awesome Group", "minLength": 3, "maxLength": 50}, "thumbnailBuffer": {"type": "string", "nullable": true, "description": "Base64 encoded thumbnail image buffer for the group", "example": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."}, "userIds": {"description": "Array of user UUIDs to add as group members", "example": ["123e4567-e89b-12d3-a456-************"], "type": "array", "items": {"type": "string", "format": "uuid"}}}, "required": ["groupName", "userIds"]}, "CreateGroupReturn": {"type": "object", "properties": {"groupId": {"type": "string", "description": "UUID of the newly created group", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["groupId"]}, "GetGroupByGroupIdReturn": {"type": "object", "properties": {"groupId": {"type": "string", "description": "Unique identifier of the group", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "groupName": {"type": "string", "description": "Name of the group", "example": "Photography Enthusiasts"}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the group thumbnail image", "example": "https://example.com/group-thumbnail.jpg", "format": "url"}, "members": {"description": "List of group members with their roles and friendship status", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItemWithGroupRole"}}, "memberCount": {"type": "integer", "description": "Total number of group members", "example": 5, "minimum": 0}}, "required": ["groupId", "groupName", "members", "memberCount"]}, "HobbiesSearchOriginEnum": {"type": "string", "enum": ["default", "profile"]}, "HobbyItem": {"type": "object", "properties": {"hobbyId": {"type": "string", "description": "UUID of the hobby", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "hobbyName": {"type": "string", "description": "Name of the hobby (sub category)", "example": "Photography"}, "parentHobbyId": {"type": "string", "description": "UUID of the parent hobby", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "parentHobbyName": {"type": "string", "description": "Name of the parent hobby (main category)", "example": "Art"}}, "required": ["hobbyId", "hobbyName", "parentHobbyId", "parentHobbyName"]}, "SearchHobbiesReturn": {"type": "object", "properties": {"hobbies": {"description": "List of hobby categories", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/HobbyItem"}}, "hobbyCount": {"type": "integer", "description": "Total number of hobby categories", "example": 2, "minimum": 0}}, "required": ["hobbies", "hobbyCount"]}, "AddHobbiesByHobbyIdDto": {"type": "object", "properties": {"hobbyIds": {"description": "Array of hobby UUIDs to add to the user's profile", "example": ["123e4567-e89b-12d3-a456-************"], "type": "array", "items": {"type": "string", "format": "uuid"}}}, "required": ["hobbyIds"]}, "FeedDateEnum": {"type": "string", "enum": ["range", "to_you", "today", "tomorrow", "this_week", "next_week", "this_month", "this_summer", "holiday"]}, "IventCreatorTypeEnum": {"type": "string", "description": "Type of the ivent creator", "enum": ["user", "page", "distributor"]}, "IventCardItem": {"type": "object", "properties": {"iventId": {"type": "string", "description": "UUID of the ivent", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "iventName": {"type": "string", "description": "Name of the ivent", "example": "My Awesome Ivent"}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the ivent thumbnail image", "example": "https://example.com/thumbnail.jpg", "format": "url"}, "locationName": {"type": "string", "description": "Name of the ivent location", "example": "Central Park"}, "creatorId": {"type": "string", "description": "UUID of the ivent creator", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "creatorType": {"example": "user", "$ref": "#/components/schemas/IventCreatorTypeEnum"}, "creatorUsername": {"type": "string", "description": "<PERSON><PERSON><PERSON> of the ivent creator", "example": "john_doe"}, "creatorImageUrl": {"type": "string", "nullable": true, "description": "URL to the ivent creator image", "example": "https://example.com/creator-image.jpg", "format": "url"}, "isFavorited": {"type": "boolean", "description": "Whether the ivent is favorited by the current user", "example": true}}, "required": ["iventId", "iventName", "locationName", "creatorId", "creatorType", "creatorUsername", "isFavorited"]}, "FeedReturn": {"type": "object", "properties": {"ivents": {"description": "List of ivents", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/IventCardItem"}}, "iventCount": {"type": "integer", "description": "Total number of ivents", "example": 0, "minimum": 0}}, "required": ["ivents", "iventCount"]}, "MarkerItem": {"type": "object", "properties": {"iventId": {"type": "string", "description": "UUID of the ivent", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "latitude": {"type": "double", "description": "Latitude coordinate of the ivent", "example": 40.7766, "minimum": -90, "maximum": 90}, "longitude": {"type": "double", "description": "Longitude coordinate of the ivent", "example": -73.9712, "minimum": -180, "maximum": 180}}, "required": ["iventId", "latitude", "longitude"]}, "MapReturn": {"type": "object", "properties": {"ivents": {"description": "List of ivent markers", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/MarkerItem"}}, "iventCount": {"type": "integer", "description": "Total number of ivent markers", "example": 0, "minimum": 0}}, "required": ["ivents", "iventCount"]}, "SearchIventReturn": {"type": "object", "properties": {"ivents": {"description": "List of ivents", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/IventCardItem"}}, "iventCount": {"type": "integer", "description": "Total number of ivents", "example": 0, "minimum": 0}}, "required": ["ivents", "iventCount"]}, "AccountTypeEnum": {"type": "string", "description": "Type of the creator", "enum": ["page", "user"]}, "BasicAccountListItem": {"type": "object", "properties": {"accountId": {"type": "string", "description": "UUID of the account (user or page)", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "accountName": {"type": "string", "description": "Name of the account (user or page)", "example": "John <PERSON> or Photography Club Istanbul"}, "accountType": {"example": "user", "$ref": "#/components/schemas/AccountTypeEnum"}, "accountImageUrl": {"type": "string", "nullable": true, "description": "URL to the account image", "example": "https://example.com/account-image.jpg", "format": "url"}}, "required": ["accountId", "accountName", "accountType"]}, "SearchAccountReturn": {"type": "object", "properties": {"accounts": {"description": "List of accounts", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/BasicAccountListItem"}}, "accountCount": {"type": "integer", "description": "Total number of accounts", "example": 0, "minimum": 0}}, "required": ["accounts", "accountCount"]}, "SearchCollabsForIventCreationReturn": {"type": "object", "properties": {"accounts": {"description": "List of accounts available for collaboration during ivent creation", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/BasicAccountListItem"}}, "accountCount": {"type": "integer", "description": "Total number of accounts available for collaboration", "example": 12, "minimum": 0}}, "required": ["accounts", "accountCount"]}, "PageMembershipStatusEnum": {"type": "string", "description": "Status of the user in the page", "enum": ["admin", "moderator", "creator", "pending", "accepted", "blocked"]}, "CollabratorListItem": {"type": "object", "properties": {"collabId": {"type": "string", "description": "UUID of the collaborator", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "collabName": {"type": "string", "description": "Name of the collaborator", "example": "Photography Club Istanbul or john_doe"}, "collabType": {"example": "user", "$ref": "#/components/schemas/AccountTypeEnum"}, "collabImageUrl": {"type": "string", "nullable": true, "description": "URL to the collaborator image", "example": "https://example.com/collaborator-image.jpg", "format": "url"}, "pageMembershipStatus": {"example": "admin", "$ref": "#/components/schemas/PageMembershipStatusEnum"}, "relationshipStatus": {"example": "accepted", "$ref": "#/components/schemas/UserRelationshipStatusEnum"}}, "required": ["collabId", "collabName", "collabType", "pageMembershipStatus", "relationshipStatus"]}, "SearchCollabsReturn": {"type": "object", "properties": {"collabs": {"description": "List of collaborators with their membership and friendship status", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/CollabratorListItem"}}, "collabCount": {"type": "integer", "description": "Total number of collaborators found", "example": 8, "minimum": 0}}, "required": ["collabs", "collabCount"]}, "RemoveCollabByIventIdDto": {"type": "object", "properties": {"collabId": {"type": "string", "description": "UUID of the collaborator to be removed from the ivent", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "collabType": {"example": "user", "$ref": "#/components/schemas/AccountTypeEnum"}}, "required": ["collabId", "collabType"]}, "IventPrivacyEnum": {"type": "string", "description": "Privacy setting for the ivent", "enum": ["private", "friends", "edu", "selected_edu", "public"]}, "CollabDto": {"type": "object", "properties": {"id": {"type": "string", "description": "UUID of the collaborator", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "type": {"example": "user", "$ref": "#/components/schemas/AccountTypeEnum"}}, "required": ["id", "type"]}, "CreateIventDto": {"type": "object", "properties": {"creatorType": {"example": "user", "$ref": "#/components/schemas/AccountTypeEnum"}, "iventName": {"type": "string", "description": "Ivent name can only contain letters, numbers, underscores, and hyphens", "example": "My Awesome Ivent", "minLength": 3, "maxLength": 200}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the ivent thumbnail image", "example": "https://example.com/thumbnail.jpg", "format": "url"}, "thumbnailBuffer": {"type": "string", "nullable": true, "description": "Base64 encoded thumbnail image buffer", "example": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."}, "dates": {"description": "Array of date strings in ISO 8601 date-time format", "example": ["2024-08-31T22:00:00Z", "2024-09-01T14:00:00Z"], "type": "array", "items": {"type": "string"}}, "mapboxId": {"type": "string", "description": "Mapbox place ID for location", "example": "address.**********"}, "latitude": {"type": "double", "description": "Latitude coordinate of the ivent location", "example": 41.0082, "minimum": -90, "maximum": 90}, "longitude": {"type": "double", "description": "Longitude coordinate of the ivent location", "example": 28.9784, "minimum": -180, "maximum": 180}, "description": {"type": "string", "nullable": true, "description": "Detailed description of the ivent", "example": "Join us for an amazing outdoor photography workshop!"}, "categoryTagId": {"type": "string", "description": "UUID of the category tag for this ivent", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "tagIds": {"description": "Array of hobby tag UUIDs associated with this ivent", "example": ["123e4567-e89b-12d3-a456-************"], "type": "array", "items": {"type": "string", "format": "uuid"}}, "privacy": {"example": "public", "$ref": "#/components/schemas/IventPrivacyEnum"}, "allowedUniversityCodes": {"description": "Array of university codes that are allowed to join this ivent", "example": ["BOGAZICI", "ITU"], "type": "array", "items": {"type": "string"}}, "collabs": {"description": "Array of collaborators for this ivent", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/CollabDto"}}, "googleFormsUrl": {"type": "string", "nullable": true, "description": "URL to Google Forms for registration", "example": "https://forms.google.com/...", "format": "url"}, "instagramUsername": {"type": "string", "nullable": true, "description": "Instagram username for the ivent", "example": "my_ivent_account"}, "whatsappUrl": {"type": "string", "nullable": true, "description": "WhatsApp group URL", "example": "https://chat.whatsapp.com/...", "format": "url"}, "isWhatsappUrlPrivate": {"type": "boolean", "nullable": true, "description": "Whether the WhatsApp URL should be kept private", "example": false}, "whatsappNumber": {"type": "string", "nullable": true, "description": "WhatsApp contact number", "example": "+90(555)1234567"}, "callNumber": {"type": "string", "nullable": true, "description": "Phone number for calls", "example": "+90(555)1234567"}, "websiteUrl": {"type": "string", "nullable": true, "description": "Website URL for the ivent", "example": "https://myivent.com", "format": "url"}}, "required": ["creatorType", "iventName", "dates", "mapboxId", "latitude", "longitude", "categoryTagId", "tagIds", "privacy", "allowedUniversityCodes", "collabs"]}, "CreateIventReturn": {"type": "object", "properties": {"iventId": {"type": "string", "description": "UUID of the newly created ivent", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["iventId"]}, "IventViewTypeEnum": {"type": "string", "description": "View type of the ivent for the current user", "enum": ["joined", "created", "default"]}, "IventListItem": {"type": "object", "properties": {"iventId": {"type": "string", "description": "UUID of the ivent", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "iventName": {"type": "string", "description": "Name of the ivent", "example": "My Awesome Ivent"}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the ivent thumbnail image", "example": "https://example.com/thumbnail.jpg", "format": "url"}, "locationName": {"type": "string", "description": "Name of the ivent location", "example": "Central Park"}, "dates": {"description": "List of dates for the ivent in ISO 8601 date-time format", "example": ["2024-08-31T22:00:00Z", "2024-09-01T14:00:00Z"], "type": "array", "items": {"type": "string"}}, "creatorId": {"type": "string", "description": "UUID of the ivent creator", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "creatorType": {"example": "user", "$ref": "#/components/schemas/IventCreatorTypeEnum"}, "creatorUsername": {"type": "string", "description": "<PERSON><PERSON><PERSON> of the ivent creator", "example": "john_doe"}, "creatorImageUrl": {"type": "string", "nullable": true, "description": "URL to the ivent creator image", "example": "https://example.com/creator-image.jpg", "format": "url"}, "memberCount": {"type": "integer", "description": "Number of members in the ivent", "example": 10, "minimum": 0}, "memberFirstnames": {"description": "List of member's first names in the ivent", "example": ["<PERSON>", "<PERSON>"], "type": "array", "items": {"type": "string"}}, "memberAvatarUrls": {"type": "array", "items": {"type": "string", "nullable": true}, "format": "url", "description": "List of member avatar <PERSON><PERSON> in the ivent", "example": ["https://example.com/avatar1.jpg", "https://example.com/avatar2.jpg"]}, "viewType": {"example": "default", "$ref": "#/components/schemas/IventViewTypeEnum"}}, "required": ["iventId", "iventName", "locationName", "dates", "creatorId", "creatorType", "creatorUsername", "memberCount", "memberFirstnames", "memberAvatarUrls", "viewType"]}, "GetLatestIventsReturn": {"type": "object", "properties": {"ivents": {"description": "List of latest ivents", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/IventListItem"}}, "iventCount": {"type": "integer", "description": "Total number of latest ivents", "example": 0, "minimum": 0}}, "required": ["ivents", "iventCount"]}, "GetSuggestedImagesReturn": {"type": "object", "properties": {"imageUrls": {"description": "List of suggested image URLs", "example": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"], "type": "array", "items": {"type": "string", "format": "url"}}, "imageCount": {"type": "integer", "description": "Total number of suggested images", "example": 2, "minimum": 0}}, "required": ["imageUrls", "imageCount"]}, "GetIventPageByIventIdReturn": {"type": "object", "properties": {"iventId": {"type": "string", "description": "UUID of the ivent", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "iventName": {"type": "string", "description": "Name of the ivent", "example": "My Awesome Ivent"}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the ivent thumbnail image", "example": "https://example.com/thumbnail.jpg", "format": "url"}, "locationId": {"type": "string", "description": "UUID of the location", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "mapboxId": {"type": "string", "description": "Mapbox place ID for the location", "example": "address.**********"}, "locationName": {"type": "string", "description": "Name of the location", "example": "Central Park"}, "dates": {"description": "List of dates for the ivent, in ISO 8601 date-time format", "example": ["2024-08-31T22:00:00Z", "2024-09-01T14:00:00Z"], "type": "array", "items": {"type": "string"}}, "description": {"type": "string", "nullable": true, "description": "Detailed description of the ivent", "example": "Join us for an amazing outdoor photography workshop!"}, "categoryTag": {"type": "string", "description": "Name of the category tag", "example": "Photography"}, "tagNames": {"description": "List of hobby tags associated with the ivent", "example": ["Photography", "Outdoor"], "type": "array", "items": {"type": "string"}}, "creatorId": {"type": "string", "description": "UUID of the ivent creator", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "creatorType": {"example": "user", "$ref": "#/components/schemas/IventCreatorTypeEnum"}, "creatorUsername": {"type": "string", "description": "<PERSON><PERSON><PERSON> of the ivent creator", "example": "john_doe"}, "creatorImageUrl": {"type": "string", "nullable": true, "description": "URL to the ivent creator image", "example": "https://example.com/creator-image.jpg", "format": "url"}, "collabNames": {"description": "List of collaborator names, either page names or usernames", "example": ["Photography Club Istanbul", "jane_doe"], "type": "array", "items": {"type": "string"}}, "collabCount": {"type": "integer", "description": "Number of collaborators", "example": 2, "minimum": 0}, "memberFirstnames": {"nullable": true, "description": "List of member first names", "example": ["<PERSON>", "<PERSON>"], "type": "array", "items": {"type": "string"}}, "memberAvatarUrls": {"type": "array", "items": {"type": "string", "nullable": true}, "format": "url", "description": "List of member avatar <PERSON>s", "example": ["https://example.com/avatar1.jpg", "https://example.com/avatar2.jpg"]}, "memberCount": {"type": "integer", "description": "Number of members", "example": 2, "minimum": 0}, "isFavorited": {"type": "boolean", "nullable": true, "description": "Whether the ivent is favorited by the current user", "example": true}, "favoriteCount": {"type": "integer", "nullable": true, "description": "Number of favorites the ivent has", "example": 10, "minimum": 0}, "googleFormsUrl": {"type": "string", "nullable": true, "description": "URL to Google Forms for registration", "example": "https://forms.google.com/...", "format": "url"}, "instagramUsername": {"type": "string", "nullable": true, "description": "Instagram username for the ivent", "example": "my_ivent_account"}, "whatsappUrl": {"type": "string", "nullable": true, "description": "WhatsApp group URL", "example": "https://chat.whatsapp.com/...", "format": "url"}, "whatsappNumber": {"type": "string", "nullable": true, "description": "WhatsApp contact number", "example": "+90(555)1234567"}, "isWhatsappUrlPrivate": {"type": "boolean", "nullable": true, "description": "Whether the WhatsApp URL should be kept private", "example": false}, "callNumber": {"type": "string", "nullable": true, "description": "Phone number for calls", "example": "+90(555)1234567"}, "websiteUrl": {"type": "string", "nullable": true, "description": "Website URL for the ivent", "example": "https://myivent.com", "format": "url"}, "viewType": {"example": "default", "$ref": "#/components/schemas/IventViewTypeEnum"}}, "required": ["iventId", "iventName", "locationId", "mapboxId", "locationName", "dates", "categoryTag", "tagNames", "creatorId", "creatorType", "creatorUsername", "collabNames", "collabCount", "memberAvatarUrls", "memberCount", "viewType"]}, "GetBannerByIventIdDto": {"type": "object", "properties": {"iventIds": {"description": "Array of ivent UUIDs to get banner information for", "example": ["123e4567-e89b-12d3-a456-************"], "type": "array", "items": {"type": "string", "format": "uuid"}}}, "required": ["iventIds"]}, "GetBannerByIventIdReturn": {"type": "object", "properties": {"ivents": {"description": "Array of ivent card items for banner display", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/IventCardItem"}}, "iventCount": {"type": "integer", "description": "Total number of ivents in the banner", "example": 0, "minimum": 0}}, "required": ["ivents", "iventCount"]}, "UpdateDateByIventIdDto": {"type": "object", "properties": {"newDates": {"description": "Array of new date strings in date-time format, in ISO 8601 date-time format", "example": ["2024-08-31T22:00:00Z", "2024-09-01T14:00:00Z"], "type": "array", "items": {"type": "string"}}}, "required": ["newDates"]}, "UpdateDetailsByIventIdDto": {"type": "object", "properties": {"newDescription": {"type": "string", "description": "New description for the ivent", "example": "Updated description with new information"}}, "required": ["newDescription"]}, "UpdateLocationByIventIdDto": {"type": "object", "properties": {"newlocationId": {"type": "string", "description": "UUID of the new location", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["newlocationId"]}, "LocationItem": {"type": "object", "properties": {"locationId": {"type": "string", "format": "uuid", "description": "UUID of the location", "example": "123e4567-e89b-12d3-a456-************"}, "mapboxId": {"type": "string", "description": "Mapbox place identifier", "example": "address.**********"}, "locationName": {"type": "string", "description": "Name of the location", "example": "Central Park"}, "openAddress": {"type": "string", "description": "Detailed address of the location", "example": "5th Avenue and 59th Street, Manhattan, New York"}, "latitude": {"type": "double", "description": "Latitude coordinate of the location", "example": 40.7766, "minimum": -90, "maximum": 90}, "longitude": {"type": "double", "description": "Longitude coordinate of the location", "example": -73.9712, "minimum": -180, "maximum": 180}, "state": {"type": "string", "description": "State where the location is situated", "example": "New York"}}, "required": ["locationId", "mapboxId", "locationName", "openAddress", "latitude", "longitude", "state"]}, "GetLocationsReturn": {"type": "object", "properties": {"locations": {"description": "List of locations", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/LocationItem"}}, "locationCount": {"type": "integer", "description": "Total number of locations", "example": 0, "minimum": 0}}, "required": ["locations", "locationCount"]}, "GetLatestLocationsReturn": {"type": "object", "properties": {"locations": {"description": "List of latest locations", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/LocationItem"}}, "locationCount": {"type": "integer", "description": "Total number of latest locations", "example": 0, "minimum": 0}}, "required": ["locations", "locationCount"]}, "SearchBoxContextRegion": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "description": "Region ID", "example": "dXJuOm1ieHBsYzpBZ0lC"}, "name": {"type": "string", "nullable": true, "description": "Region name", "example": "Istanbul"}, "region_code": {"type": "string", "nullable": true, "description": "Region code", "example": "34"}, "region_code_full": {"type": "string", "nullable": true, "description": "Full region code", "example": "TR-34"}}}, "SearchBoxContextPostcode": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "description": "Postcode ID", "example": "dXJuOm1ieHBsYzpBZ0lC"}, "name": {"type": "string", "nullable": true, "description": "Postcode name", "example": "34000"}}}, "SearchBoxContextDistrict": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "description": "District ID", "example": "dXJuOm1ieHBsYzpBZ0lC"}, "name": {"type": "string", "nullable": true, "description": "District name", "example": "Bey<PERSON>ğ<PERSON>"}}}, "SearchBoxContextPlace": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "description": "Place ID", "example": "dXJuOm1ieHBsYzpBZ0lC"}, "name": {"type": "string", "nullable": true, "description": "Place name", "example": "Istanbul"}}}, "SearchBoxContextLocality": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "description": "Locality ID", "example": "dXJuOm1ieHBsYzpBZ0lC"}, "name": {"type": "string", "nullable": true, "description": "Locality name", "example": "Galata"}}}, "SearchBoxContextNeighborhood": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "description": "Neighborhood ID", "example": "dXJuOm1ieHBsYzpBZ0lC"}, "name": {"type": "string", "nullable": true, "description": "Neighborhood name", "example": "Karaköy"}}}, "SearchBoxContextAddress": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "description": "Address ID", "example": "dXJuOm1ieHBsYzpBZ0lC"}, "name": {"type": "string", "nullable": true, "description": "Address name", "example": "123 Main Street"}, "address_number": {"type": "string", "nullable": true, "description": "Address number", "example": "123"}, "street_name": {"type": "string", "nullable": true, "description": "Street name", "example": "Main Street"}}}, "SearchBoxContextStreet": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "description": "Street ID", "example": "dXJuOm1ieHBsYzpBZ0lC"}, "name": {"type": "string", "nullable": true, "description": "Street name", "example": "Main Street"}}}, "SearchBoxContext": {"type": "object", "properties": {"region": {"nullable": true, "description": "Region context information", "allOf": [{"$ref": "#/components/schemas/SearchBoxContextRegion"}]}, "postcode": {"nullable": true, "description": "Postcode context information", "allOf": [{"$ref": "#/components/schemas/SearchBoxContextPostcode"}]}, "district": {"nullable": true, "description": "District context information", "allOf": [{"$ref": "#/components/schemas/SearchBoxContextDistrict"}]}, "place": {"nullable": true, "description": "Place context information", "allOf": [{"$ref": "#/components/schemas/SearchBoxContextPlace"}]}, "locality": {"nullable": true, "description": "Locality context information", "allOf": [{"$ref": "#/components/schemas/SearchBoxContextLocality"}]}, "neighborhood": {"nullable": true, "description": "Neighborhood context information", "allOf": [{"$ref": "#/components/schemas/SearchBoxContextNeighborhood"}]}, "address": {"nullable": true, "description": "Address context information", "allOf": [{"$ref": "#/components/schemas/SearchBoxContextAddress"}]}, "street": {"nullable": true, "description": "Street context information", "allOf": [{"$ref": "#/components/schemas/SearchBoxContextStreet"}]}}}, "SearchBoxSuggestFeature": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the place", "example": "Starbucks"}, "name_preferred": {"type": "string", "nullable": true, "description": "Preferred name of the place", "example": "Starbucks Coffee"}, "mapbox_id": {"type": "string", "description": "Mapbox ID of the place", "example": "dXJuOm1ieHBvaTo0ZDk1YjJkNy05YzQyLTQ4YjMtOGI4Zi1lNzJlNzc2YzI4YzI"}, "feature_type": {"type": "string", "description": "Feature type", "example": "poi"}, "address": {"type": "string", "nullable": true, "description": "Address of the place", "example": "123 Main Street"}, "full_address": {"type": "string", "nullable": true, "description": "Full address of the place", "example": "123 Main Street, Beyoğlu, Istanbul, Turkey"}, "place_formatted": {"type": "string", "description": "Formatted place description", "example": "Starbucks, Beyoğlu, Istanbul"}, "context": {"description": "Context information about the place", "allOf": [{"$ref": "#/components/schemas/SearchBoxContext"}]}, "language": {"type": "string", "description": "Language of the result", "example": "en"}, "maki": {"type": "string", "nullable": true, "description": "Maki icon identifier", "example": "cafe"}, "poi_category": {"nullable": true, "description": "POI categories", "example": ["food_and_drink", "cafe"], "type": "array", "items": {"type": "string"}}, "poi_category_ids": {"nullable": true, "description": "POI category IDs", "example": ["food_and_drink_cafe"], "type": "array", "items": {"type": "string"}}, "brand": {"nullable": true, "description": "Brand names", "example": ["Starbucks"], "type": "array", "items": {"type": "string"}}, "brand_id": {"nullable": true, "description": "Brand IDs", "example": ["starbucks-c8b9b4"], "type": "array", "items": {"type": "string"}}, "external_ids": {"type": "object", "nullable": true, "description": "External IDs mapping", "example": {"foursquare": "4b123456f964a520123456e3"}}, "metadata": {"type": "object", "nullable": true, "description": "Additional metadata", "example": {"phone": "+**********", "website": "https://starbucks.com"}}, "distance": {"type": "integer", "nullable": true, "description": "Distance in meters", "example": 150, "minimum": 0}, "eta": {"type": "double", "nullable": true, "description": "Estimated time of arrival in minutes", "example": 5.2, "minimum": 0}, "added_distance": {"type": "double", "nullable": true, "description": "Added distance in meters", "example": 200.5, "minimum": 0}, "added_time": {"type": "double", "nullable": true, "description": "Added time in minutes", "example": 3.1, "minimum": 0}}, "required": ["name", "mapbox_id", "feature_type", "place_formatted", "context", "language"]}, "SearchBoxSuggestReturn": {"type": "object", "properties": {"suggestions": {"description": "List of suggested places", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/SearchBoxSuggestFeature"}}, "attribution": {"type": "string", "description": "Attribution text", "example": "© 2024 Mapbox, © OpenStreetMap"}}, "required": ["suggestions", "attribution"]}, "SearchBoxGeometry": {"type": "object", "properties": {"type": {"type": "string", "description": "Geometry type", "example": "Point"}, "coordinates": {"description": "Coordinates array [longitude, latitude]", "example": [28.9784, 41.0082], "type": "array", "items": {"type": "double"}}}, "required": ["type", "coordinates"]}, "SearchBoxRoutablePoints": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the routable point", "example": "main_entrance"}, "latitude": {"type": "double", "description": "Latitude coordinate", "example": 41.0082}, "longitude": {"type": "double", "description": "Longitude coordinate", "example": 28.9784}}, "required": ["name", "latitude", "longitude"]}, "SearchBoxCoordinates": {"type": "object", "properties": {"longitude": {"type": "double", "description": "Longitude coordinate", "example": 28.9784}, "latitude": {"type": "double", "description": "Latitude coordinate", "example": 41.0082}, "accuracy": {"type": "string", "nullable": true, "description": "Accuracy of the coordinates", "example": "rooftop"}, "routable_points": {"nullable": true, "description": "Routable points for navigation", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/SearchBoxRoutablePoints"}}}, "required": ["longitude", "latitude"]}, "SearchBoxProperties": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the place", "example": "Starbucks"}, "name_preferred": {"type": "string", "nullable": true, "description": "Preferred name of the place", "example": "Starbucks Coffee"}, "mapbox_id": {"type": "string", "description": "Mapbox ID of the place", "example": "dXJuOm1ieHBvaTo0ZDk1YjJkNy05YzQyLTQ4YjMtOGI4Zi1lNzJlNzc2YzI4YzI"}, "feature_type": {"type": "string", "description": "Feature type", "example": "poi"}, "address": {"type": "string", "nullable": true, "description": "Address of the place", "example": "123 Main Street"}, "full_address": {"type": "string", "nullable": true, "description": "Full address of the place", "example": "123 Main Street, Beyoğlu, Istanbul, Turkey"}, "place_formatted": {"type": "string", "description": "Formatted place description", "example": "Starbucks, Beyoğlu, Istanbul"}, "context": {"description": "Context information about the place", "allOf": [{"$ref": "#/components/schemas/SearchBoxContext"}]}, "language": {"type": "string", "description": "Language of the result", "example": "en"}, "maki": {"type": "string", "nullable": true, "description": "Maki icon identifier", "example": "cafe"}, "poi_category": {"nullable": true, "description": "POI categories", "example": ["food_and_drink", "cafe"], "type": "array", "items": {"type": "string"}}, "poi_category_ids": {"nullable": true, "description": "POI category IDs", "example": ["food_and_drink_cafe"], "type": "array", "items": {"type": "string"}}, "brand": {"nullable": true, "description": "Brand names", "example": ["Starbucks"], "type": "array", "items": {"type": "string"}}, "brand_id": {"nullable": true, "description": "Brand IDs", "example": ["starbucks-c8b9b4"], "type": "array", "items": {"type": "string"}}, "external_ids": {"type": "object", "nullable": true, "description": "External IDs mapping", "example": {"foursquare": "4b123456f964a520123456e3"}}, "metadata": {"type": "object", "nullable": true, "description": "Additional metadata", "example": {"phone": "+**********", "website": "https://starbucks.com"}}, "coordinates": {"description": "Coordinates of the place", "allOf": [{"$ref": "#/components/schemas/SearchBoxCoordinates"}]}, "bbox": {"nullable": true, "description": "Bounding box coordinates [minX, minY, maxX, maxY]", "example": [28.978, 41.008, 28.9788, 41.0084], "type": "array", "items": {"type": "double"}}}, "required": ["name", "mapbox_id", "feature_type", "place_formatted", "context", "language", "coordinates"]}, "SearchBoxFeature": {"type": "object", "properties": {"type": {"type": "string", "description": "Feature type", "example": "Feature"}, "geometry": {"description": "Geometry of the feature", "allOf": [{"$ref": "#/components/schemas/SearchBoxGeometry"}]}, "properties": {"description": "Properties of the feature", "allOf": [{"$ref": "#/components/schemas/SearchBoxProperties"}]}}, "required": ["type", "geometry", "properties"]}, "SearchBoxRetrieveReturn": {"type": "object", "properties": {"features": {"description": "List of retrieved features", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/SearchBoxFeature"}}, "attribution": {"type": "string", "description": "Attribution text", "example": "© 2024 Mapbox, © OpenStreetMap"}}, "required": ["features", "attribution"]}, "SearchBoxForwardReturn": {"type": "object", "properties": {"type": {"type": "string", "description": "Collection type", "example": "FeatureCollection"}, "features": {"description": "List of features found", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/SearchBoxFeature"}}, "attribution": {"type": "string", "description": "Attribution text", "example": "© 2024 Mapbox, © OpenStreetMap"}}, "required": ["type", "features", "attribution"]}, "SearchBoxCategoryReturn": {"type": "object", "properties": {"type": {"type": "string", "description": "Collection type", "example": "FeatureCollection"}, "features": {"description": "List of features in the category", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/SearchBoxFeature"}}, "attribution": {"type": "string", "description": "Attribution text", "example": "© 2024 Mapbox, © OpenStreetMap"}}, "required": ["type", "features", "attribution"]}, "SearchBoxCategoryItem": {"type": "object", "properties": {"canonical_id": {"type": "string", "description": "Canonical category ID", "example": "food_and_drink"}, "icon": {"type": "string", "description": "Maki icon identifier", "example": "cafe"}, "name": {"type": "string", "description": "Category name", "example": "Food and Drink"}}, "required": ["canonical_id", "icon", "name"]}, "SearchBoxCategoryListReturn": {"type": "object", "properties": {"list_items": {"description": "List of available categories", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/SearchBoxCategoryItem"}}, "attribution": {"type": "string", "description": "Attribution text", "example": "© 2024 Mapbox, © OpenStreetMap"}, "version": {"type": "string", "description": "Version of the API", "example": "1.0.0"}}, "required": ["list_items", "attribution", "version"]}, "SearchBoxReverseReturn": {"type": "object", "properties": {"type": {"type": "string", "description": "Collection type", "example": "FeatureCollection"}, "features": {"description": "List of features found at the location", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/SearchBoxFeature"}}, "attribution": {"type": "string", "description": "Attribution text", "example": "© 2024 Mapbox, © OpenStreetMap"}}, "required": ["type", "features", "attribution"]}, "MediaFormatEnum": {"type": "string", "description": "Format of the media", "enum": ["image", "video"]}, "CreateMemoryDto": {"type": "object", "properties": {"mediaFormat": {"$ref": "#/components/schemas/MediaFormatEnum"}, "caption": {"type": "string", "nullable": true, "description": "Caption or description for the memory", "example": "Amazing sunset at the beach! 🌅", "maxLength": 500}, "squadId": {"type": "string"}}, "required": ["mediaFormat", "squadId"]}, "CreateMemoryReturn": {"type": "object", "properties": {"memoryId": {"type": "string"}}, "required": ["memoryId"]}, "MemoryOriginEnum": {"type": "string", "enum": ["create_new", "list_folder"]}, "GetMemoryByMemoryIdReturn": {"type": "object", "properties": {"memoryId": {"type": "string", "description": "UUID of the memory", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "mediaUrl": {"type": "string", "description": "URL to the memory media", "example": "https://example.com/memory.jpg", "format": "url"}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the memory thumbnail", "example": "https://example.com/thumbnail.jpg", "format": "url"}, "caption": {"type": "string", "description": "Caption of the memory", "example": "This is a memory"}, "creatorId": {"type": "string", "description": "UUID of the memory creator", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "creatorUsername": {"type": "string", "description": "<PERSON><PERSON><PERSON> of the memory creator", "example": "john_doe"}, "creatorAvatarUrl": {"type": "string", "nullable": true, "description": "URL to the memory creator image", "example": "https://example.com/creator-image.jpg", "format": "url"}, "iventId": {"type": "string", "description": "UUID of the ivent", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "iventName": {"type": "string", "description": "Name of the ivent", "example": "My Awesome Ivent"}, "dates": {"nullable": true, "description": "List of dates for the ivent in ISO 8601 date-time format", "example": ["2024-08-31T22:00:00Z", "2024-09-01T14:00:00Z"], "type": "array", "items": {"type": "string"}}, "memberCount": {"type": "integer", "nullable": true, "description": "Number of members in the ivent", "example": 10, "minimum": 0}, "memberFirstnames": {"nullable": true, "description": "List of member's first names in the ivent", "example": ["<PERSON>", "<PERSON>"], "type": "array", "items": {"type": "string"}}}, "required": ["memoryId", "mediaUrl", "caption", "creatorId", "creatorUsername", "iventId", "iventName"]}, "NotificationTypeEnum": {"type": "string", "description": "Type of notification", "enum": ["arkadaslik_istegi_gonderdi", "artik_a<PERSON><PERSON><PERSON><PERSON>", "arkadaslik_istegi_onayladi", "uyelik_istegi_gonderdi", "uye<PERSON>_istegi_kabul_etti", "say<PERSON><PERSON>_takip_etti", "iventine_vibe_e<PERSON>i", "<PERSON><PERSON><PERSON>_<PERSON><PERSON>_e<PERSON>i", "uyelik_daveti", "say<PERSON><PERSON>_uye_oldu", "vibeina_medya_e<PERSON>i", "i<PERSON>e_davet_etti", "i<PERSON>_da<PERSON><PERSON>_kabul_etti", "vibe<PERSON>_begendi", "uygu<PERSON><PERSON>_katildi", "ivent_bilgilerini_guncelledi", "ivent_iptal", "whatsapp_grubuna_katilma_istegi_gonderdi", "whatsapp_grubuna_katil<PERSON>_istegi_on<PERSON><PERSON>i", "ivente_paydas_olarak_ekleme_istegi", "ivente_paydaslik_istegi_kabul_edildi", "i<PERSON><PERSON>_x_kisi_favoriledi", "i<PERSON><PERSON>_katildi", "i<PERSON>_yayin<PERSON>i", "ivente_x_adet_memory_ekledi", "arka<PERSON>_gru<PERSON>na_e<PERSON>i", "vibe_eklemek_icin_sonuc_saat", "vibe_e<PERSON><PERSON><PERSON>_icin_yirmi_dort_saat", "ivent_yaklasiyor", "ivent_creator_b<PERSON><PERSON><PERSON><PERSON>_on<PERSON><PERSON>i", "ivent_creator_bas<PERSON><PERSON><PERSON>_redded<PERSON>di", "<PERSON><PERSON>_bas<PERSON>runuz_onaylandi", "say<PERSON>_basvurunuz_reddedildi", "vibe_yorum_yapti", "<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "vibeiniza_eklediginiz_medyayi_gizlediniz", "vibeiniza_eklediginiz_medyayi_eklediniz", "whatsapp_grubuna_katilma_istegi_on<PERSON><PERSON><PERSON>z", "ivente_paydas_oldunuz", "ile_i<PERSON>e_ka<PERSON><PERSON><PERSON>", "ivente_katiliyor"]}, "NotificationItem": {"type": "object", "properties": {"notificationType": {"example": "arkadaslik_istegi_gonderdi", "$ref": "#/components/schemas/NotificationTypeEnum"}, "notificationId": {"type": "string", "description": "Unique identifier of the notification", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "createdAt": {"type": "string", "description": "Timestamp when the notification was created, in ISO 8601 date-time format", "example": "2023-12-01T10:30:00Z"}, "accountType": {"example": "user", "$ref": "#/components/schemas/AccountTypeEnum"}, "accountId": {"type": "string", "description": "UUID of the account that triggered the notification", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "accountUsername": {"type": "string", "description": "<PERSON><PERSON><PERSON> of the account that triggered the notification", "example": "irem_bas<PERSON>lu"}, "accountAvatarUrl": {"type": "string", "nullable": true, "description": "Avatar URL of the account that triggered the notification", "example": "https://example.com/avatar.jpg", "format": "url"}, "contentType": {"type": "string", "description": "Type of content related to the notification", "example": "vibe"}, "contentId": {"type": "string", "nullable": true, "description": "UUID of the content related to the notification", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "contentThumbnailUrl": {"type": "string", "nullable": true, "description": "Thumbnail URL of the content", "example": "https://example.com/thumbnail.jpg", "format": "url"}, "contentName": {"type": "string", "description": "Name or title of the content", "example": "Teoman Concert Vibe"}, "contentItem": {"type": "string", "description": "Description of the content item", "example": "3 Memories"}, "actionType": {"type": "string", "description": "Type of action that triggered the notification", "example": "memory_added"}, "actionId": {"type": "string", "description": "UUID of the item to navigate to when notification is tapped", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["notificationType", "notificationId", "createdAt", "accountType", "accountId", "accountUsername", "contentType", "contentName", "contentItem", "actionType", "actionId"]}, "GetNotificationsReturn": {"type": "object", "properties": {"notifications": {"description": "List of user notifications", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/NotificationItem"}}, "notificationCount": {"type": "integer", "description": "Total number of notifications", "example": 15, "minimum": 0}}, "required": ["notifications", "notificationCount"]}, "NotificationReplyTypeEnum": {"type": "string", "enum": ["accept", "reject"]}, "SearchPageBlocklistByPageIdReturn": {"type": "object", "properties": {"users": {"description": "List of users who are blocked by this page", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItem"}}, "userCount": {"type": "integer", "description": "Total number of blocked users", "example": 0, "minimum": 0}}, "required": ["users", "userCount"]}, "BlockUserByPageIdDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the user to block", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["userId"]}, "UnblockUserByPageIdDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the user to unblock", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["userId"]}, "SearchModeratorsForPageCreationReturn": {"type": "object", "properties": {"users": {"description": "List of users available for page creation", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItem"}}, "userCount": {"type": "integer", "description": "Total number of users available for page creation", "example": 15, "minimum": 0}}, "required": ["users", "userCount"]}, "RemovePageModeratorByPageIdDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the moderator to be removed", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["userId"]}, "TransferPageAdministrationByPageIdDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the user to transfer page administration to", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["userId"]}, "SearchPageMembersByPageIdReturn": {"type": "object", "properties": {"users": {"description": "List of page members", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItem"}}, "userCount": {"type": "integer", "description": "Total number of page members", "example": 20, "minimum": 0}}, "required": ["users", "userCount"]}, "SearchUsersToAddByPageIdReturn": {"type": "object", "properties": {"users": {"description": "List of users available to be added as page members", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItem"}}, "userCount": {"type": "integer", "description": "Total number of users available to be added as page members", "example": 10, "minimum": 0}}, "required": ["users", "userCount"]}, "UserListItemWithPageRole": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the user", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "username": {"type": "string", "description": "Username of the user", "example": "john_doe"}, "avatarUrl": {"type": "string", "nullable": true, "description": "URL to the user's avatar image", "example": "https://example.com/avatar.jpg", "format": "url"}, "university": {"type": "string", "nullable": true, "description": "Name of the user's university", "example": "Boğaziçi University"}, "pageMembershipStatus": {"example": "admin", "$ref": "#/components/schemas/PageMembershipStatusEnum"}}, "required": ["userId", "username", "pageMembershipStatus"]}, "SearchAdministrationByPageIdReturn": {"type": "object", "properties": {"users": {"description": "List of page administrators and moderators", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItemWithPageRole"}}, "userCount": {"type": "integer", "description": "Total number of page administrators and moderators", "example": 3, "minimum": 0}}, "required": ["users", "userCount"]}, "AddPageMembersByPageIdDto": {"type": "object", "properties": {"userIds": {"description": "Array of user UUIDs to add as page members", "example": ["123e4567-e89b-12d3-a456-************"], "type": "array", "items": {"type": "string", "format": "uuid"}}}, "required": ["userIds"]}, "RemovePageMemberByPageIdDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the member to be removed", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["userId"]}, "CreatePageDto": {"type": "object", "properties": {"pageName": {"type": "string", "description": "Name of the page", "example": "Photography Club Istanbul", "minLength": 3, "maxLength": 100}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the page thumbnail image", "example": "https://example.com/page-thumbnail.jpg", "format": "url"}, "websiteUrl": {"type": "string", "nullable": true, "description": "Website URL for the page", "example": "https://photographyclub.com", "format": "url"}, "description": {"type": "string", "nullable": true, "description": "Description of the page", "example": "A community for photography enthusiasts in Istanbul"}, "isEdu": {"type": "boolean", "description": "Whether this page is educational", "example": false}, "haveMembership": {"type": "boolean", "description": "Whether this page has membership functionality", "example": true}, "tagIds": {"description": "Array of hobby tag UUIDs associated with the page", "example": ["123e4567-e89b-12d3-a456-************"], "type": "array", "items": {"type": "string", "format": "uuid"}}, "creatorIds": {"description": "Array of creator user UUIDs for the page", "example": ["123e4567-e89b-12d3-a456-************"], "type": "array", "items": {"type": "string", "format": "uuid"}}, "locationId": {"type": "string", "description": "UUID of the location where the page is based", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["pageName", "isEdu", "haveMembership", "tagIds", "creatorIds", "locationId"]}, "CreatePageReturn": {"type": "object", "properties": {"pageId": {"type": "string", "description": "UUID of the newly created page", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["pageId"]}, "GetPageByPageIdReturn": {"type": "object", "properties": {"pageId": {"type": "string", "description": "UUID of the page", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "pageName": {"type": "string", "description": "Name of the page", "example": "Photography Club Istanbul"}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the page thumbnail image", "example": "https://example.com/page-thumbnail.jpg", "format": "url"}, "createdIventCount": {"type": "integer", "description": "Number of ivents created by this page", "example": 5, "minimum": 0}, "followerCount": {"type": "integer", "description": "Number of followers of this page", "example": 150, "minimum": 0}, "tagIds": {"description": "Hobby tag IDs associated with the page", "example": ["123e4567-e89b-12d3-a456-************"], "type": "array", "items": {"type": "string", "format": "uuid"}}, "haveMembership": {"type": "boolean", "description": "Whether this page has membership functionality", "example": true}, "isFirstPerson": {"type": "boolean", "description": "Whether the current user is the owner/creator of this page", "example": false}}, "required": ["pageId", "pageName", "createdIventCount", "followerCount", "tagIds", "haveMembership", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "GetIventsCreatedByPageIdReturn": {"type": "object", "properties": {"ivents": {"description": "List of ivents created by this page", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/IventCardItem"}}, "iventCount": {"type": "integer", "description": "Total number of ivents created by this page", "example": 0, "minimum": 0}}, "required": ["ivents", "iventCount"]}, "VibeFolderCardItem": {"type": "object", "properties": {"vibeFolderId": {"type": "string", "description": "UUID of the vibe folder", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the vibe folder thumbnail image", "example": "https://example.com/thumbnail.jpg", "format": "url"}, "iventId": {"type": "string", "description": "UUID of the ivent", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "iventName": {"type": "string", "description": "Name of the ivent", "example": "My Awesome Ivent"}, "vibeId": {"type": "string", "description": "UUID of the latest vibe in the vibe folder", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "memberCount": {"type": "integer", "description": "Number of members in the ivent", "example": 10, "minimum": 0}, "memberFirstnames": {"description": "List of member's first names in the ivent", "example": ["<PERSON>", "<PERSON>"], "type": "array", "items": {"type": "string"}}}, "required": ["vibeFolderId", "iventId", "iventName", "vibeId", "memberCount", "memberFirstnames"]}, "GetVibeFoldersByPageIdReturn": {"type": "object", "properties": {"vibeFolders": {"description": "List of vibe folders associated with this page", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/VibeFolderCardItem"}}, "vibeFolderCount": {"type": "integer", "description": "Total number of vibe folders", "example": 0, "minimum": 0}}, "required": ["vibeFolders", "vibeFolderCount"]}, "GetPageDetailsByPageIdReturn": {"type": "object", "properties": {"description": {"type": "string", "nullable": true, "description": "Description of the page", "example": "A community for photography enthusiasts in Istanbul"}, "websiteUrl": {"type": "string", "nullable": true, "description": "Website URL for the page", "example": "https://photographyclub.com", "format": "url"}, "locationId": {"type": "string", "description": "UUID of the location where the page is based", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "locationAdress": {"type": "string", "nullable": true, "description": "Address of the page location", "example": "Taksim Square, Istanbul, Turkey"}}, "required": ["locationId"]}, "UpdateDescriptionByPageIdDto": {"type": "object", "properties": {"newDescription": {"type": "string", "description": "New description for the page", "example": "Updated description with new information about our photography community"}}, "required": ["newDescription"]}, "UpdateLinksByPageIdDto": {"type": "object", "properties": {"newLink": {"type": "string", "description": "New website link for the page", "example": "https://newwebsite.com", "format": "url"}}, "required": ["newLink"]}, "UpdateLocationByPageIdDto": {"type": "object", "properties": {"newLocationId": {"type": "string", "description": "UUID of the new location for the page", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["newLocationId"]}, "SearchFollowersByPageIdReturn": {"type": "object", "properties": {"users": {"description": "List of users who follow this page", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItem"}}, "userCount": {"type": "integer", "description": "Total number of followers", "example": 0, "minimum": 0}}, "required": ["users", "userCount"]}, "RemoveFollowerByPageIdDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the user to remove as a follower", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["userId"]}, "FriendListingTypeEnum": {"type": "string", "enum": ["user", "group"]}, "GroupListItem": {"type": "object", "properties": {"groupId": {"type": "string", "description": "UUID of the group", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "groupName": {"type": "string", "description": "Name of the group", "example": "Photography Enthusiasts"}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the group thumbnail image", "example": "https://example.com/group-thumbnail.jpg", "format": "url"}, "memberFirstnames": {"description": "List of member's first names in the group", "example": ["<PERSON>", "<PERSON>"], "type": "array", "items": {"type": "string"}}, "memberCount": {"type": "integer", "description": "Number of members in the group", "example": 5, "minimum": 0}}, "required": ["groupId", "groupName", "memberFirstnames", "memberCount"]}, "SearchInvitableUsersByIventIdReturn": {"type": "object", "properties": {"groups": {"description": "List of groups that can be invited to the ivent", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/GroupListItem"}}, "groupCount": {"type": "integer", "description": "Total number of invitable groups", "example": 0, "minimum": 0}, "friends": {"description": "List of users that can be invited to the ivent", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItem"}}, "friendCount": {"type": "integer", "description": "Total number of invitable users", "example": 0, "minimum": 0}}, "required": ["groups", "groupCount", "friends", "friendCount"]}, "UserListItemWithRelationshipStatus": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the user", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "username": {"type": "string", "description": "Username of the user", "example": "john_doe"}, "avatarUrl": {"type": "string", "nullable": true, "description": "URL to the user's avatar image", "example": "https://example.com/avatar.jpg", "format": "url"}, "university": {"type": "string", "nullable": true, "description": "Name of the user's university", "example": "Boğaziçi University"}, "relationshipStatus": {"example": "accepted", "$ref": "#/components/schemas/UserRelationshipStatusEnum"}}, "required": ["userId", "username", "relationshipStatus"]}, "SearchParticipantsByIventIdReturn": {"type": "object", "properties": {"users": {"description": "List of users with their relationship status", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItemWithRelationshipStatus"}}, "userCount": {"type": "integer", "description": "Total number of users", "example": 0, "minimum": 0}, "viewType": {"example": "default", "$ref": "#/components/schemas/IventViewTypeEnum"}}, "required": ["users", "userCount", "viewType"]}, "JoinIventAndCreateSquadByIventIdDto": {"type": "object", "properties": {"groupIds": {"description": "Array of group UUIDs to invite to the ivent", "example": ["123e4567-e89b-12d3-a456-************"], "type": "array", "items": {"type": "string", "format": "uuid"}}, "userIds": {"description": "Array of user UUIDs to invite to the ivent", "example": ["123e4567-e89b-12d3-a456-************"], "type": "array", "items": {"type": "string", "format": "uuid"}}}, "required": ["groupIds", "userIds"]}, "InviteFriendsByIventIdDto": {"type": "object", "properties": {"groupIds": {"description": "Array of group UUIDs to invite to the ivent", "example": ["123e4567-e89b-12d3-a456-************"], "type": "array", "items": {"type": "string", "format": "uuid"}}, "userIds": {"description": "Array of user UUIDs to invite to the ivent", "example": ["123e4567-e89b-12d3-a456-************"], "type": "array", "items": {"type": "string", "format": "uuid"}}}, "required": ["groupIds", "userIds"]}, "UniversityItem": {"type": "object", "properties": {"universityName": {"type": "string", "description": "Name of the university", "example": "Boğaziçi University"}, "universityImageUrl": {"type": "string", "nullable": true, "description": "URL to the university image", "example": "https://example.com/university.jpg", "format": "url"}, "universityLocationState": {"type": "string", "nullable": true, "description": "State where the university is located", "example": "Istanbul"}}, "required": ["universityName"]}, "SearchUniversitiesReturn": {"type": "object", "properties": {"universities": {"description": "List of universities", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UniversityItem"}}, "universityCount": {"type": "integer", "description": "Total number of universities", "example": 0, "minimum": 0}}, "required": ["universities", "universityCount"]}, "GetUserBlocklistReturn": {"type": "object", "properties": {"users": {"description": "List of users who are blocked by this user", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItem"}}, "userCount": {"type": "integer", "description": "Total number of blocked users", "example": 0, "minimum": 0}}, "required": ["users", "userCount"]}, "SearchFriendsByUserIdReturn": {"type": "object", "properties": {"groups": {"description": "List of groups", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/GroupListItem"}}, "groupCount": {"type": "integer", "description": "Total number of groups", "example": 0, "minimum": 0}, "friends": {"description": "List of friends", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItem"}}, "friendCount": {"type": "integer", "description": "Total number of friends", "example": 0, "minimum": 0}}, "required": ["groups", "groupCount", "friends", "friendCount"]}, "RegisterDto": {"type": "object", "properties": {"phoneNumber": {"type": "string", "example": "+90(500)4003020", "description": "Phone number in international format with country code"}, "fullname": {"type": "string", "description": "User's full name can only contain letters and spaces", "example": "<PERSON>", "minLength": 2, "maxLength": 50}, "hobbyIds": {"description": "Array of hobby UUIDs that the user is interested in", "example": ["123e4567-e89b-12d3-a456-************"], "type": "array", "items": {"type": "string", "format": "uuid"}}}, "required": ["phoneNumber", "fullname", "hobbyIds"]}, "RegisterReturn": {"type": "object", "properties": {"userId": {"type": "string", "description": "Unique identifier of the newly registered user", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "token": {"type": "string", "description": "JWT authentication token for the new user", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "role": {"example": "level_0", "$ref": "#/components/schemas/UserRoleEnum"}, "username": {"type": "string", "description": "Username of the new user", "example": "john_doe"}, "fullname": {"type": "string", "description": "Full name of the new user", "example": "<PERSON>"}, "avatarUrl": {"type": "string", "nullable": true, "description": "URL to the user's avatar image", "example": "https://example.com/avatar.jpg", "format": "url"}}, "required": ["userId", "token", "role", "username", "fullname"]}, "GetUserByUserIdReturn": {"type": "object", "properties": {"userId": {"type": "string", "description": "Unique identifier of the user", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "userRole": {"example": "level_0", "$ref": "#/components/schemas/UserRoleEnum"}, "username": {"type": "string", "description": "Username of the user", "example": "john_doe"}, "fullname": {"type": "string", "description": "Full name of the user", "example": "<PERSON>"}, "avatarUrl": {"type": "string", "nullable": true, "description": "URL to the user's avatar image", "example": "https://example.com/avatar.jpg", "format": "url"}, "iventCount": {"type": "integer", "description": "Number of ivents created by the user", "example": 5, "minimum": 0}, "friendCount": {"type": "integer", "description": "Number of friends the user has", "example": 10, "minimum": 0}, "followerCount": {"type": "integer", "description": "Number of followers the user has", "example": 15, "minimum": 0}, "hobbies": {"description": "List of user's hobbies", "example": ["Photography", "Travel"], "type": "array", "items": {"type": "string"}}, "isFollowing": {"type": "boolean", "description": "Whether the current user is following this user", "example": false}, "isFirstPerson": {"type": "boolean", "description": "Whether this is the current user's own profile", "example": false}, "relationshipStatus": {"nullable": true, "example": "accepted", "$ref": "#/components/schemas/UserRelationshipStatusEnum"}}, "required": ["userId", "userRole", "username", "fullname", "iventCount", "friendCount", "followerCount", "hobbies", "isFollowing", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "GetContactsByUserIdDto": {"type": "object", "properties": {"phoneNumbers": {"example": ["+90(500)4003020"], "description": "Array of phone numbers to check for contacts", "type": "array", "items": {"type": "string"}}}, "required": ["phoneNumbers"]}, "UserListItemWithPhoneNumber": {"type": "object", "properties": {"userId": {"type": "string", "description": "UUID of the user", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "username": {"type": "string", "description": "Username of the user", "example": "john_doe"}, "avatarUrl": {"type": "string", "nullable": true, "description": "URL to the user's avatar image", "example": "https://example.com/avatar.jpg", "format": "url"}, "university": {"type": "string", "nullable": true, "description": "Name of the user's university", "example": "Boğaziçi University"}, "phoneNumber": {"type": "string", "description": "Phone number of the user", "example": "+90(500)4003020"}}, "required": ["userId", "username", "phoneNumber"]}, "GetContactsByUserIdReturn": {"type": "object", "properties": {"contacts": {"description": "List of user contacts with their phone numbers", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItemWithPhoneNumber"}}, "contactCount": {"type": "integer", "description": "Total number of contacts found", "example": 0, "minimum": 0}}, "required": ["contacts", "contactCount"]}, "IventListItemWithIsFavorited": {"type": "object", "properties": {"iventId": {"type": "string", "description": "UUID of the ivent", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "iventName": {"type": "string", "description": "Name of the ivent", "example": "My Awesome Ivent"}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the ivent thumbnail image", "example": "https://example.com/thumbnail.jpg", "format": "url"}, "locationName": {"type": "string", "description": "Name of the ivent location", "example": "Central Park"}, "dates": {"description": "List of dates for the ivent in ISO 8601 date-time format", "example": ["2024-08-31T22:00:00Z", "2024-09-01T14:00:00Z"], "type": "array", "items": {"type": "string"}}, "creatorId": {"type": "string", "description": "UUID of the ivent creator", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "creatorType": {"example": "user", "$ref": "#/components/schemas/IventCreatorTypeEnum"}, "creatorUsername": {"type": "string", "description": "<PERSON><PERSON><PERSON> of the ivent creator", "example": "john_doe"}, "creatorImageUrl": {"type": "string", "nullable": true, "description": "URL to the ivent creator image", "example": "https://example.com/creator-image.jpg", "format": "url"}, "memberCount": {"type": "integer", "description": "Number of members in the ivent", "example": 10, "minimum": 0}, "memberFirstnames": {"description": "List of member's first names in the ivent", "example": ["<PERSON>", "<PERSON>"], "type": "array", "items": {"type": "string"}}, "memberAvatarUrls": {"type": "array", "items": {"type": "string", "nullable": true}, "format": "url", "description": "List of member avatar <PERSON><PERSON> in the ivent", "example": ["https://example.com/avatar1.jpg", "https://example.com/avatar2.jpg"]}, "viewType": {"example": "default", "$ref": "#/components/schemas/IventViewTypeEnum"}, "isFavorited": {"type": "boolean", "description": "Whether the ivent is favorited by the current user", "example": true}}, "required": ["iventId", "iventName", "locationName", "dates", "creatorId", "creatorType", "creatorUsername", "memberCount", "memberFirstnames", "memberAvatarUrls", "viewType", "isFavorited"]}, "GetFavoritesByUserIdReturn": {"type": "object", "properties": {"ivents": {"description": "List of user's favorite ivents", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/IventListItemWithIsFavorited"}}, "iventCount": {"type": "integer", "description": "Total number of favorite ivents", "example": 0, "minimum": 0}}, "required": ["ivents", "iventCount"]}, "GetFollowingsByUserIdReturn": {"type": "object", "properties": {"followings": {"description": "List of users being followed", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItemWithRelationshipStatus"}}, "followingCount": {"type": "integer", "description": "Total number of users being followed", "example": 0, "minimum": 0}}, "required": ["followings", "followingCount"]}, "IventListingTypeEnum": {"type": "string", "enum": ["joined", "created"]}, "GetIventsByUserIdReturn": {"type": "object", "properties": {"ivents": {"description": "List of ivents created by the user", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/IventListItem"}}, "iventCount": {"type": "integer", "description": "Total number of ivents created by the user", "example": 0, "minimum": 0}, "isFirstPerson": {"type": "boolean", "description": "Whether this is the current user's own profile", "example": false}}, "required": ["ivents", "iventCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "GetLevelByUserIdReturn": {"type": "object", "properties": {"levelInfo": {"example": "level_0", "$ref": "#/components/schemas/UserRoleEnum"}}, "required": ["levelInfo"]}, "SideMenuPageItem": {"type": "object", "properties": {"pageId": {"type": "string", "description": "UUID of the page", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "pageName": {"type": "string", "description": "Name of the page", "example": "Photography Club Istanbul"}, "pageMembershipStatus": {"example": "admin", "$ref": "#/components/schemas/PageMembershipStatusEnum"}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the page thumbnail image", "example": "https://example.com/page-thumbnail.jpg", "format": "url"}}, "required": ["pageId", "pageName", "pageMembershipStatus"]}, "GetPagesByUserIdReturn": {"type": "object", "properties": {"pages": {"description": "List of pages the user is associated with", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/SideMenuPageItem"}}, "pageCount": {"type": "integer", "description": "Total number of pages", "example": 0, "minimum": 0}}, "required": ["pages", "pageCount"]}, "MemoryFolderCardItem": {"type": "object", "properties": {"memoryFolderId": {"type": "string", "description": "UUID of the memory folder", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the memory folder thumbnail image", "example": "https://example.com/thumbnail.jpg", "format": "url"}, "iventId": {"type": "string", "description": "UUID of the ivent", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "iventName": {"type": "string", "description": "Name of the ivent", "example": "My Awesome Ivent"}, "dates": {"description": "List of dates for the ivent in ISO 8601 date-time format", "example": ["2024-08-31T22:00:00Z", "2024-09-01T14:00:00Z"], "type": "array", "items": {"type": "string"}}, "memberCount": {"type": "integer", "description": "Number of members in the ivent", "example": 10, "minimum": 0}, "memberFirstnames": {"description": "List of member's first names in the ivent", "example": ["<PERSON>", "<PERSON>"], "type": "array", "items": {"type": "string"}}, "createdAt": {"type": "string", "nullable": true, "description": "Date of creation of the memory folder in ISO 8601 date-time format", "example": "2024-08-31T22:00:00Z"}}, "required": ["memoryFolderId", "iventId", "iventName", "dates", "memberCount", "memberFirstnames"]}, "GetMemoryFoldersByUserIdReturn": {"type": "object", "properties": {"memoryFolders": {"description": "List of user's memory folders", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/MemoryFolderCardItem"}}, "memoryFolderCount": {"type": "integer", "description": "Total number of memory folders", "example": 0, "minimum": 0}}, "required": ["memoryFolders", "memoryFolderCount"]}, "GetVibeFoldersByUserIdReturn": {"type": "object", "properties": {"vibeFolders": {"description": "List of user's vibe folders", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/VibeFolderCardItem"}}, "vibeFolderCount": {"type": "integer", "description": "Total number of vibe folders", "example": 0, "minimum": 0}}, "required": ["vibeFolders", "vibeFolderCount"]}, "UserGenderEnum": {"type": "string", "description": "Gender of the user", "enum": ["male", "female", "non-binary", "other", "prefer_not_to_say"]}, "UpdateByUserIdDto": {"type": "object", "properties": {"newUsername": {"type": "string", "description": "Username can only contain letters, numbers, underscores, and hyphens", "example": "john_doe123", "minLength": 4, "maxLength": 20}, "newBirthday": {"type": "string", "description": "Birthday, in ISO 8601 date-time format", "example": "2023-12-01T10:30:00Z"}, "newGender": {"example": "male", "$ref": "#/components/schemas/UserGenderEnum"}, "newAvatarUrl": {"type": "string", "description": "URL to the user's avatar image", "example": "https://example.com/avatar.jpg", "format": "url"}}, "required": ["newUsername", "newBirthday", "newGender", "newAvatarUrl"]}, "UpdateEmailByUserIdDto": {"type": "object", "properties": {"newEmail": {"type": "string", "description": "New email address for the user", "example": "<EMAIL>", "format": "email"}}, "required": ["newEmail"]}, "UserEduVerificationEnum": {"type": "string", "description": "User education verification status", "enum": ["unverified", "student", "grad"]}, "UpdateGradByUserIdDto": {"type": "object", "properties": {"newGrad": {"example": "student", "$ref": "#/components/schemas/UserEduVerificationEnum"}}, "required": ["newGrad"]}, "UpdateNotificationsByUserIdDto": {"type": "object", "properties": {}}, "UpdatePhoneNumberByUserIdDto": {"type": "object", "properties": {"newPhoneNumber": {"type": "string", "example": "+90(500)4003020", "description": "New phone number in international format with country code"}}, "required": ["newPhoneNumber"]}, "GetFollowersByUserIdReturn": {"type": "object", "properties": {"friendUsernames": {"description": "List of friend usernames", "example": [], "type": "array", "items": {"type": "string"}}, "friendCount": {"type": "integer", "description": "Total number of friends", "example": 2, "minimum": 0}, "followers": {"description": "List of followers with their relationship status", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItemWithRelationshipStatus"}}, "followerCount": {"type": "integer", "description": "Total number of followers", "example": 0, "minimum": 0}, "isFirstPerson": {"type": "boolean", "description": "Whether this is the current user's own profile", "example": false}}, "required": ["friendUsernames", "friendCount", "followers", "followerCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "GetFollowerFriendsByUserIdReturn": {"type": "object", "properties": {"friends": {"description": "List of friends who are also followers", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItem"}}, "friendCount": {"type": "integer", "description": "Total number of follower friends", "example": 0, "minimum": 0}}, "required": ["friends", "friendCount"]}, "RemoveFollowerByUserIdDto": {"type": "object", "properties": {"followerId": {"type": "string", "description": "UUID of the follower to remove", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["followerId"]}, "GetUserBannerByUserIdReturn": {"type": "object", "properties": {"userId": {"type": "string", "description": "Unique identifier of the user", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "username": {"type": "string", "description": "Username of the user", "example": "john_doe"}, "avatarUrl": {"type": "string", "nullable": true, "description": "URL to the user's avatar image", "example": "https://example.com/avatar.jpg", "format": "url"}, "fullname": {"type": "string", "description": "Full name of the user", "example": "<PERSON>"}}, "required": ["userId", "username", "fullname"]}, "VibePrivacyEnum": {"type": "string", "description": "Privacy setting for the vibe", "enum": ["private", "friends", "friends_of_friends", "public"]}, "CreateVibeDto": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "Media file (image or video). Supported formats: JPG, JPEG, PNG, GIF, MP4, MOV, AVI. Max size: 50MB"}, "caption": {"type": "string", "nullable": true, "description": "Caption or description for the vibe", "example": "Amazing sunset at the beach! 🌅", "maxLength": 500}, "vibeFolderId": {"type": "string", "description": "UUID of the folder this vibe belongs to", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "privacy": {"example": "public", "$ref": "#/components/schemas/VibePrivacyEnum"}}, "required": ["file", "vibeFolderId", "privacy"]}, "CreateVibeReturn": {"type": "object", "properties": {"vibeId": {"type": "string", "description": "UUID of the newly created vibe", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}}, "required": ["vibeId"]}, "VibeItem": {"type": "object", "properties": {"vibeId": {"type": "string", "description": "UUID of the vibe", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "vibeFolderId": {"type": "string", "description": "UUID of the vibe folder", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "mediaUrl": {"type": "string", "description": "URL to the vibe media", "example": "https://example.com/vibe.jpg", "format": "url"}, "mediaFormat": {"example": "image", "$ref": "#/components/schemas/MediaFormatEnum"}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the vibe thumbnail", "example": "https://example.com/thumbnail.jpg", "format": "url"}, "caption": {"type": "string", "description": "Caption of the vibe", "example": "This is a vibe"}, "creatorId": {"type": "string", "description": "UUID of the creator", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "creatorType": {"example": "user", "$ref": "#/components/schemas/AccountTypeEnum"}, "creatorUsername": {"type": "string", "description": "<PERSON><PERSON><PERSON> of the creator", "example": "john_doe"}, "creatorAvatarUrl": {"type": "string", "nullable": true, "description": "URL to the creator image", "example": "https://example.com/creator-image.jpg", "format": "url"}, "iventId": {"type": "string", "description": "UUID of the ivent", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "iventName": {"type": "string", "description": "Name of the ivent", "example": "My Awesome Ivent"}, "dates": {"description": "List of dates for the ivent, in ISO 8601 date-time format", "example": ["2024-08-31T22:00:00Z", "2024-09-01T14:00:00Z"], "type": "array", "items": {"type": "string"}}, "memberCount": {"type": "integer", "description": "Number of members in the ivent", "example": 10, "minimum": 0}, "memberFirstnames": {"description": "List of member's first names in the ivent", "example": ["<PERSON>", "<PERSON>"], "type": "array", "items": {"type": "string"}}, "likeCount": {"type": "integer", "description": "Number of likes on the vibe", "example": 10, "minimum": 0}, "commentCount": {"type": "integer", "description": "Number of comments on the vibe", "example": 5, "minimum": 0}, "nextVibeId": {"type": "string", "nullable": true, "description": "UUID of the next vibe in the vibe folder", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "previousVibeId": {"type": "string", "nullable": true, "description": "UUID of the previous vibe in the vibe folder", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "vibeIndex": {"type": "integer", "description": "Index of the vibe in the vibe folder", "example": 0, "minimum": 0}, "vibeCount": {"type": "integer", "description": "Total number of vibes in the vibe folder", "example": 10, "minimum": 0}, "createdAt": {"type": "string", "nullable": true, "description": "Date of creation of the vibe, in ISO 8601 date-time format", "example": "2024-08-31T22:00:00Z"}}, "required": ["vibeId", "vibeFolderId", "mediaUrl", "mediaFormat", "caption", "creatorId", "creatorType", "creatorUsername", "iventId", "iventName", "dates", "memberCount", "memberFirstnames", "likeCount", "commentCount", "vibeIndex", "vibeCount"]}, "GetVibesReturn": {"type": "object", "properties": {"vibes": {"description": "List of vibes", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/VibeItem"}}, "vibeCount": {"type": "integer", "description": "Total number of vibes", "example": 0, "minimum": 0}}, "required": ["vibes", "vibeCount"]}, "GetVibeByVibeIdReturn": {"type": "object", "properties": {"vibeId": {"type": "string", "description": "UUID of the vibe", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "vibeFolderId": {"type": "string", "description": "UUID of the vibe folder", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "mediaUrl": {"type": "string", "description": "URL to the vibe media", "example": "https://example.com/vibe.jpg", "format": "url"}, "mediaFormat": {"example": "image", "$ref": "#/components/schemas/MediaFormatEnum"}, "thumbnailUrl": {"type": "string", "nullable": true, "description": "URL to the vibe thumbnail", "example": "https://example.com/thumbnail.jpg", "format": "url"}, "caption": {"type": "string", "description": "Caption of the vibe", "example": "This is a vibe"}, "creatorId": {"type": "string", "description": "UUID of the creator", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "creatorType": {"example": "user", "$ref": "#/components/schemas/AccountTypeEnum"}, "creatorUsername": {"type": "string", "description": "<PERSON><PERSON><PERSON> of the creator", "example": "john_doe"}, "creatorAvatarUrl": {"type": "string", "nullable": true, "description": "URL to the creator image", "example": "https://example.com/creator-image.jpg", "format": "url"}, "iventId": {"type": "string", "description": "UUID of the ivent", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "iventName": {"type": "string", "description": "Name of the ivent", "example": "My Awesome Ivent"}, "dates": {"description": "List of dates for the ivent, in ISO 8601 date-time format", "example": ["2024-08-31T22:00:00Z", "2024-09-01T14:00:00Z"], "type": "array", "items": {"type": "string"}}, "memberCount": {"type": "integer", "description": "Number of members in the ivent", "example": 10, "minimum": 0}, "memberFirstnames": {"description": "List of member's first names in the ivent", "example": ["<PERSON>", "<PERSON>"], "type": "array", "items": {"type": "string"}}, "likeCount": {"type": "integer", "description": "Number of likes on the vibe", "example": 10, "minimum": 0}, "commentCount": {"type": "integer", "description": "Number of comments on the vibe", "example": 5, "minimum": 0}, "nextVibeId": {"type": "string", "nullable": true, "description": "UUID of the next vibe in the vibe folder", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "previousVibeId": {"type": "string", "nullable": true, "description": "UUID of the previous vibe in the vibe folder", "example": "123e4567-e89b-12d3-a456-************", "format": "uuid"}, "vibeIndex": {"type": "integer", "description": "Index of the vibe in the vibe folder", "example": 0, "minimum": 0}, "vibeCount": {"type": "integer", "description": "Total number of vibes in the vibe folder", "example": 10, "minimum": 0}, "createdAt": {"type": "string", "nullable": true, "description": "Date of creation of the vibe, in ISO 8601 date-time format", "example": "2024-08-31T22:00:00Z"}}, "required": ["vibeId", "vibeFolderId", "mediaUrl", "mediaFormat", "caption", "creatorId", "creatorType", "creatorUsername", "iventId", "iventName", "dates", "memberCount", "memberFirstnames", "likeCount", "commentCount", "vibeIndex", "vibeCount"]}, "CommentItem": {"type": "object", "properties": {"commentId": {"type": "string", "format": "uuid", "description": "UUID of the comment"}, "comment": {"type": "string", "description": "Text content of the comment"}, "commenterUserId": {"type": "string", "format": "uuid", "description": "UUID of the user who made the comment"}, "commenterUsername": {"type": "string", "description": "<PERSON><PERSON><PERSON> of the user who made the comment", "example": "john_doe"}, "createdAt": {"type": "string", "description": "Timestamp when the comment was created in ISO 8601 date-time format", "example": "2023-12-01T10:30:00Z"}}, "required": ["commentId", "comment", "commenterUser<PERSON>d", "commenterUsername", "createdAt"]}, "GetCommentsByVibeIdReturn": {"type": "object", "properties": {"comments": {"description": "List of comments on the vibe", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/CommentItem"}}, "commentCount": {"type": "integer", "description": "Total number of comments on the vibe", "example": 0, "minimum": 0}}, "required": ["comments", "commentCount"]}, "UpdateByVibeIdDto": {"type": "object", "properties": {"newCaption": {"type": "string", "description": "New caption for the vibe", "example": "Updated caption with new information", "maxLength": 500}}, "required": ["newCaption"]}, "GetLikesByVibeIdReturn": {"type": "object", "properties": {"likes": {"description": "List of users who liked the vibe", "example": [], "type": "array", "items": {"$ref": "#/components/schemas/UserListItemWithRelationshipStatus"}}, "likeCount": {"type": "integer", "description": "Total number of likes on the vibe", "example": 0, "minimum": 0}}, "required": ["likes", "likeCount"]}}}, "security": [{"JWT-auth": []}]}